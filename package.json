{"name": "ssnj-front", "version": "0.1.0", "private": true, "scripts": {"serve:dev": "vue-cli-service serve", "serve": "set VUE_APP_COMPANY=mingwork&& vue-cli-service serve --mode testing --open", "build": "export VUE_APP_COMPANY=mingwork && vue-cli-service build", "build:beta": "export VUE_APP_COMPANY=mingwork && vue-cli-service build --max_old_space_size=9000 --mode testing", "build:prod": "export VUE_APP_COMPANY=mingwork && vue-cli-service build --mode production", "upload": "node upload.js", "deploy:beta": "npm run build:beta && npm run upload", "upload:production": "node upload-production.js", "deploy:production": "npm run build && npm run upload:production", "lint": "vue-cli-service lint", "build:dev": "vue-cli-service build --mode development", "upload:dev": "node upload-dev.js", "deploy:dev": "npm run build:dev && npm run upload:dev", "serve:ssnj": "set VUE_APP_COMPANY=ssnj&& vue-cli-service serve --mode testing", "build:ssnj": "export VUE_APP_COMPANY=ssnj && vue-cli-service build", "build:ssnj:beta": "export VUE_APP_COMPANY=ssnj && vue-cli-service build --mode testing"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/icons-vue": "^6.0.1", "@better-scroll/core": "^2.4.2", "@better-scroll/mouse-wheel": "^2.4.2", "@better-scroll/pull-up": "^2.4.2", "@tailwindcss/postcss7-compat": "^2.2.17", "@tinymce/tinymce-vue": "^4.0.4", "ant-design-vue": "^2.2.8", "autofit.js": "^3.0.7", "autoprefixer": "^9.8.8", "axios": "^0.21.1", "base-64": "^1.0.0", "chalk": "^4.1.1", "clipboard": "^2.0.8", "core-js": "^3.33.2", "d3": "^3.5.17", "dayjs": "^1.10.5", "decimal.js": "^10.3.1", "dingtalk-jsapi": "^2.14.1", "echarts": "^5.4.3", "echarts-liquidfill": "^3.1.0", "element-plus": "2.6.1", "idb": "^8.0.1", "js-cookie": "^3.0.0", "less": "^4.1.3", "less-loader": "^7.3.0", "lodash": "^4.17.21", "moment": "^2.30.1", "ora": "^5.4.1", "pdfjs-dist": "^2.14.305", "pingpp-js": "^2.2.27", "postcss": "^7.0.39", "postcss-px-to-viewport": "^1.1.1", "scp2": "^0.5.0", "store": "^2.0.12", "svg-sprite-loader": "^6.0.11", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "v-charts": "^1.19.0", "vue": "^3.4.34", "vue-color-kit": "^1.0.6", "vue-countup-v3": "^1.4.0", "vue-i18n": "^11.0.1", "vue-qr": "^3.2.4", "vue-router": "4.0.11", "vue3-colorpicker": "^2.2.3", "vue3-tree-org": "^4.2.2", "vuedraggable": "^4.1.0", "vuex": "4.0.1", "weixin-js-sdk": "^1.6.5", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "webpack-aliyun-oss": "^0.3.12"}, "eslintConfig": {"root": true, "env": {"node": true}, "globals": {"defineProps": "readonly", "defineEmits": "readonly", "defineExpose": "readonly"}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"no-debugger": "off", "no-useless-escape": "off", "no-unused-vars": "off", "no-irregular-whitespace": "off"}}, "browserslist": ["> 1%", "last 2 versions"]}