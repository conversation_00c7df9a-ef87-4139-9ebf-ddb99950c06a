import api from "@/api";
import { Get, Post, Put, Delete } from "@/common/request";
function getNoticePage(param) {
  return Get({
    url: api.getNoticePage,
    urlParam: param,
  });
}
function getNoticeInfo(noticeId) {
  return Get({
    url: api.getNoticeInfo,
    pathParam: { noticeId },
  });
}
function readAll(param) {
  return Put({
    url: api.readAll,
    urlParam: param,
  });
}
function getUnReadNoticeCount(param) {
  return Get({
    url: api.getUnReadNoticeCount,
    urlParam: param,
  });
}
function getUnReadMessageList(param) {
  return Get({
    url: api.getUnReadMessageList,
    urlParam: param,
  });
}
function getAdvisoryDetail(advisoryId) {
  return Get({
    url: api.getAdvisoryDetail,
    pathParam: { advisoryId },
  });
}

export default {
  getNoticePage,
  getNoticeInfo,
  readAll,
  getUnReadNoticeCount,
  getUnReadMessageList,
  getAdvisoryDetail,
};
