import api from '@/api'
import { Get, Post } from '@/common/request'
function bindStationVehicle(obj) {
    return Post({
        url: api.bindStationVehicle,
        bodyParam: obj
    })
}
function updateStationInfoVehicle(obj) {
    return Post({
        url: api.updateStationInfoVehicle,
        bodyParam: obj
    })
}
function updateStationDevicesVehicle(obj) {
    return Post({
        url: api.updateStationDevicesVehicle,
        bodyParam: obj
    })
}
function getSupplierStationSummaryVehicle(param) {
    return Get({
        url: api.getSupplierStationSummaryVehicle,
        urlParam: param
    })
}
function statisticStationActiveDeviceCountVehicle(param) {
    return Get({
        url: api.statisticStationActiveDeviceCountVehicle,
        urlParam: param
    })
}
function getStationPageVehicle(obj) {
    return Post({
        url: api.getStationPageVehicle,
        bodyParam: obj
    })
}

function getStationInfoVehicle(param) {
    return Get({
        url: api.getStationInfoVehicle,
        urlParam: param
    })
}
function getStationStaticInfoVehicle(param) {
    return Get({
        url: api.getStationStaticInfoVehicle,
        urlParam: param
    })
}
function getDeviceListVehicle(param) {
    return Get({
        url: api.getDeviceListVehicle,
        urlParam: param
    })
}
function statisticStationDevicesStatusProportionVehicle(param) {
    return Get({
        url: api.statisticStationDevicesStatusProportionVehicle,
        urlParam: param
    })
}
function statisticStationDevicesSocProportion(param) {
    return Get({
        url: api.statisticStationDevicesSocProportion,
        urlParam: param
    })
}

function statisticsDeviceChargeDischargeRank(obj) {
    return Post({
        url: api.statisticsDeviceChargeDischargeRank,
        bodyParam: obj
    })
}
function getVehicleBmsInfo(param) {
    return Get({
        url: api.getVehicleBmsInfo,
        urlParam: param
    })
}

function statisticsDeviceDailyChargeAndProfit(obj) {
    return Post({
        url: api.statisticsDeviceDailyChargeAndProfit,
        bodyParam: obj
    })
}
function statisticsDeviceSocAndWorkStatusByDay(param) {
    return Get({
        url: api.statisticsDeviceSocAndWorkStatusByDay,
        urlParam: param
    })
}
function getVehiclePackList(param) {
    return Get({
        url: api.getVehiclePackList,
        urlParam: param
    })
}
function pushDeviceBatteryLockCommand(obj) {
    return Post({
        url: api.pushDeviceBatteryLockCommand,
        bodyParam: obj
    })
}

function getVehicleBmsDataLog(obj) {
    return Post({
        url: api.getVehicleBmsDataLog,
        bodyParam: obj
    })
}

export default {
    bindStationVehicle,
    updateStationInfoVehicle,
    updateStationDevicesVehicle,
    getSupplierStationSummaryVehicle,
    statisticStationActiveDeviceCountVehicle,
    getStationPageVehicle,
    getStationInfoVehicle,
    getStationStaticInfoVehicle,
    getDeviceListVehicle,
    statisticStationDevicesStatusProportionVehicle,
    statisticStationDevicesSocProportion,
    statisticsDeviceChargeDischargeRank,
    getVehicleBmsInfo,
    statisticsDeviceDailyChargeAndProfit,
    statisticsDeviceSocAndWorkStatusByDay,
    getVehiclePackList,
    pushDeviceBatteryLockCommand,
    getVehicleBmsDataLog,

}
