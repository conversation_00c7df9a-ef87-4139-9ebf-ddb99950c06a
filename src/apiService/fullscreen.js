import api from '@/api'
import { Get, Post } from '@/common/request'

export function getDeviceMetaData(param) {
    return Get({
        url: api.getStationBasicInfo,
        urlParam: param,
    })
}
export function getStationChargingAndProfit(param) {
    return Get({
        url: api.getStationChargingAndProfit,
        urlParam: param,
    })
}
export function getSocialContributionDegree(param) {
    return Get({
        url: api.getSocialContributionDegree,
        urlParam: param,
    })
}
export function getStationProfitRank(param) {
    return Get({
        url: api.getStationProfitRank,
        urlParam: param,
    })
}

export function getStationChargingAndProfitTrend(param) {
    return Get({
        url: api.getStationChargingAndProfitTrend,
        urlParam: param,
    })
}

export function getStationProfitTrend(param={}){
    return Get({
        url: api.getStationProfitTrend,
        urlParam: param,
    })
}

export function getStationChargeTrend(params={}){
    return Get({
        url: api.getStationChargeTrend,
        urlParam: params
    })
}

export default {
    getDeviceMetaData,
}
