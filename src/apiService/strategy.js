import api from '@/api'
import { Get, Post } from '@/common/request'
function getRunMode(param) {
  return Get({
      url: api.getRunMode,
      urlParam: param,
  })
}
function changeRunMode(params) {
  return Post({
      url: api.changeRunMode,
      bodyParam: params
  })
}
function changeStationRunMode(params) {
  return Post({
      url: api.changeStationRunMode,
      bodyParam: params,
      config:{
        timeout:300000
      }
  })
}

function getStrategyTemplateList(param) {
  return Get({
      url: api.getStrategyTemplateList,
      urlParam: param,
  })
}
function getStrategyTemplatePage(param) {
  return Post({
      url: api.getStrategyTemplatePage,
      bodyParam: param
  })
}

function getOrgAndSubOrgStationNameList(param) {
  return Get({
      url: api.getOrgAndSubOrgStationNameList,
      urlParam: param,
  })
}

function getStrategyTemplateDetail(param) {
  return Get({
      url: api.getStrategyTemplateDetail,
      urlParam: param,
  })
}
function addStrategyTemplate(params) {
  return Post({
      url: api.addStrategyTemplate,
      bodyParam: params
  })
}
function updateStrategyTemplate(params) {
  return Post({
      url: api.updateStrategyTemplate,
      bodyParam: params
  })
}
function deleteStrategyTemplate(params) {
  return Post({
      url: api.deleteStrategyTemplate,
      bodyParam: params
  })
}
function getStrategyStationList(param) {
  return Get({
      url: api.getStrategyStationList,
      urlParam: param,
  })
}
function previewStation(param) {
  return Get({
      url: api.previewStation,
      urlParam: param,
  })
}

function addStrategyStation(params) {
  return Post({
      url: api.addStrategyStation,
      bodyParam: params
  })
}
function authorizationStation(params) {
  return Post({
      url: api.authorizationStation,
      bodyParam: params
  })
}
function updateStation(params) {
  return Post({
      url: api.updateStation,
      bodyParam: params
  })
}
function deleteStation(params) {
  return Post({
      url: api.deleteStation,
      bodyParam: params
  })
}
function getAiRecommendStrategy(param) {
  return Get({
      url: api.getAiRecommendStrategy,
      urlParam: param,
  })
}
function getAiForecastPower(param) {
  return Get({
      url: api.getAiForecastPower,
      urlParam: param,
  })
}
function getElecPriceTemplateList(params) {
  return Post({
      url: api.getElecPriceTemplateList,
      bodyParam: params
  })
}
function getElecPriceTemplateDetail(param) {
  return Get({
      url: api.getElecPriceTemplateDetail,
      urlParam: param,
  })
}
function addElecPriceTemplate(params) {
  return Post({
      url: api.addElecPriceTemplate,
      bodyParam: params
  })
}
function updateElecPriceTemplate(params) {
  return Post({
      url: api.updateElecPriceTemplate,
      bodyParam: params
  })
}
function deleteElecPriceTemplate(params) {
  return Post({
      url: api.deleteElecPriceTemplate,
      bodyParam: params
  })
}
function getElecPriceStationList(param) {
  return Get({
      url: api.getElecPriceStationList,
      urlParam: param,
  })
}
function settingStationElectricPrice(params) {
  return Post({
      url: api.settingStationElectricPrice,
      bodyParam: params
  })
}
function uploadVPPElectricPrice(params) {
  return Post({
      url: api.uploadVPPElectricPrice,
      bodyParam: params
  })
}

function authorizationStationElectricPrice(params) {
  return Post({
      url: api.authorizationStationElectricPrice,
      bodyParam: params
  })
}
function getEmsDynamicEnvironmentSystem(param) {
  return Get({
      url: api.getEmsDynamicEnvironmentSystem,
      urlParam: param,
      config:{
        timeout:300000
      }
  })
}
function setDynamicEnvironmentSystem(params) {
  return Post({
      url: api.setDynamicEnvironmentSystem,
      bodyParam: params,
      config:{
        timeout:300000
      }
  })
}

export default {
  getRunMode,
  changeRunMode,
  changeStationRunMode,
  getStrategyTemplateList,
  getStrategyTemplatePage,
  getOrgAndSubOrgStationNameList,
  getStrategyTemplateDetail,
  addStrategyTemplate,
  updateStrategyTemplate,
  deleteStrategyTemplate,
  getStrategyStationList,
  previewStation,
  addStrategyStation,
  authorizationStation,
  updateStation,
  deleteStation,
  getAiRecommendStrategy,
  getAiForecastPower,
  getElecPriceTemplateList,
  getElecPriceTemplateDetail,
  addElecPriceTemplate,
  updateElecPriceTemplate,
  deleteElecPriceTemplate,
  getElecPriceStationList,
  settingStationElectricPrice,
  uploadVPPElectricPrice,
  authorizationStationElectricPrice,
  getEmsDynamicEnvironmentSystem,
  setDynamicEnvironmentSystem,
}