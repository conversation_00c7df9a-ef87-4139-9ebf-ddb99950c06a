import api from '@/api'
import { Get, Post } from '@/common/request'
function getVppStationInfo(param) {
  return Get({
      url: api.getVppStationInfo,
      urlParam: param,
  })
}
function openVppStation(params) {
  return Post({
      url: api.openVppStation,
      bodyParam: params
  })
}
function getRealStrategyAndAiRecommendStrategy(param) {
  return Get({
      url: api.getRealStrategyAndAiRecommendStrategy,
      urlParam: param,
  })
}

function getStationEmsBasicInfo(param) {
  return Get({
    url: api.getStationEmsBasicInfo,
    urlParam: param,
  })
}
function getVppDemandRecords(params) {
  return Post({
    url: api.getVppDemandRecords,
    bodyParam: params
  })
}

function statisticsDailyChargeAndProfitDetail(params) {
  return Post({
    url: api.statisticsDailyChargeAndProfitDetail,
    bodyParam: params
  })
}
function statisticsStationDateChargeAndNetProfit(params) {
  return Post({
    url: api.statisticsStationDateChargeAndNetProfit,
    bodyParam: params
  })
}

function openVppAiStrategy(params) {
  return Post({
    url: api.openVppAiStrategy,
    bodyParam: params
  })
}
function openVppDemand(params) {
  return Post({
    url: api.openVppDemand,
    bodyParam: params
  })
}
function getConfigEarningParams(param) {
  return Get({
    url: api.getConfigEarningParams,
    urlParam: param,
  })
}
function configEarningParams(params) {
  return Post({
    url: api.configEarningParams,
    bodyParam: params
  })
}
function getVppDemandTotalSettlement(param) {
  return Get({
    url: api.getVppDemandTotalSettlement,
    urlParam: param,
  })
}
function getOperationLogPage(params) {
  return Post({
    url: api.getOperationLogPage,
    bodyParam: params
  })
}
export default {
  getVppStationInfo,
  openVppStation,
  getRealStrategyAndAiRecommendStrategy,
  getStationEmsBasicInfo,
  getVppDemandRecords,
  statisticsDailyChargeAndProfitDetail,
  statisticsStationDateChargeAndNetProfit,
  openVppAiStrategy,
  openVppDemand,
  getConfigEarningParams,
  configEarningParams,
  getVppDemandTotalSettlement,
  getOperationLogPage
}