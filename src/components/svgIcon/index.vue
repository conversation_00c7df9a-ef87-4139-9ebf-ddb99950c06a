<template>
  <div class="icon-wrapper">
    <svg :class="svgClass" aria-hidden="true">
      <use :xlink:href="iconName"></use>
    </svg>
  </div>
</template>

<script>

// 定义一个加载目录的函数
const requireAll = requireContext => requireContext.keys().map(requireContext)
const req = require.context('@/assets/iconSvg', false, /\.svg$/)
// 加载目录下的所有的 svg 文件
requireAll(req)

export default {
  name: 'Icon',
  props: {
    name: String,
    prefix: {
      type: String,
      default: 'icon-'
    },
    className: {
      type: String,
      default: ''
    },
  },
  computed: {
    iconName() {
      return `#${this.prefix}${this.name}`
    },
    svgClass() {
      if (this.className) {
        return `icon  ${this.className}`
      } else {
        return 'icon'
      }
    }
  }
}
</script>

<style lang="less" scoped>
.icon-wrapper {
  display: inline-flex;
  text-align: center;
  justify-content: center;
}

.icon {
  width: 100%;
  height: 100%;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
  margin: auto;
}
</style>