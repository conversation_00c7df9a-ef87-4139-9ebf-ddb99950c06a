<template>
    <div>
        <div v-if="type === 'WECHAT'">
            <div class="mt-8">
                <div class="relative">
                    <div class="flex justify-center relative z-10">
                        <img :src="uri" alt="" width="200" />
                    </div>
                </div>
                <div class="px-3">
                    <div
                        class="text-center text-primary-text dark:text-80-dark mb-4"
                    >
                        {{ $t('WeChatScan') }}
                    </div>
                    <div
                        class="text-center text-primary-text dark:text-80-dark"
                    >
                        {{ $t('tips_bind_l') }}
                        {{ $t('WeChatMiniProgram') }}
                        {{ $t('tips_bind_r') }}
                    </div>
                </div>
            </div>
        </div>
        <div v-if="type === 'DING_TALK'">
            <div class="mt-8">
                <div class="relative">
                    <div class="flex justify-center relative z-10">
                        <vue-qr
                            :logoSrc="src2"
                            :text="url"
                            :callback="test"
                            :size="200"
                        ></vue-qr>
                    </div>
                </div>
                <div class="px-3">
                    <div
                        class="text-center text-primary-text dark:text-80-dark mb-4"
                    >
                        {{ $t('DingTalkScan') }}
                    </div>
                    <div
                        class="text-center text-primary-text dark:text-80-dark"
                    >
                        {{ $t('tips_bind_l') }}
                        {{ $t('DingTalkMiniProgram') }}
                        {{ $t('tips_bind_r') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            // src: 'https://www.baidu.com/img/bd_logo1.png',
            src2: require('@/assets/login/icon.png'),
            url: `dingtalk://dingtalkclient/action/open_micro_app?appId=129525&page=pages%2Fbind%2Fbinding%3FuserId%3D${this.$store.state.user.userInfoData.userId}`,
        }
    },

    props: {
        type: {
            type: String,
            default: '',
        },
        uri: {
            type: String,
            default: '',
        },
    },
    methods: {
        test() {},
    },
}
</script>

<style lang="less" scoped></style>
