<template>
    <el-dialog
        :model-value="visible"
        :width="dialogWidth"
        :before-close="handleCancel"
        @update:model-value="handleVisibleChange"
        center
        destroy-on-close
        class="as"
    >
        <div class="confirm-popover">
            <div class="pl-7 relative">
                <div class="icon absolute left-0 top-0.5">
                    <iconSvg
                        name="help"
                        class="icon-default w-5 h-5"
                        style="color: #fd750b"
                    />
                </div>
                <div class="confirm-title" v-if="title">{{ title }}</div>
                <div class="confirm-content" v-if="content">{{ content }}</div>
            </div>
        </div>

        <template #footer>
            <div class="confirm-actions">
                <el-button plain round @click="handleCancel" class="cancel-btn">
                    {{ cancelText || $t('common_fou') }}
                </el-button>
                <el-button
                    plain
                    round
                    type="primary"
                    @click="handleConfirm"
                    class="confirm-btn"
                >
                    {{ confirmText || $t('common_shi') }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { fa } from 'element-plus/es/locale/index.mjs'

// Vue 3 Composition API

const props = defineProps({
    title: {
        type: String,
        default: '',
    },
    content: {
        type: String,
        default: '',
    },
    visible: {
        type: Boolean,
        default: false,
    },
    dialogWidth: {
        type: [String, Number],
        default: '414px',
    },
    dialogTitle: {
        type: String,
        default: 'Tips',
    },
    confirmText: {
        type: String,
        default: '',
    },
    cancelText: {
        type: String,
        default: '',
    },
})

const emits = defineEmits(['confirm', 'cancel', 'update:visible'])

const handleConfirm = () => {
    emits('confirm')
    emits('update:visible', false)
}

const handleCancel = () => {
    emits('cancel')
    emits('update:visible', false)
}

const handleVisibleChange = (value) => {
    emits('update:visible', value)
}
</script>

<style lang="less" scoped>
.confirm-popover {
    padding: 0;

    .confirm-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;
        line-height: 24px;
        color: var(--text-100);
    }

    .confirm-content {
        font-size: 14px;
        margin-bottom: 0;
        line-height: 22px;
        word-break: break-word;
        color: var(--text-60);
    }
}

.confirm-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin: 0;
    padding: 0;
}

// 自定义 dialog 样式
:deep(.el-dialog) {
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

    .el-dialog__header {
        padding: 20px 24px 0;

        .el-dialog__title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-100);
        }
    }

    .el-dialog__body {
        padding: 20px 24px;
    }

    .el-dialog__footer {
        padding: 0 24px 24px;
    }
}
</style>
<style lang="less">
.el-dialog.as {
    padding: 12px 28px 28px;
    .el-dialog__body {
        margin-bottom: 8px;
    }
}
</style>
