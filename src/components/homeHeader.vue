<template>
    <div :style="{ backgroundColor: $route.meta.headerBg }">
        <a-layout-header v-if="isNotMapView">
            <div class="header-bg bg-ff dark:bg-ff-dark">
                <div
                    class="header mx-auto my-0 xxl:m-auto"
                    :class="isFullPage ? '' : 'w-content-area'"
                >
                    <!-- 左侧logo -->
                    <div class="relative leading-6 flex login-box">
                        <div class="logo cursor h-full float-right">
                            <img
                                class="h-full flex"
                                :src="loginImg?.orgLogo"
                                alt=""
                                @click="goHome"
                            />
                            <!-- <iconSvg name="furuishi" :className="'h-full-svg'"/> -->
                        </div>
                    </div>
                    <div
                        class="relative xxl:flex-1 flex justify-end image-boxs"
                    >
                        <div></div>
                        <div class="account">
                            <span
                                class="user-info"
                                v-if="!$store.getters['user/getUserInfoData']"
                                >{{ $t('login_weidenglu') }}</span
                            >
                            <div class="flex items-center">
                                <a-dropdown
                                    overlayClassName="header-account-dropdown user-info-dropdown"
                                    placement="bottomRight"
                                >
                                    <div
                                        class="welcome cursor flex items-center"
                                    >
                                        <div
                                            v-if="
                                                userInfo?.realName ||
                                                userInfo?.phone
                                            "
                                        >
                                            <div class="flex items-center">
                                                <div>
                                                    <div
                                                        class="text-title dark:text-title-dark flex items-center justify-end"
                                                    >
                                                        <span
                                                            class="align-middle user-info overflow"
                                                        >
                                                            hi，{{
                                                                userInfo?.realName
                                                            }}</span
                                                        >
                                                        <iconSvg
                                                            class="w-4 h-4 ml-2 dropIcon"
                                                            name="dropdown"
                                                        />
                                                        <!-- <img
                                                            src="@/assets/down.png"
                                                            class="w-4 h-4 ml-2"
                                                        /> -->
                                                    </div>
                                                    <div
                                                        class="overflow commonpy text-secondar-text dark:text-60-dark"
                                                    >
                                                        {{ userInfo?.orgName }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <template #overlay>
                                        <a-menu :class="locale">
                                            <a-menu-item @click="goUser">
                                                <span class="login-out-box">
                                                    {{ $t('Account') }}
                                                    <iconSvg
                                                        name="user"
                                                        :className="'login-out '"
                                                    />
                                                </span>
                                            </a-menu-item>
                                            <a-menu-item
                                                v-if="
                                                    isDemoUser &&
                                                    activeSystem == 'device'
                                                "
                                                @click="siteVisible = true"
                                            >
                                                <span class="login-out-box">
                                                    {{ $t('AddStation') }}
                                                    <iconSvg
                                                        name="addSn"
                                                        :className="'login-out'"
                                                    />
                                                </span>
                                            </a-menu-item>

                                            <a-menu-item
                                                @click="logout"
                                                v-if="!isInDingtalk"
                                            >
                                                <span class="login-out-box">
                                                    {{
                                                        $t('menu_anquantuichu')
                                                    }}
                                                    <iconSvg
                                                        name="exit"
                                                        :className="'login-out'"
                                                    />
                                                </span>
                                            </a-menu-item>
                                        </a-menu>
                                    </template>
                                </a-dropdown>
                            </div>
                        </div>
                        <div class="ml-4" v-if="businessType == 'all'">
                            <switchBtn />
                        </div>
                        <div class="notice-box" v-if="false">
                            <a-dropdown
                                overlayClassName="header-account-dropdown notice-box-dropdown"
                                placement="bottomRight"
                                v-model:visible="dropdownVisible"
                            >
                                <iconSvg
                                    name="tongzhi"
                                    class="tz"
                                    :className="'set-up'"
                                    :style="{
                                        color:
                                            staffNotificationList.length > 0
                                                ? 'red'
                                                : null,
                                    }"
                                />
                                <template #overlay>
                                    <a-menu>
                                        <div class="menu-box">
                                            <div
                                                class="title text-title dark:text-title-dark"
                                            >
                                                {{ $t('Alert Notification') }}
                                            </div>
                                            <div class="risk-content">
                                                <div
                                                    class="item-risk"
                                                    v-for="item in staffNotificationList"
                                                    :key="item.id"
                                                    @click="lookRisk(item)"
                                                >
                                                    <div class="item-risk-t">
                                                        <div
                                                            class="item-title text-third-text dark:text-40-dark"
                                                        >
                                                            {{ item.title }}
                                                        </div>
                                                        <div
                                                            class="item-arrow flex text-third-text dark:text-40-dark"
                                                        >
                                                            <iconSvg
                                                                name="jian"
                                                                :className="'margin0'"
                                                            />
                                                        </div>
                                                    </div>
                                                    <div class="item-risk-b">
                                                        <div
                                                            class="item-title-b text-title dark:text-title-dark"
                                                        >
                                                            {{ item.content }}
                                                        </div>
                                                        <div
                                                            class="item-time text-third-text dark:text-40-dark"
                                                        >
                                                            {{
                                                                item.createTime
                                                            }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div
                                                class="risk-empty"
                                                v-if="
                                                    staffNotificationList.length ==
                                                    0
                                                "
                                            >
                                                <a-empty>
                                                    <template #description>
                                                        <span
                                                            class="risk-empty-title"
                                                            >{{
                                                                $t(
                                                                    'alarm_zanwuyichang'
                                                                )
                                                            }}</span
                                                        >
                                                    </template>
                                                </a-empty>
                                            </div>
                                        </div>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </div>
                        <div
                            class="notice-box"
                            @click="handleToggle"
                            v-if="false"
                        >
                            <iconSvg
                                name="theme"
                                class="set"
                                :className="'set-up'"
                            />
                        </div>
                        <div class="notice-box" v-if="isShangshan">
                            <el-dropdown :trigger="['click']">
                                <span class="el-dropdown-link">
                                    <iconSvg
                                        name="language"
                                        class="set"
                                        :className="'set-up'"
                                    />
                                </span>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item
                                            v-for="item in languagesOptions"
                                            :key="item.value"
                                            @click="changeLanguage(item.value)"
                                            :class="
                                                currentLanguage == item.value
                                                    ? 'ment-active'
                                                    : ''
                                            "
                                            >{{ item.label }}</el-dropdown-item
                                        >
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>

                        <div class="notice-box" @click="openHelp" v-if="false">
                            <iconSvg
                                name="help"
                                class="help"
                                :className="'set-up'"
                            />
                        </div>
                        <div class="notice-box" @click="goRole">
                            <iconSvg
                                name="shezhi"
                                class="set"
                                :className="'set-up'"
                            />
                        </div>

                        <!-- <div class="notice-box" style="background: none">
                            <el-switch
                                v-model="isDark"
                                @change="changeTheme"
                                size="large"
                            >
                                <template #active-action>
                                    <span
                                        class="custom-active-action flex items-center"
                                        >🌙</span
                                    >
                                </template>
                                <template #inactive-action>
                                    <span
                                        class="custom-inactive-action flex items-center"
                                        >🌞</span
                                    >
                                </template>
                            </el-switch>
                        </div> -->
                    </div>
                </div>
            </div>
        </a-layout-header>

        <alarmOverviewDetailDrawer
            v-model:visible="noticeVisible"
            :alarmId="alarmId"
            @success="refresh"
        >
        </alarmOverviewDetailDrawer>
        <edit-info
            v-model:visible="siteVisible"
            @onClose="siteVisible = false"
            :isAddNew="true"
        />
        <el-drawer
            v-model="helpVisible"
            :size="486"
            :lockScroll="true"
            :show-close="false"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div class="drawer-header text-title dark:text-title-dark">
                        <span>{{ $t('User Guide') }}</span>
                    </div>
                    <div>
                        <el-button plain round @click="closeDrawer">{{
                            $t('common_guanbi')
                        }}</el-button>
                    </div>
                </div>
            </template>
            <div>
                <div class="rounded-lg bg-00A0B8 mb-5">
                    <div class="px-9 py-6 leading-6 help-text">
                        <div class="text-base leading-6">
                            上善能及能源云平台使用手册
                        </div>
                        <div class="flex items-center text-sm leading-6 mb-3">
                            新手快速入门｜管理操作教程｜常见问题解答
                        </div>
                        <div>
                            <div @click="goHelp" class="look-btn">立即查看</div>
                        </div>
                    </div>
                </div>
                <div class="text-primary-text dark:text-80-dark">
                    <div class="mb-3">更新说明</div>
                    <div
                        v-for="item in updateList"
                        :key="item.date"
                        style="background-color: rgba(34, 34, 34, 0.04)"
                        class="px-4 py-3 rounded-lg mb-3 text-sm leading-6"
                    >
                        <div class="text-center mb-1">{{ item.date }}</div>
                        <div v-if="item.newVersion.length">
                            <div class="text-base">🌟 全新功能</div>
                            <div
                                v-for="ite in item.newVersion"
                                :key="ite"
                                class="pl-5 flex items-start space-x-1"
                            >
                                <div>·</div>
                                <div>{{ ite }}</div>
                            </div>
                        </div>
                        <div v-if="item.optimize.length" class="mt-3">
                            <div class="text-base mb-1">💎 体验优化</div>
                            <div
                                v-for="ite in item.optimize"
                                :key="ite"
                                class="pl-5 flex items-start space-x-1"
                            >
                                <div>·</div>
                                <div>{{ ite }}</div>
                            </div>
                        </div>
                        <div v-if="item.fixVersion.length" class="mt-3">
                            <div class="text-base mb-1">🔧 问题修复</div>
                            <div
                                v-for="ite in item.fixVersion"
                                :key="ite"
                                class="pl-5 flex items-start space-x-1"
                            >
                                <div>·</div>
                                <div>{{ ite }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import {
    computed,
    onMounted,
    onBeforeMount,
    reactive,
    toRefs,
    ref,
    watch,
} from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
// import { enterpriseServiceDomain } from '@/config/env'
import { env } from 'dingtalk-jsapi'
import deviceService from '@/apiService/device'
import editInfo from '@/views/device/home/<USER>'
import alarmOverviewDetailDrawer from '@/views/device/alarmOverviewDetailDrawer.vue'
import { useI18n } from 'vue-i18n'
import useTheme from '@/common/useTheme'
import switchBtn from './switchBtn.vue'
export default {
    components: {
        editInfo,
        alarmOverviewDetailDrawer,
        switchBtn,
    },
    props: {
        isFullPage: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, { emit }) {
        const { isDark, toggleTheme } = useTheme()
        const { isFullPage } = toRefs(props)
        const isInDingtalk = computed(() => {
            return env.platform != 'notInDingTalk'
        })
        const store = useStore()
        const route = useRoute(),
            router = useRouter(),
            selectedKeys = ref(['/'])
        const userInfo = computed(() => {
            // return store.state.user.userInfo || {};
            return store.state.user.userInfoData || void 0
        })
        const activeSystem = ref(localStorage.getItem('activeSystem'))
        const businessType = computed(() => {
            return localStorage.getItem('businessType')
        })
        watch(
            () => localStorage.getItem('activeSystem'),
            (newVal, oldVal) => {
                activeSystem.value = localStorage.getItem('activeSystem')
            },
            { immediate: true, deep: true }
        )

        const loginImg = computed(() => {
            return store.getters['user/getConfigData']
        })

        const state = reactive({
            noticeVisible: false,
            alarmId: undefined,
        })
        const logout = async () => {
            const {
                data: { code },
            } = await deviceService.loginOut()
            if (code === 0) {
                store.commit('device/setSelectSupplierInfo', undefined)
                store.commit('car/setSelectSupplierInfoCar', undefined)
                store.commit('user/loginOut')
                router.push({ path: '/login' })
                window.location.reload()
            }
            //跳转到官网登录页
            // window.location.href = enterpriseServiceDomain + "/#/login";
        }

        const helpVisible = ref(false)
        const openHelp = () => {
            helpVisible.value = true
        }
        const goHelp = () => {
            window.open(
                'https://alidocs.dingtalk.com/i/p/Pl2AmoV5q68Xdb9o5pXBW9aPOQB9om7Z',
                '_blank'
            )
        }
        const goto = ({ item, key, keyPath }) => {
            router.push(`/${key}`)
        }
        watch(
            () => route.path,
            (val) => {
                activeSystem.value = localStorage.getItem('activeSystem')
                selectedKeys.value = [val.replace('/', '')]
            },
            { immediate: true }
        )
        const showDeviceBtn = computed(() => {
            return route.path == '/device'
        })
        const showPowerBtn = computed(() => {
            return route.path == '/vehicle'
        })
        const goSystem = (path) => {
            router.push(`/${path}`)
        }
        const dropdownVisible = ref(false)
        const siteVisible = ref(false)
        const siteRef = ref(null)
        const addLoading = ref(false)

        const staffNotificationList = ref([])
        // const getStaffNotificationPage = async () => {
        //     // const {
        //     //     data: {
        //     //         code,
        //     //         data: { records },
        //     //     },
        //     // } = await deviceService.getStaffNotificationPage()
        //     // if (code === 0) {
        //     //     staffNotificationList.value = records || []
        //     //     // staffNotificationList.value = [
        //     //     //     {
        //     //     //         title: '复兴能源站点 有一条新的紧急异常',
        //     //     //         content: '单体电压过压 1 级报警',
        //     //     //         createTime: '2024-01-11',
        //     //     //         id: '45',
        //     //     //     }
        //     //     // ]
        //     // }
        // }

        // getStaffNotificationPage()

        const readNotification = async (id) => {
            await deviceService.readNotification({ id })
        }

        const lookRisk = async (row) => {
            // router.push({
            //     name: 'alarmOverview',
            //     params: {
            //         id: row.bizId,
            //     },
            // })
            state.alarmId = row.bizId
            state.noticeVisible = true
            dropdownVisible.value = false
            await readNotification(row.id)
            // getStaffNotificationPage()
        }

        const goRole = () => {
            // router.push({ path: '/rolePage' })
            const origin = window.location.origin
            window.open(`${origin}/#/rolePage`)
        }
        const goUser = () => {
            const origin = window.location.origin
            window.open(`${origin}/#/user`)
        }
        onBeforeMount(async () => {})
        const { t, locale } = useI18n()
        onMounted(async () => {
            // getStaffNotificationPage()
        })

        const refresh = () => {
            // getStaffNotificationPage()
        }

        const isDemoUser = computed(() => {
            return (
                store.state.user.userInfoData &&
                store.state.user.userInfoData?.userId != '1101'
            )
        })
        const closeDrawer = () => {
            helpVisible.value = false
        }
        const updateList = ref([
            {
                date: '2024-09',
                newVersion: [
                    '站点基础信息中，丰富了场站相关的基础资源信息如场站负载、变压器容量',
                ],
                optimize: [
                    '异常板块全面更新，所有上报异常会有专人跟进处理，进度透明，结果清晰，保障设备安全稳定运行',
                ],
                fixVersion: [],
            },
            {
                date: '2024-08',
                newVersion: [
                    '首页站点列表新增树状视图类型，通过层级关系清晰地展示各储能站点的归属信息',
                    '新增数据导出功能，支持用户便捷查看&导出站点的核心历史数据，以便于进行进一步的分析、存档或报告生成',
                    '新增操作日志查询功能，用于记录和跟踪系统中的所有关键操作，为系统管理员提供了基础的安全审计能力',
                    '增加设备故障复位功能，快捷重置特定设备状态',
                    // '站点基础信息中，丰富了场站相关的基础资源信息如场站负载、变压器容量',
                ],
                optimize: [
                    // '异常板块全面更新，所有上报异常会有专人跟进处理，进度透明，结果清晰，保障设备安全稳定运行',
                    '图表日期选择模块优化，结合设备投运日期限定可选时段',
                    '异常页摘要图表跟随列表页动态更新',
                    '异常模块相关细节逻辑优化',
                ],
                fixVersion: ['修复了个别站点充放电状态展示异常问题'],
            },
            {
                date: '2024-07',
                newVersion: [
                    '增加虚拟电厂相关场景功能，支持设备接入并进行需求响应以提升整体效益',
                    '增加AI策略优化功能，通过AI算法可有效优化设备整体运行策略以提升电芯寿命',
                    '新增操作日志查询功能，用于记录和跟踪系统中的所有关键操作，为系统管理员提供了基础的安全审计能力',
                    '增加设备故障复位功能，快捷重置特定设备状态',
                ],
                optimize: [
                    '优化了当前执行策略展示内容的结构',
                    '优化了电价模版配置阶段的校验规则及提示文案',
                    '异常模块相关细节逻辑优化',
                ],
                fixVersion: [
                    '修复了部分图表在部分浏览器显示异常的问题',
                    '修复了下发给子站点的模版，有其他站点在使用时，无法正常删除的问题',
                ],
            },
            {
                date: '2024-06',
                newVersion: [
                    '充放电策略配置模块（仅适用于如下产品型号设备：SE215-1.3（型号待定））',
                    '电价管理策略与模版配置（仅适用于如下产品型号设备：SE215-1.3（型号待定））',
                    '动环管理相关硬件控制&参数配置（仅适用于如下产品型号设备：SE215-1.3（型号待定））',
                    '增加设备故障复位功能，快捷重置特定设备状态',
                ],
                optimize: [
                    '优化了当前执行策略展示内容的结构',
                    '优化了电价模版配置阶段的校验规则及提示文案',
                    '异常模块相关细节逻辑优化',
                ],
                fixVersion: [
                    '修复了部分离线站点设备状态显示异常的问题',
                    '修复了部分图表时间选择器选择时间后无法取消的问题',
                ],
            },
            {
                date: '2024-05',
                newVersion: [
                    '新增整体运行状态示意图，直观展示电网、负载、储能等侧的用电流向及详细功率数据',
                    '支持用户将系统账号与钉钉账号进行绑定，绑定后可通过钉钉小程序进行访问',
                    '站点基础信息支持自定义更新',
                    '增加设备故障复位功能，快捷重置特定设备状态',
                ],
                optimize: [
                    '整体布局和交互进行大幅优化',
                    '添加站点逻辑优化',
                    '提升站点初始化部署体验',
                ],
                fixVersion: [
                    '修复了部分浏览器下收益排行图表比例条显示异常的问题',
                ],
            },
        ])
        const cancel = () => {}

        const goHome = () => {
            if (activeSystem.value == 'car') {
                router.push({
                    path: '/vehicle',
                })
            } else {
                router.push({
                    path: '/device',
                })
            }
        }

        const isOperator = computed(() => {
            return store.state.user.userInfoData.roles.includes(
                'operation_staff'
            )
            // return false
        })
        const goOperation = () => {
            const origin = window.location.origin
            window.open(`${origin}/#/operation`)
        }
        const goOperationCar = () => {
            const origin = window.location.origin
            window.open(`${origin}/#/operation-vehicle`)
        }

        const supplierId = computed(() => {
            return (
                route?.query?.supplierId ||
                store.state.device.selectSupplierInfo?.id
            )
        })
        const goFull = () => {
            // router.push({ path: '/fullScreen/device' })
            const origin = window.location.origin
            window.open(
                `${origin}/#/fullScreen/device?supplierId=${supplierId.value}`
            )
        }

        const languagesOptions = [
            {
                label: '中文',
                value: 'zh',
            },
            {
                label: 'English',
                value: 'en',
            },
        ]
        const changeLanguage = (val) => {
            if (val == locale.value) {
                return
            }
            store.dispatch('lang/changeLanguage', val)
            // localStorage.setItem('language', val)
            window.location.reload()
        }
        const currentLanguage = computed(() => {
            return (
                localStorage.getItem('language') ||
                store.state.lang.currentLanguage ||
                'zh'
            )
        })
        const getCompanyInfo = computed(
            () => store.getters['user/getUserInfoData']
        )
        const isShangshan = computed(() => {
            return getCompanyInfo?.value?.orgId == '1726907974555729921'
        })

        const handleToggle = () => {
            // emit('onChangeTheme')

            toggleTheme()
        }
        const changeTheme = (e) => {
            //
        }
        const isNotMapView = computed(() => {
            return route.name != 'deviceOverview'
        })
        return {
            isNotMapView,
            ...toRefs(state),
            logout,
            userInfo,
            isInDingtalk,
            openHelp,
            goto,
            loginImg,
            siteVisible,
            siteRef,
            addLoading,
            staffNotificationList,
            lookRisk,
            goRole,
            goUser,
            dropdownVisible,
            refresh,
            isDemoUser,
            helpVisible,
            goHelp,
            closeDrawer,
            updateList,
            cancel,
            goHome,
            activeSystem,
            isOperator,
            goOperation,
            goOperationCar,
            goFull,
            languagesOptions,
            changeLanguage,
            t,
            currentLanguage,
            isShangshan,
            isDark,
            toggleTheme,
            handleToggle,
            changeTheme,
            businessType,
            showDeviceBtn,
            showPowerBtn,
            goSystem,
            locale,
        }
    },
}
</script>

<style lang="less" scoped>
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 68px;
    line-height: 68px;
    padding: 0 24px 0 30px;
    // backdrop-filter: blur(10px);
    &.w-content-area {
        width: 1440px;
        padding: 0;
    }

    @media screen and (min-width: 1921px) {
        &.w-content-area {
            width: 1600px;
        }
    }

    @media screen and (max-width: 1480px) {
        &.w-content-area {
            padding: 0 20px;
        }
    }
    // width: 100%;
    .menu {
        // width: 1200px;
        position: relative;
        height: 100%;
    }

    .login-box {
        height: 26px;

        :deep(.h-full-svg) {
            height: 32px;
            width: 156px;
        }
    }
}

@media screen and (min-width: 1921px) {
}

.ant-layout-header {
    height: auto;
    padding: 0;
    background: transparent;
    // height: 60px;
    // line-height: 60px;
    // background-color: @body-background;
}

.logo {
    vertical-align: middle;
    flex: 1;
    display: flex;
    align-items: center;
}

.welcome,
.phone {
    line-height: 22px;
}

.setting {
    .iconfont {
        color: theme('colors.primary-text');

        &.active {
            // color: @primary-color;
        }
    }
}

.account {
    display: flex;
    align-items: center;

    .login-name {
        width: 32px;
        height: 32px;
        background: #ea0c28;
        border-radius: 50%;
        line-height: 32px;
        text-align: center;
        font-size: 12px;
        color: #fff;
        margin-right: 8px;
    }

    .user-info {
        height: 22px;
        font-size: 14px;

        line-height: 22px;
    }

    .commonpy {
        height: 20px;
        font-size: 12px;

        line-height: 20px;
        max-width: 160px;
        text-align: right;
    }

    .text-sm-s {
        font-size: 14px;
        line-height: 20px;
    }
}

.bubble:after {
    content: '';
    position: absolute;
    top: 8px;
    width: 0;
    height: 0;
    border-width: 8px;
    border-style: solid;
    border-color: transparent;
    border-right-width: 8px;
    border-right-color: currentColor;
    color: #000;
    left: -13px;
}

.bubble {
    line-height: 32px;
    border-radius: 32px;
    padding: 0 20px;
    margin-left: 10px;
    position: relative;
    display: inline-block;
    // background: @primary-color;
    color: #fff;
}

.menu {
    &-item {
        display: block;
        height: 88px;
        line-height: 88px;

        &::after {
            display: block;
            content: '';
            width: 48px;
            height: 6px;
            background: transparent;
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            transition: width 0.5s ease;
        }

        .menu-item-icon,
        .menu-item-text {
            color: theme('colors.secondar-text');
        }

        &.active {
            &::after {
                width: 32px;
                // background: @primary-color;
            }

            .menu-item-icon,
            .menu-item-text {
                // color: @primary-color;
            }
        }
    }
}

.image-boxs {
    .notice-box {
        height: 32px;
        width: 32px;
        border-radius: 50%;
        background-color: #f5f7f7;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 16px;
        cursor: pointer;
        .tz {
            color: #808080;
        }
        &:hover {
            :deep(.tz) {
                color: red;
            }
        }

        .help {
            color: #808080;
        }

        &:hover {
            :deep(.help) {
                color: var(--themeColor);
                opacity: 1;
            }
        }

        .set {
            color: #808080;
        }

        &:hover {
            :deep(.set) {
                color: var(--themeColor);
            }
        }
        :deep(.el-dropdown) {
            outline: none;
            * {
                outline: none;
            }
        }
    }

    :deep(.set-up) {
        width: 20px;
        height: 20px;
        margin-right: 0;
    }
}

.toggle-btn {
    background: #f5f7f7;
    color: rgba(34, 34, 34, 0.8);
    user-select: none;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
        background: var(--themeColor);
        color: #fff;

        * {
            color: #fff;
            fill: #fff;
        }
    }
}
:deep(.el-switch__core .el-switch__action) {
    background-color: transparent !important;
}
:deep(.el-switch.is-checked) {
    background-color: transparent;
}
:deep(.el-switch.is-checked .el-switch__core) {
    background-color: transparent;
}
.dropIcon {
    color: var(--text-60);
}
.dark {
    .dropIcon {
        color: var(--text-100);
    }
}
</style>
<style lang="less">
.header-account-dropdown {
    .ant-dropdown-menu {
        margin-top: 14px;
        width: 130px !important;
        &.zh {
            .ant-dropdown-menu-item {
                text-align: center;
            }
        }
        &.en {
            .ant-dropdown-menu-item {
                text-align: right;
            }
        }
    }
    .ant-dropdown-menu-item,
    .ant-dropdown-menu-submenu-title {
        padding: 12px;
        line-height: 22px;
    }

    .ant-dropdown-menu-submenu-title > span > .anticon:first-child {
        font-size: 14px;
    }

    .ant-dropdown-menu-title-content {
        font-size: 14px;
    }

    .login-out {
        width: 16px;
        height: 16px;
        margin-left: 6px;
    }

    .login-out-box {
        display: inline-flex;
        align-items: center;
        justify-content: flex-start;
        text-align: left;
    }

    .margin0 {
        margin-right: 0;
        width: 18px;
        height: 18px;
    }
}

.notice-box-dropdown {
    z-index: 99999;
    .ant-dropdown-menu {
        width: 324px !important;
        padding-bottom: 0;
        background: #fff;
        .menu-box {
            .title {
                line-height: 52px;
                padding: 0 16px;
                border-bottom: 1px solid transparent;
                border-color: var(--border);
                font-size: 14px;
            }

            .risk-content {
                max-height: 500px;
                overflow-y: auto;

                &::-webkit-scrollbar {
                    /* Chrome Safari */
                    width: 10px;
                    /* 横向滚动条宽度为0 */
                    height: 15px;
                    /* 纵向滚动条高度为0 */
                }

                &::-webkit-scrollbar-thumb {
                    background-color: #d9d9d9;
                    border-radius: 5px;
                }

                &::-webkit-scrollbar-track {
                    background-color: #ccc;
                    border: 1px solid #ccc;
                    border-radius: 5px;
                }

                &::-webkit-scrollbar-button {
                    // background-color: #d9d9d9;
                    border-radius: 5px;
                }

                .item-risk {
                    padding: 16px;
                    border-bottom: 1px solid var(--border);
                    display: flex;
                    flex-direction: column;

                    cursor: pointer;

                    .item-risk-t,
                    .item-risk-b {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        .item-title {
                            font-size: 14px;
                        }

                        .item-title-b {
                            font-size: 14px;
                        }

                        .item-time {
                            font-size: 14px;
                        }

                        .item-arrow {
                            font-size: 18px;
                        }
                    }

                    .item-risk-b {
                        margin-top: 8px;
                    }
                }
            }

            .risk-empty {
                padding: 16px 0;

                .risk-empty-title {
                    font-size: 14px;

                    color: var(--text-100);
                }
            }
        }
    }
}

.help-text {
    color: rgba(255, 255, 255, 0.85);
}

.look-btn {
    color: var(--themeColor);
    width: 88px;
    line-height: 32px;
    background: #fff;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s;

    &:hover {
        opacity: 0.95;
    }
}

.dropdown-menu {
    width: 108px;
    text-align: center;
}
.ment-active {
    color: var(--themeColor);
}
.header-bg {
    backdrop-filter: blur(10px);
}
.user-info-dropdown {
    .ant-dropdown-menu {
        background: var(--input-bg);
    }
}
.dark {
    .user-info-dropdown {
        .ant-dropdown-menu {
            background: var(--main-bg);
        }
    }
    .el-dropdown-menu {
        background: var(--main-bg);
    }
    .el-dropdown-menu__item {
        color: var(--text-100);
    }
    .el-dropdown-menu__item.ment-active {
        color: var(--themeColor);
    }
    .el-dropdown-menu__item:not(.is-disabled):focus,
    .el-dropdown-menu__item:not(.is-disabled):hover {
        background: transparent;
        color: var(--themeColor);
    }
    .image-boxs {
        .notice-box {
            background-color: var(--main-bg);
            .set-up {
                color: var(--text-80);
            }
        }
    }

    .notice-box-dropdown {
        .ant-dropdown-menu {
            background: var(--main-bg);
        }
    }
}

.user-info-dropdown {
    .ant-dropdown-menu-item,
    .ant-dropdown-menu-submenu-title {
        color: var(--text-100);
    }
    .ant-dropdown-menu-item:hover,
    .ant-dropdown-menu-submenu-title:hover {
        background: transparent;
        color: var(--themeColor);
    }
}
.user-info-dropdown .ant-dropdown-menu {
    backdrop-filter: blur(10px);
    background-color: var(--header-bg);
}
.user-info-dropdown {
    .ant-dropdown-menu-item,
    .ant-dropdown-menu-submenu-title {
        color: var(--text-100);
    }
    .ant-dropdown-menu-item:hover,
    .ant-dropdown-menu-submenu-title:hover {
        background: transparent;
        color: var(--themeColor);
    }
}
.user-info-dropdown .ant-dropdown-menu {
    backdrop-filter: blur(10px);
    background-color: var(--header-bg);
}
.toggle-view-btn {
    padding: 0 8px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(34, 34, 34, 0.04);
    border-radius: 100px;
    user-select: none;
    cursor: pointer;
    margin-left: 13px;
    color: var(--themeColor);
    border: 1px solid transparent;
}
.dark {
    .toggle-view-btn {
        border: 1px solid var(--themeColor);
    }
}
</style>
