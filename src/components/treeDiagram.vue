<template>
    <div id="product_tree"></div>
</template>

<script>
import * as d3 from 'd3'
export default {
    data() {
        return {
            container: null,
            zoom: null,
            rootData: null,
            depthInfo: null,
            rootName: null,
        }
    },
    mounted() {
        const child = document.getElementById('product_tree')
        child.innerHTML = ''
        this.getData()
    },
    methods: {
        getData() {
            this.rootData = {
                downward: {
                    direction: 'downward',
                    name: 'origin',
                    children: [
                        {
                            name: '金科城有限公司',
                            amount: '100',
                            ratio: '55%',
                            hasHumanholding: true,
                            hasChildren: true,
                            isExpand: false,
                            children: [
                                {
                                    name: '公司名字',
                                    hasHumanholding: false,
                                    hasChildren: true,
                                    amount: '100',
                                    ratio: '55%',
                                    children: [],
                                },
                                {
                                    name: '公司名字',
                                    hasHumanholding: false,
                                    hasChildren: true,
                                    amount: '100',
                                    ratio: '55%',
                                    children: [],
                                },
                            ],
                        },
                        {
                            name: '大鹅普测有限公司',
                            amount: '100',
                            ratio: '55%',
                            hasHumanholding: true,
                            hasChildren: true,
                            isExpand: false,
                            children: [
                                {
                                    name: '公司名字',
                                    hasHumanholding: false,
                                    hasChildren: true,
                                    amount: '100',
                                    ratio: '55%',
                                    children: [],
                                },
                                {
                                    name: '公司名字',
                                    hasHumanholding: false,
                                    hasChildren: true,
                                    amount: '100',
                                    ratio: '55%',
                                    children: [],
                                },
                            ],
                        },
                        {
                            name: '北京多彩有限公司',
                            amount: '100',
                            ratio: '55%',
                            hasHumanholding: true,
                            hasChildren: true,
                            isExpand: false,
                            children: [
                                {
                                    name: '公司名字',
                                    hasHumanholding: false,
                                    hasChildren: true,
                                    amount: '100',
                                    ratio: '55%',
                                    children: [],
                                },
                                {
                                    name: '公司名字',
                                    hasHumanholding: false,
                                    hasChildren: true,
                                    amount: '100',
                                    ratio: '55%',
                                    children: [],
                                },
                            ],
                        },
                        {
                            name: '哈尔滨维斯特科技发展有限公司',
                            hasHumanholding: false,
                            hasChildren: true,
                            amount: '100',
                            ratio: '55%',
                            children: [],
                        },
                        {
                            name: '哈尔滨维斯特科技发展有限公司',
                            hasHumanholding: false,
                            hasChildren: true,
                            isExpand: false,
                            amount: '100',
                            ratio: '55%',
                            children: [
                                {
                                    name: '公司或股东名字',
                                    hasHumanholding: false,
                                    amount: '100',
                                    ratio: '55%',
                                    children: [],
                                },
                                {
                                    name: '公司或股东名字',
                                    hasHumanholding: false,
                                    amount: '100',
                                    ratio: '55%',
                                    children: [],
                                },
                                {
                                    name: '公司或股东名字',
                                    hasHumanholding: false,
                                    amount: '100',
                                    ratio: '55%',
                                    children: [],
                                },
                                {
                                    name: '公司或股东名字',
                                    hasHumanholding: false,
                                    amount: '100',
                                    ratio: '55%',
                                    children: [],
                                },
                            ],
                        },
                    ],
                },
            }
            this.rootName = '北京伴学科技有限公司'
            this.drawing()
        },
        drawing() {
            const _this = this
            let rootRectWidth = 0
            const forUpward = true

            class TreeChart {
                constructor(d3Object) {
                    this.d3 = d3Object
                    this.directions = ['downward']
                }

                drawChart() {
                    this.treeData = {}
                    const self = this
                    self.directions.forEach((direction) => {
                        self.treeData[direction] = _this.rootData[direction]
                    })
                    rootRectWidth = _this.rootName.length * 15
                    self.graphTree(self.getTreeConfig())
                }

                getTreeConfig() {
                    const treeConfig = {
                        margin: { top: 10, right: 5, bottom: 0, left: 5 },
                    }
                    treeConfig.chartWidth =
                        1600 - treeConfig.margin.right - treeConfig.margin.left
                    treeConfig.chartHeight =
                        800 - treeConfig.margin.top - treeConfig.margin.bottom
                    treeConfig.centralHeight = treeConfig.chartHeight / 2
                    treeConfig.centralWidth = treeConfig.chartWidth / 6
                    treeConfig.linkLength = 300
                    treeConfig.duration = 500
                    return treeConfig
                }

                graphTree(config) {
                    const self = this
                    const d3 = this.d3
                    const linkLength = config.linkLength
                    const duration = config.duration
                    const hasChildNodeArr = []
                    let id = 0
                    const diagonal = d3.svg
                        .diagonal()
                        .projection((d) => [d.y, d.x])
                    const pathFunc = funLine
                    const zoom = d3.behavior
                        .zoom()
                        .scaleExtent([0.5, 2])
                        .on('zoom', redraw)
                    const svg = d3
                        .select('#product_tree')
                        .append('svg')
                        .attr(
                            'width',
                            config.chartWidth +
                                config.margin.right +
                                config.margin.left
                        )
                        .attr(
                            'height',
                            config.chartHeight +
                                config.margin.top +
                                config.margin.bottom
                        )
                        .attr('xmlns', 'http://www.w3.org/2000/svg')
                        .on('mousedown', disableRightClick)
                        .call(zoom)
                        .on('dblclick.zoom', null)
                    const treeG = svg
                        .append('g')
                        .attr('class', 'gbox')
                        .attr(
                            'transform',
                            'translate(' +
                                config.margin.left +
                                ',' +
                                config.margin.top +
                                ')'
                        )
                    const markerDown = svg
                        .append('marker')
                        .attr('id', 'resolvedDown')
                        .attr('markerUnits', 'strokeWidth')
                        .attr('markerUnits', 'userSpaceOnUse')
                        .attr('viewBox', '0 -5 10 10')
                        .attr('refX', 0)
                        .attr('refY', 0)
                        .attr('markerWidth', 12)
                        .attr('markerHeight', 12)
                        .attr('orient', '0')
                        .attr('stroke-width', 2)
                        .append('path')
                        .attr('d', 'M0,-5L10,0L0,5')
                        .attr('fill', '#000')

                    for (const direction in this.directions) {
                        const data = self.treeData[direction]
                        if (typeof data !== 'undefined') {
                            data.x0 = config.centralHeight
                            data.y0 = config.centralWidth
                            data.children.forEach(collapse)
                            update(data, data, treeG)
                        }
                    }

                    function update(source, originalData, g) {
                        const direction = originalData['direction']
                        const node_class = direction + 'Node'
                        const link_class = direction + 'Link'
                        const downwardSign = 1
                        const nodeColor = '#8b4513'
                        const isExpand = false
                        const nodeSpace = 160
                        const tree = d3.layout
                            .tree()
                            .sort(sortByDate)
                            .nodeSize([nodeSpace, 0])
                        const nodes = tree.nodes(originalData)
                        const links = tree.links(nodes)
                        const offsetY = -config.centralHeight

                        nodes.forEach((d) => {
                            d.y =
                                downwardSign * (d.depth * linkLength) +
                                config.centralWidth
                            d.x = d.x - offsetY
                            if (d.name === 'origin') {
                                d.x = config.centralHeight
                                d.y += downwardSign * 0
                            }
                        })

                        const node = g
                            .selectAll('g.' + node_class)
                            .data(nodes, (d) => d.id || (d.id = ++id))

                        const nodeEnter = node
                            .enter()
                            .append('g')
                            .attr('class', node_class)
                            .attr('data-text', (d) => d.name)
                            .attr(
                                'transform',
                                (d) =>
                                    'translate(' +
                                    source.y0 +
                                    ',' +
                                    source.x0 +
                                    ')'
                            )
                            .style('cursor', (d) =>
                                d.name === 'origin'
                                    ? ''
                                    : d.children || d._children
                                    ? 'pointer'
                                    : ''
                            )

                        nodeEnter
                            .append('rect')
                            .attr('width', 0)
                            .attr('height', 0)
                            .attr('stroke', (d) =>
                                d.name === 'origin' ? '#e0e0e0' : nodeColor
                            )
                            .attr('stroke-width', 1)
                            .attr('rx', 0)
                            .attr('ry', 0)
                            .attr('x', 0)
                            .attr('y', 0)

                        const foreignObject = nodeEnter
                            .append('foreignObject')
                            .attr('width', 0)
                            .attr('height', 0)

                        const div = foreignObject
                            .append('xhtml:div')
                            .style('opacity', 0)
                            .style('color', '#333333')
                            .style('font-size', '12px')
                            .style('word-wrap', 'break-word')
                            .html((d) => {
                                let text = ''
                                if (d.name === 'origin') {
                                    text = _this.rootName
                                } else {
                                    text =
                                        '<span class="companyName">' +
                                        d.name +
                                        '</span>' +
                                        (d.ratio
                                            ? '<br/><span>持股比例：' +
                                              d.ratio +
                                              '</span>'
                                            : '')
                                    text += d.amount
                                        ? '<br/><span>认缴金额：' +
                                          d.amount +
                                          '</span>'
                                        : ''
                                }
                                return text
                            })

                        nodeEnter
                            .append('path')
                            .attr(
                                'style',
                                'fill:#000;stroke:#000;stroke-width:2'
                            )
                            .attr('d', (d) =>
                                d.name === 'origin' ? '' : 'M0,0L0,0'
                            )
                            .attr('marker-end', (d) =>
                                d.name === 'origin' ? '' : 'url(#resolvedDown)'
                            )

                        const nodeUpdate = node
                            .transition()
                            .duration(duration)
                            .attr(
                                'transform',
                                (d) => 'translate(' + d.y + ',' + d.x + ')'
                            )

                        nodeUpdate
                            .select('rect')
                            .attr('width', rootRectWidth)
                            .attr('height', 40)
                            .attr('stroke-width', 1)
                            .attr('rx', 10)
                            .attr('ry', 10)
                            .attr('x', -rootRectWidth / 2)
                            .attr('y', -20)

                        nodeUpdate
                            .select('foreignObject')
                            .attr('width', rootRectWidth)
                            .attr('height', 40)
                            .attr('x', -rootRectWidth / 2)
                            .attr('y', -20)

                        nodeUpdate
                            .select('foreignObject div')
                            .style('opacity', 1)

                        const nodeExit = node
                            .exit()
                            .transition()
                            .duration(duration)
                            .attr(
                                'transform',
                                (d) =>
                                    'translate(' +
                                    source.y +
                                    ',' +
                                    source.x +
                                    ')'
                            )
                            .remove()

                        nodeExit
                            .select('rect')
                            .attr('width', 0)
                            .attr('height', 0)

                        nodeExit
                            .select('foreignObject')
                            .attr('width', 0)
                            .attr('height', 0)

                        const link = g
                            .selectAll('path.' + link_class)
                            .data(links, (d) => d.target.id)

                        link.enter()
                            .insert('path', 'g')
                            .attr('class', link_class)
                            .attr('x', 0)
                            .attr('y', 0)
                            .attr('d', (d) => {
                                const o = { x: source.x0, y: source.y0 }
                                return pathFunc({ source: o, target: o })
                            })
                            .attr('marker-end', 'url(#resolvedDown)')

                        link.transition().duration(duration).attr('d', pathFunc)

                        link.exit()
                            .transition()
                            .duration(duration)
                            .attr('d', (d) => {
                                const o = { x: source.x, y: source.y }
                                return pathFunc({ source: o, target: o })
                            })
                            .remove()

                        nodes.forEach((d) => {
                            d.x0 = d.x
                            d.y0 = d.y
                        })

                        nodeEnter.on('click', (event, d) => {
                            if (d.name === 'origin') {
                                return
                            }
                            d3.selectAll('.contextmenu').remove()
                            if (d.children) {
                                d._children = d.children
                                d.children = null
                            } else {
                                d.children = d._children
                                d._children = null
                            }
                            update(d, originalData, g)
                        })

                        function sortByDate(a, b) {
                            if (a.children || a._children)
                                hasChildNodeArr.push(a)
                            if (b.children || b._children)
                                hasChildNodeArr.push(b)
                            return a.name.length - b.name.length
                        }
                    }

                    function funLine(d) {
                        const src = { x: d.source.x, y: d.source.y }
                        const target = { x: d.target.x, y: d.target.y }
                        return diagonal({ source: src, target: target })
                    }

                    function disableRightClick() {
                        d3.event.preventDefault()
                        d3.event.stopPropagation()
                    }

                    function collapse(d) {
                        if (d.children) {
                            d._children = d.children
                            d._children.forEach(collapse)
                            d.children = null
                        }
                    }

                    function redraw() {
                        treeG.attr(
                            'transform',
                            'translate(' +
                                d3.event.translate +
                                ') scale(' +
                                d3.event.scale +
                                ')'
                        )
                    }
                }
            }

            const newTree = new TreeChart(d3)
            newTree.drawChart()
        },
    },
}
</script>

<style scoped>
#product_tree {
    width: 100%;
    height: 100%;
    overflow: auto;
}
</style>
