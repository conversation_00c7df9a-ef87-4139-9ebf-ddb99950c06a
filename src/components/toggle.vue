<template>
    <div
        class="rounded-full toggle bg-ff dark:bg-transparent inline-flex items-center text-center leading-8 h-8"
        :class="disabled ? ' opacity-60 cursor-not-allowed' : ''"
    >
        <div
            class="w-12 flex items-center justify-center cursor-pointer pl-2"
            :class="[
                type == 'chart' ? 'active' : 'normal',
                disabled ? 'cursor-not-allowed' : '',
            ]"
            @click="toggle('chart')"
        >
            <iconSvg name="chart" class="w-5 h-5" />
        </div>
        <div class="h-full bg-border dark:bg-ff-dark" style="width: 1px"></div>
        <div
            class="w-12 flex items-center justify-center cursor-pointer pr-2"
            :class="[
                type == 'table' ? 'active' : 'normal',
                disabled ? 'cursor-not-allowed' : '',
            ]"
            @click="toggle('table')"
        >
            <iconSvg name="table" class="w-5 h-5" />
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    type: {
        type: String,
        default: 'chart',
    },
    disabled: {
        type: Boolean,
        default: false,
    },
})
const emits = defineEmits(['change', 'update:type'])
const toggle = (e) => {
    if (props.disabled) {
        return
    }
    if (e != props.type) {
        emits('update:type', e)
        emits('change', e)
    }
}
</script>

<style lang="less" scoped>
.toggle {
    border: 1px solid #dcdfe6;
}
.normal {
    color: var(--text-60);
}
.active {
    color: var(--themeColor);
}
</style>
