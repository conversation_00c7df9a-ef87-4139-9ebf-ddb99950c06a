<template>
    <div class="search">
        <p class="text-title dark:text-title-dark">{{ title }}</p>
        <el-space class="options" :size="12">
            <!-- 操作按钮 -->
            <template v-for="(btn, index) in searchData.searchButtons">
                <el-button
                    plain
                    round
                    @click="btn.click"
                    class="btn-hover mr-3"
                    :key="index"
                    v-if="!btn.hide"
                >
                    <span>{{ btn.name }}</span>
                    <span class="icon-box ml-0.5" v-if="btn.icon">
                        <iconSvg :name="btn.icon" class="icon-default" />
                    </span>
                </el-button>
            </template>
            <slot></slot>
            <!-- 搜索字段 -->
            <template v-for="(item, key) in searchData.fields" :key="key">
                <template v-if="item.ELType == 'el-date-picker'">
                    <el-date-picker
                        :style="{ width: item.width || '108px' }"
                        v-model="item.value"
                        type="daterange"
                        range-separator="-"
                        :start-placeholder="
                            item.startPlaceholder || $t('common_kaishiriqi')
                        "
                        :end-placeholder="
                            item.endPlaceholder || $t('common_jieshuriqi')
                        "
                        :format="item.valueFormat"
                        :value-format="item.valueFormat"
                        @change="search"
                        @clear="search"
                        v-bind="item"
                    >
                        <template #prefix>
                            <iconSvg name="calendar" :className="'w-5 h-5'" />
                        </template>
                    </el-date-picker>
                </template>
                <template v-else-if="item.ELType == 'el-select'">
                    <el-select
                        :style="{ width: item.width || '108px' }"
                        v-model="item.value"
                        :placeholder="item.placeholder"
                        @change="search"
                        v-bind="item"
                    >
                        <el-option
                            v-for="option in item.options"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                        />
                    </el-select>
                </template>
                <template v-else>
                    <component
                        :style="{ width: item.width || '108px' }"
                        :is="item.ELType"
                        v-model="item.value"
                        @change="search"
                        v-bind="item"
                    />
                </template>
            </template>
            <!-- 联合搜索框 -->
            <search-input
                v-if="searchData.filterData"
                :filterData="searchData.filterData"
                @search="handleFilter"
            ></search-input>
            <el-button
                plain
                round
                @click="btn.click"
                :disabled="btn.disabled"
                :loading="btn.loading"
                class="btn-hover mr-3"
                v-for="(btn, index) in searchData.operationButtons"
                :key="index"
            >
                <span>{{ btn.name }}</span>
                <span class="icon-box ml-0.5" v-if="btn.icon">
                    <iconSvg :name="btn.icon" class="icon-default" />
                </span>
            </el-button>
            <el-button
                plain
                round
                @click="search"
                v-if="showRefresh"
                class="btn-hover mr-3"
            >
                <span class="icon-box ml-0.5">
                    <iconSvg name="refresh" class="icon-default" />
                </span>
            </el-button>
        </el-space>
    </div>
</template>
<script>
import SearchInput from './search-input'
import { ReloadOutlined } from '@ant-design/icons-vue'

export default {
    name: 'search',
    components: { SearchInput, ReloadOutlined },
    props: {
        searchData: {
            type: Object,
            default: () => {},
        },
        title: String,
        filterData: {
            type: Object,
            default: () => {},
        },
        icon: String,
        showRefresh: {
            type: Boolean,
            default: true,
        },
    },
    setup(props, { emit }) {
        const search = () => {
            emit('search')
        }
        const handleFilter = (val) => {
            emit('update:filter', val)
            search()
        }
        return {
            search,
            handleFilter,
        }
    },
}
</script>
<style lang="less" scoped>
.search {
    padding: 16px 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;

    .options {
        float: right;
        display: flex;
        flex-wrap: wrap;
    }
}
</style>

<style scoped lang="less">
:deep(.ant-select-arrow) {
    z-index: 2;
}

:deep(.ant-calendar-picker .ant-calendar-picker-icon) {
    margin-top: -8px;
    top: 50%;
}
</style>
