<template>
    <div class="search-input-group">
        <el-select
            v-model="filterBy"
            @change="handleFilterByChange"
            :fit-input-width="false"
            v-if="Object.keys(filterData.options).length"
            class="left-options"
            :style="
                filterData.config && filterData.config.filterByWidth
                    ? {
                          width: filterData.config.filterByWidth,
                      }
                    : {}
            "
            :disabled="filterData.options.length ? false : true"
            :suffix-icon="filterData.options.length ? true : ''"
        >
            <template v-for="(val, key) in filterData.options" :key="key">
                <template
                    v-if="val.display && typeof val.display == 'function'"
                >
                    <el-option
                        :value="key"
                        :label="val.name"
                        v-if="val.display()"
                    />
                </template>
                <el-option v-else :value="key" :label="val.name" />
            </template>
        </el-select>
        <component
            :is="currentSearch.ELType"
            v-model="filterValue"
            style="width: 188px"
            clearable
            filterable
            :remote="!!currentSearch.handleSearch"
            :remote-method="handleSearch"
            @change="handleChange(currentSearch.ELType)"
            v-bind="currentSearch"
            :loading="selectLoading"
            :class="hasLeft ? 'right-input' : ''"
        />
    </div>
</template>
<script>
import { computed, reactive, toRaw, toRefs } from 'vue'
export default {
    name: 'search-input',
    props: {
        filterData: {
            type: Object,
            default: () => {
                return {}
            },
        },
        fnChangeFilterBy: Function,
    },
    setup(props, { emit }) {
        let { filterData } = toRefs(props)
        filterData.value.value.filterValue
        const state = reactive({
            filterBy:
                filterData.value.value.filterBy ||
                Object.keys(filterData.value.options)[0],
            filterValue: filterData.value.value.filterValue,
            selectLoading: false,
        })
        const currentSearch = computed(() => {
            return filterData.value.options[state.filterBy] || {}
        })
        const handleFilterByChange = async (val) => {
            state.filterValue = undefined
            let fnChangeFilterBy = filterData.value.fnChangeFilterBy
            if (fnChangeFilterBy && typeof fnChangeFilterBy == 'function') {
                state.selectLoading = true
                await fnChangeFilterBy(val)
                state.selectLoading = false
            }
            // search();
        }
        const handleSearch = (val) => {
            //如果有传handleSearch方法的，就直接调用该方法
            if (
                currentSearch.value.handleSearch &&
                typeof currentSearch.value.handleSearch == 'function'
            ) {
                currentSearch.value.handleSearch(val)
            } else {
                search()
            }
        }
        const handleChange = (type) => {
            //search组件不触发change
            if (type != 'el-input') {
                search()
            }
        }
        const search = () => {
            emit('search', toRaw(state))
        }
        const hasLeft = computed(() => {
            return Object.keys(filterData.value.options).length
        })
        return {
            ...toRefs(state),
            currentSearch,
            handleFilterByChange,
            handleSearch,
            handleChange,
            hasLeft,
        }
    },
}
</script>
<style lang="less">
.search-input.ant-input-group-compact .ant-select {
    border-right: none !important;
}
.ant-input-group.ant-input-group-compact > *:last-child,
.ant-input-group.ant-input-group-compact
    > .ant-select:last-child
    > .ant-select-selector,
.ant-input-group.ant-input-group-compact
    > .ant-calendar-picker:last-child
    .ant-input,
.ant-input-group.ant-input-group-compact
    > .ant-select-auto-complete:last-child
    .ant-input,
.ant-input-group.ant-input-group-compact
    > .ant-cascader-picker:last-child
    .ant-input,
.ant-input-group.ant-input-group-compact
    > .ant-cascader-picker-focused:last-child
    .ant-input,
.ant-input-group.ant-input-group-compact
    > .ant-mention-wrapper:last-child
    .ant-mention-editor,
.ant-input-group.ant-input-group-compact
    > .ant-time-picker:last-child
    .ant-time-picker-input {
    // border-right: none !important;
}
.ant-input-search-button {
    * {
        color: #666;
        fill: #666;
        transition: fill 0.3s;
    }
    &:hover {
        * {
            color: var(--themeColor);
            fill: var(--themeColor);
        }
    }
}
.left-options {
    .el-select__wrapper {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-right: 0;
        background-color: transparent;
    }
    margin-right: -1px;
}
.right-input {
    .el-select__wrapper {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
}
.search-input-group {
    .el-select + .el-input {
        .el-input__wrapper {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
    }
}
</style>
