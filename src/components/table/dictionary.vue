<template>
    <template v-if="statusObj[color] && showBadge">
        <span v-if="isTextColor" :style="{ color: statusObj[color] }">
            {{ statusObj[labelKey] }}
        </span>
        <span
            v-else-if="isBackgroundColor"
            class="isBackgroundColor rounded-sm"
            :style="{
                fontSize: '12px',
                background: statusObj.backGroundColor || 'rgba(0,0,0,.1)',
                color: statusObj[color] || 'var(--text-100)',
                height: '22px',
                lineHeight: '22px',
            }"
        >
            {{ statusObj[labelKey] }}
        </span>
        <!-- <a-badge v-else :color="statusObj.color" :text="statusObj[labelKey]" /> -->
        <span v-else class="inline-flex items-center">
            <b
                class="iconBadge"
                :style="{
                    backgroundColor: statusObj[color],
                    boxShadow: `0px 0px 2px 0px ${statusObj[color]}`,
                }"
            ></b>
            <!--  boxShadow: `0px 0px 2px 0px ${statusObj.shadowColor}`, -->
            <span class="text text-title dark:text-title-dark">{{
                statusObj[labelKey]
            }}</span>
        </span>
    </template>
    <span v-else class="text-label">{{ statusObj[labelKey] }}</span>
</template>
<script>
import { toRefs, computed, watch, toRef, reactive } from 'vue'
export default {
    props: {
        statusOptions: {
            type: Array,
            default: () => [],
        },
        value: [String, Number, Boolean],
        labelKey: {
            type: String,
            default: 'label',
        },
        isTextColor: {
            type: Boolean,
            default: false,
        },
        isBackgroundColor: {
            type: Boolean,
            default: false,
        },
        showBadge: {
            type: Boolean,
            default: true,
        },
        color: {
            type: String,
            default: 'color',
        },
    },
    setup(props) {
        const { statusOptions, value, isBackgroundColor } = toRefs(props)
        const statusObj = computed(() => {
            const item =
                statusOptions.value.find((s) => s.value === value.value) || {}
            return item
        })
        // const state = reactive({
        //   backgroundColor: false,
        // });
        // watch(
        //   isBackgroundColor,
        //   (val) => {
        //     state.backgroundColor = val;
        //   },
        //   { immediate: true }
        // );
        return {
            statusObj,
            // ...toRefs(state),
        }
    },
}
</script>
<style lang="less" scoped>
.badge {
    display: flex;
    align-items: center;
    .circle {
        width: 10px;
        height: 10px;
        background: rgba(255, 125, 0, 1);
        box-shadow: 0px 0px 4px 0px rgba(255, 125, 0, 0.5);
        border: 1px solid #ffffff;
        border-radius: 50%;
        margin-right: 4px;
    }
}
.isBackgroundColor {
    display: inline-block;
    height: 100%;
    padding: 0 8px;
}
.iconBadge {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    // border: 1px solid #ffffff;
    vertical-align: middle;
    margin-right: 6px;
    margin-top: 0;
}
.text {
    vertical-align: middle;
}
</style>
