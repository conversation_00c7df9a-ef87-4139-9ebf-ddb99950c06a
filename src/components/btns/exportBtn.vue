<template>
    <confirm-button
        :title="$t('Export data')"
        :content="content"
        @confirm="handleConfirm"
        @cancel="handleCancel"
        placement="bottom-end"
    >
        <template #reference>
            <el-button
                plain
                round
                class="btn-hover"
                :disabled="disabled"
                v-bind="$attrs"
            >
                <span>{{ t('Export_daochu') }}</span>
                <span class="icon-box ml-0.5" style="width: 20px">
                    <iconSvg name="export" class="icon-default" />
                </span>
            </el-button>
        </template>
    </confirm-button>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const props = defineProps({
    disabled: {
        type: Boolean,
        default: false,
    },
    content: {
        type: String,
        default: '',
    },
})
const emits = defineEmits(['confirm', 'cancel'])
const handleConfirm = () => {
    emits('confirm')
}
const handleCancel = () => {
    emits('cancel')
}
</script>

<style lang="scss" scoped></style>
