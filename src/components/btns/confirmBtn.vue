<template>
    <el-popover
        v-model:visible="visible"
        :placement="placement"
        :width="popoverWidth"
        plain
        trigger="manual"
    >
        <div class="confirm-popover">
            <div class="pl-7 relative">
                <div class="icon absolute left-0 top-0.5">
                    <iconSvg
                        name="help"
                        class="icon-default w-5 h-5"
                        style="color: #fd750b"
                    />
                </div>
                <div class="confirm-title" v-if="title">{{ title }}</div>
                <div class="confirm-content" v-if="content">{{ content }}</div>
            </div>

            <div class="confirm-actions">
                <el-button plain round @click="handleCancel" class="cancel-btn">
                    {{ cancelText || $t('common_fou') }}
                </el-button>
                <el-button
                    plain
                    round
                    type="primary"
                    @click="handleConfirm"
                    class="confirm-btn"
                >
                    {{ confirmText || $t('common_shi') }}
                </el-button>
            </div>
        </div>
        <template #reference>
            <div @click="handleReferenceClick" class="inline-block">
                <slot name="reference"></slot>
            </div>
        </template>
    </el-popover>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
    title: {
        type: String,
        default: '',
    },
    content: {
        type: String,
        default: '',
    },
    placement: {
        type: String,
        default: 'bottom-end',
    },
    popoverWidth: {
        type: [String, Number],
        default: 414,
    },
    confirmText: {
        type: String,
        default: '',
    },
    cancelText: {
        type: String,
        default: '',
    },
})

const emits = defineEmits(['confirm', 'cancel'])

const visible = ref(false)

const handleReferenceClick = () => {
    visible.value = true
}

const handleConfirm = () => {
    visible.value = false
    emits('confirm')
}

const handleCancel = () => {
    visible.value = false
    emits('cancel')
}
</script>

<style lang="less" scoped>
:deep(.el-popover) {
    border-radius: 9px !important;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.5);
}
.confirm-popover {
    padding: 16px;

    .confirm-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;
        line-height: 24px;
        color: var(--text-100);
    }

    .confirm-content {
        font-size: 14px;
        margin-bottom: 24px;
        line-height: 22px;
        word-break: break-word;
        color: var(--text-60);
    }

    .confirm-actions {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
        margin: 0;
    }
}

// 自定义 popover 样式
:deep(.el-popover) {
    padding: 0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #e4e7ed;
}
</style>
