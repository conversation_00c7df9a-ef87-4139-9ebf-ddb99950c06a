<template>
    <a-input
        autocomplete="off"
        :placeholder="$t('SMSCode')"
        :value="value"
        @input="$emit('update:value', $event.target.value)"
        :maxlength="4"
        v-bind="$attrs"
        size="large"
        class="input-color"
    >
        <template #prefix>
            <slot></slot>
        </template>
        <template #suffix>
            <el-button
                round
                plain
                v-if="disableTime == 0"
                @click="onChangeCode"
                size="small"
                class="bt-color"
                >{{ $t('GetVerificationCode') }}</el-button
            >
            <!-- <span class="disable-time" v-else>｜倒计时{{ disableTime }}s</span> -->
            <el-button
                round
                plain
                v-else
                @click="onChangeCode"
                class="time-color"
                disabled
                >{{ $t('login_daojishi') }} {{ disableTime }}s</el-button
            >
        </template>
    </a-input>
</template>
<script>
import { defineComponent, toRefs, getCurrentInstance } from 'vue'
import service from '@/apiService/device'
import { useDisableTime } from '@/common/setup'
import { mobile } from '@/common/reg'
import { useI18n } from 'vue-i18n'
export default defineComponent({
    props: {
        phone: {
            type: String,
        },
        skipTime: {
            type: Number,
            default: 60,
        },
        value: String,
        smsVerifyType: String,
    },
    setup(props, { emit }) {
        const { t, locale } = useI18n()
        const { phone, smsVerifyType } = toRefs(props)
        const { disableTime, setDisableTime } = useDisableTime()
        const { proxy } = getCurrentInstance()
        const onChangeCode = async () => {
            if (!mobile.test(phone.value)) {
                proxy.$message.error(t('Please enter a valid phone number'))
            } else {
                setDisableTime()
                await service.sendSmsCode({ phone: phone.value })
            }
            //   emit("onChangeCode", async () => {
            //     setDisableTime();
            //     let result = await service.getSmsCode(phone.value);
            //   });
        }
        return {
            onChangeCode,
            disableTime,
        }
    },
})
</script>
<style lang="less" scoped>
.disable-time {
    color: #e5e5e5;
}

.ant-input-affix-wrapper {
    // padding: 0 12px;
}

.bt-color {
    // color: var(--themeColor);
    height: 32px;
    line-height: 1 !important;
    padding: 0;
    position: absolute;
    right: 4px;
    bottom: 4px;
    padding: 0 12px;
    font-size: 14px;
    min-width: 96px;
}

.time-color {
    height: 32px;
    line-height: 1 !important;
    padding: 0;
    position: absolute;
    right: 4px;
    bottom: 4px;
    padding: 0 12px;
    font-size: 14px;
    min-width: 96px;
}

.bt-time {
    height: 32px;
    line-height: 32px;
    padding: 0;
    position: absolute;
    right: 4px;
    padding: 0 12px;
    font-size: 16px;
}

.input-color {
    &:focus {
        border-color: var(--themeColor) !important;
    }

    :deep(.ant-input) {
        font-size: 16px;
    }
}
</style>
