<template>
    <div style="height: 600px">
        <vue3-tree-org
            :data="chartData"
            :props="{
                id: 'id',
                label: 'name',
                pid: 'parentId',
                children: 'children',
                isLeaf: 'isLeaf',
                stationName: 'stationName',
            }"
            :center="true"
            :horizontal="horizontal"
            :collapsable="collapsable"
            :only-one-node="onlyOneNode"
            :clone-node-drag="cloneNodeDrag"
            :before-drag-end="beforeDragEnd"
            :label-style="labelStyle"
            @on-node-drag="nodeDragMove"
            @on-node-drag-end="nodeDragEnd"
            @on-contextmenu="onMenus"
            @on-expand="onExpand"
            @on-node-dblclick="onNodeDblclick"
            @on-node-click="onNodeClick"
            @on-zoom="onZoom"
            :toolBar="{
                scale: true,
                restore: true,
                expand: false,
                zoom: true,
                fullscreen: true,
            }"
            :node-draggable="false"
            :draggable-on-node="true"
            disabled
            :define-menus="[]"
        >
            <template v-slot="{ node }">
                <div
                    v-if="node.$$data.resourceType === 'station'"
                    class=""
                    style="
                        width: 472px;
                        box-shadow: 0 0 10px 0 rgba(200, 200, 200, 1);
                    "
                >
                    <!--   @click="goDetail(item)" -->
                    <device-box
                        :data="node.$$data"
                        v-model:userType="userType"
                    />
                </div>
                <div class="border border-border rounded-lg px-3 py-4" v-else>
                    <div class="flex justify-between items-center mb-3">
                        <div>
                            <dictionary
                                :statusOptions="orgTypeOptions"
                                :value="node.$$data.orgType"
                                isBackgroundColor
                                :color="'color'"
                            />
                        </div>
                    </div>
                    <div class="node-name text-left">
                        <div class="text-base font-medium">
                            {{ node.label }}
                        </div>
                    </div>
                    <a-divider class="my-4"></a-divider>
                    <div class="flex justify-between items-center statistics">
                        <div class="text-left">
                            <div
                                class="mb-2 text-secondar-text dark:text-60-dark"
                            >
                                累计装机容量({{
                                    alternateUnits(
                                        node.$$data.totalInstalledCapacity,
                                        1000
                                    )
                                        ? 'MWh'
                                        : 'kWh'
                                }})
                            </div>
                            <div>
                                {{
                                    unitConversion(
                                        node.$$data.totalInstalledCapacity,
                                        1000
                                    )
                                }}
                            </div>
                        </div>
                        <a-divider type="vertical" class="m-0 h-9"></a-divider>
                        <div class="text-left">
                            <div
                                class="mb-2 text-secondar-text dark:text-60-dark"
                            >
                                累计收益({{
                                    alternateUnits(
                                        node.$$data.totalProfit,
                                        10000
                                    )
                                        ? '万元'
                                        : '元'
                                }})
                            </div>
                            <div>
                                {{
                                    unitConversion(
                                        node.$$data.totalProfit,
                                        10000
                                    )
                                }}
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <!-- 自定义展开按钮 -->
            <template v-slot:expand="{ node }">
                <div>{{ node.children.length }}</div>
            </template>
        </vue3-tree-org>
    </div>
</template>

<script>
import { ref, reactive, toRaw, watch, nextTick, computed } from 'vue'
import { useStore } from 'vuex'
import { unitConversion, alternateUnits } from '@/views/device/const'
import Dictionary from '@/components/table/dictionary.vue'
import DeviceBox from '../views/device/home/<USER>'
export default {
    name: 'baseTree',
    components: {
        Dictionary,
        DeviceBox,
    },
    setup() {
        const store = useStore()
        const getCompanyInfo = computed(
            () => store.getters['user/getUserInfoData']
        )
        const cloneNodeDrag = ref(true)
        const userType = computed(() => {
            // const node = searchNode(treeData.value, selectedKeys.value[0])
            // !node.node.children &&
            if (getCompanyInfo.value?.orgType == 'customer') {
                // 如果是客户视角
                return 'customer'
            } else if (getCompanyInfo.value?.orgType == 'supplier') {
                // 如果是供应商，但是没有子节点
                return 'supplier'
            } else {
                return 'all'
            }
        })
        return {
            cloneNodeDrag,
            userType,
        }
    },
    data() {
        return {
            horizontal: true,
            collapsable: true,
            onlyOneNode: false,
            expandAll: true,
            disaled: false,
            labelStyle: {
                background: '#fff',
                width: '280px',
                borderRadius: '8px',
            },
            orgTypeOptions: [
                {
                    value: 'supplier',
                    label: '服务商',
                    color: '#FD750B',
                    backGroundColor: 'rgba(253, 117, 11, 0.10)',
                },
                {
                    value: 'customer',
                    label: '客户',
                    color: '#1677FF',
                    backGroundColor: 'rgba(22, 119, 255, 0.10)',
                },
            ],
            data: {},
        }
    },
    props: {
        chartData: {
            type: Object,
            default: () => ({}),
        },
        selectedKeys: {
            type: Array,
            default: () => [],
        },
    },
    watch: {
        chartData: {
            deep: true,
            immediate: true,
            handler(val) {
                if (val && val.id) {
                    this.data = val
                    this.toggleExpand(this.data, this.expandAll)
                }
            },
        },
    },
    created() {},
    mounted() {
        //
        this.toggleExpand(this.data, this.expandAll)
    },
    methods: {
        onMenus({ node, command }) {},
        onExpand(e, data) {
            this.$emit('onExpand', e, data)
        },
        onExpandAll(b) {},
        nodeDragMove(data) {},
        beforeDragEnd(node, targetNode) {
            return new Promise((resolve, reject) => {
                if (!targetNode) reject()
                if (node.id === targetNode.id) {
                    reject()
                } else {
                    resolve()
                }
            })
        },
        nodeDragEnd(data, isSelf) {},
        onNodeDblclick() {},
        goDetail(record) {
            const { stationNo } = record
            // setPageCurrent设置vuex中的值
            // store.commit('device/setPageCurrent', pageConfig.current)
            sessionStorage.setItem(
                'stationPic',
                record.stationPic || require('@/assets/device/defaultImg.png')
            )
            this.$router.push({
                name: 'deviceDetail',
                query: { stationNo, supplierId: this.selectedKeys[0] },
                params: { stationPic: record.stationPic },
            })
        },
        async onNodeClick(e, data) {
            // console.log('[ e ] >', e, data)
            // ElMessage.info(data.name)
            if (data.stationNo) {
                this.goDetail(data)
            }
        },
        onZoom(e) {
            // e.preventDefault()
        },
        expandChange(e, data) {},
        toggleExpand(data, val) {
            // 只展开一层。
            if (Array.isArray(data)) {
                data.forEach((item) => {
                    item.expand = val
                    if (item.children) {
                        item.children.forEach((child) => {
                            child.expand = val
                        })
                        // this.toggleExpand(item.children, val)
                    }
                })
            } else {
                data.expand = val
                // if (data.children) {
                //     this.toggleExpand(data.children, val)
                // }
            }
        },
        alternateUnits,
        unitConversion,
    },
}
</script>

<style lang="less" scoped>
.node-name {
    color: #0e1423;
}
.statistics {
    line-height: 1;
}
</style>
