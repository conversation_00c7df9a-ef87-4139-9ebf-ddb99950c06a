<template>
    <a-drawer
        class="mw-drawer"
        width="486"
        placement="right"
        :closable="false"
        :maskClosable="false"
    >
        <header class="border-b border-d9 px-5 flex justify-between">
            <span class="font-medium" style="font-size: 16px"
                >{{ customTitle }}
                <slot name="tag"></slot>
            </span>
            <a-space>
                <a-button shape="round" @click="$attrs.onClose" class="btns">{{
                    closeText
                }}</a-button>
                <slot name="header"></slot>
            </a-space>
        </header>
        <!-- {{ JSON.stringify({ ...$attrs, title: "标题" }) }} -->
        <!-- <a-drawer v-bind="$attrs" width="486" :maskClosable="false"> -->
        <div
            id="content"
            class="px-5 my-5"
            style="height: calc(100vh - 54px - 40px); overflow: auto"
        >
            <slot></slot>
        </div>
    </a-drawer>
</template>
<script>
export default {
    props: {
        customTitle: String,
        closeText: {
            type: String,
            default: '关闭',
        },
    },
    setup() {},
}
</script>
<style lang="less" scoped>
.mw-drawer {
    header {
        height: 54px;
        line-height: 54px;

        .btns {
            height: 32px;
            line-height: 16px;
            font-size: 14px;

            padding: 6px 12px;
            box-sizing: border-box;
            background: #f5f7f7;
            border-color: #f5f7f7;
            color: #222222;

            &:hover {
                border-color: #f5f7f7;
                color: #222222;
            }
        }

        :deep(.ant-btn) {
            height: 32px;
            line-height: 16px;
            font-size: 14px;

            padding: 6px 12px;
            box-sizing: border-box;
            background: #f5f7f7;
            border-color: #f5f7f7;
            color: #222222;

            &:hover {
                border-color: #f5f7f7;
                color: #222222;
            }
        }

        :deep(.ant-btn-primary) {
            background: var(--themeColor);
            border-color: var(--themeColor);
            outline-color: var(--themeColor);
        }

        :deep(.ant-btn-primary) {
            color: #fff;
            background-color: var(--themeColor);
            border-color: var(--themeColor);

            &:hover {
                color: #fff;
                border-color: var(--themeColor);
            }
        }

        :deep(
                .ant-btn-primary[disabled],
                .ant-btn-primary[disabled]:hover,
                .ant-btn-primary[disabled]:focus,
                .ant-btn-primary[disabled]:active
            ) {
            color: #fff;
            background-color: var(--themeColor);
            border-color: var(--themeColor);
            opacity: 0.6;
        }

        :deep(
                .btns.ant-btn-loading:not(.ant-btn-circle):not(
                        .ant-btn-circle-outline
                    ):not(.ant-btn-icon-only)
                    .anticon:not(:last-child)
            ) {
            margin-left: -14px;
        }
    }
}

:deep(.ant-btn[disabled]) {
    background: #f5f5f5;
}
</style>
<style lang="less">
.mw-drawer {
    .ant-drawer-body {
        padding: 0;
    }
}
</style>

<style lang="less">
// .mw-drawer {
//     .ant-space-item {
//         font-size: 0;
//     }
// }
</style>
