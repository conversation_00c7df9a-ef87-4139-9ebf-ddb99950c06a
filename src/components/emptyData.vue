<template>
    <a-empty
        image="https://ming-enterprise.oss-cn-hangzhou.aliyuncs.com/wxMiniApp/staticResource/common/empty.svg"
        :image-style="{
            height: '60px',
        }"
    >
        <template #description>
            <span style="color: var(--text-40)">
                {{ description }}
            </span>
        </template>
        <slot></slot>
    </a-empty>
</template>
<script>
import { useI18n } from 'vue-i18n'
export default {
    props: {
        description: {
            type: String,
            default: '暂无数据',
        },
    },
    setup(props, { emit }) {
        const { t } = useI18n()
        return {
            t,
        }
    },
}
</script>
