<template>
  <template v-if="genre != 'customized'">
    <span v-if="size">
      <span :style="{ fontSize: size + 'px' }">{{
        formattermoney(value || 0).l
      }}</span
      ><span :style="{ fontSize: size + 'px' }">{{
        formattermoney(value || 0).r
      }}</span
      ><span v-if="showMark">元</span>
    </span>
    <span v-else>
      <span style="font-size:inherit">{{ formattermoney(value || 0).l }}</span
      ><span style="font-size:inherit">{{ formattermoney(value || 0).r }}</span
      ><span v-if="showMark">元</span>
    </span>
  </template>
  <template v-else>
    <template v-if="rangeFlag == '0'">价格面议</template>
    <template v-else>
      <span style="font-size:inherit">
        {{ formattermoney(minPrice).l }}
      </span>
      <span style="font-size:inherit">{{ formattermoney(minPrice).r }}</span>
      <template v-if="maxPrice > 0 && maxPrice > minPrice">
        <span style="font-size:inherit">
          -
          {{ formattermoney(maxPrice).l }}
        </span>
        <span style="font-size:inherit">{{ formattermoney(maxPrice).r }}</span>
        <span v-if="showMark">元</span>
      </template>
      <span v-else-if="showMark">元</span>
    </template>
  </template>
</template>
<script>
import { formattermoney } from "@/common/util";
export default {
  props: {
    genre: {
      type: String,
      default: () => "",
    },
    value: {
      type: Number,
      default: () => null,
    },
    size: Number,
    showMark: {
      type: Boolean,
      default: () => true,
    },
    minPrice: {
      type: Number,
      default: () => 0,
    },
    maxPrice: {
      type: Number,
      default: () => 0,
    },
    rangeFlag: {
      type: String,
      default: "",
    },
  },
  setup(props) {
    return { formattermoney };
  },
};
</script>
<style lang="less" scoped></style>
