<template>
    <div class="s-box">
        <div class="dl" @click="goSystem('vehicle')"></div>
        <div class="cn" @click="goSystem('device')"></div>
        <div class="bg"></div>
    </div>
</template>

<script setup>
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
const route = useRoute(),
    router = useRouter()
const goSystem = (path) => {
    router.push(`/${path}`)
}
</script>

<style lang="less" scoped>
.s-box {
    width: 65px;
    height: 32px;
}
</style>
