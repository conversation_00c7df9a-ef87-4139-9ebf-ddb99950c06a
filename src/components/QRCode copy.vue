<template>
    <div>
        <div class="flex">
            <el-radio-group v-model="radioValue" size="large">
                <el-radio-button label="微信" value="wechat" />
                <el-radio-button label="钉钉" value="dingtalk" />
            </el-radio-group>
        </div>
        <div v-if="radioValue === 'wechat'">
            <div class="relative">
                <div class="flex justify-center relative z-10">
                    <vue-qr
                        v-if="info && info.thirdUserName"
                        :logoSrc="src2"
                        :text="wUrl2"
                        :callback="test"
                        :size="200"
                    ></vue-qr>
                    <vue-qr
                        v-else
                        :logoSrc="src2"
                        :text="wUrl"
                        :callback="test"
                        :size="200"
                    ></vue-qr>
                    <!-- <vue-qr text="Hello world!" :callback="test" qid="testid"></vue-qr> -->
                </div>
                <!-- <div
                class="flex justify-center absolute top-0 left-0 right-0 bottom-0 w-full h-full z-20"
            >
                <div
                    class="flex justify-center items-center font-medium text-base"
                    style="
                        width: 200px;
                        height: 200px;
                        background: rgba(255, 255, 255, 0.8);
                    "
                >
                    已绑定
                </div>
            </div> -->
            </div>
            <div class="px-5">
                <template v-if="info && info.thirdUserName">
                    <div
                        class="text-center text-primary-text dark:text-80-dark"
                    >
                        您可使用微信扫码或打开微信搜索访问<span
                            class="text-title dark:text-title-dark"
                            >上善能及储能</span
                        >小程序
                    </div>
                    <div
                        class="text-center text-primary-text dark:text-80-dark"
                    >
                        当前绑定微信账户：<span
                            class="text-title dark:text-title-dark"
                            >【{{ info.thirdUserName }}】</span
                        >
                    </div>
                </template>
                <template v-else>
                    <div
                        class="text-center text-primary-text dark:text-80-dark mb-8"
                    >
                        微信扫码绑定
                    </div>
                    <div
                        class="text-center text-primary-text dark:text-80-dark"
                    >
                        绑定完成后，即可访问微信小程序，在线查看设备运行状态与数据。
                    </div>
                </template>
            </div>
        </div>
        <div v-if="radioValue === 'dingtalk'">
            <div class="relative">
                <div class="flex justify-center relative z-10">
                    <vue-qr
                        v-if="info && info.thirdUserName"
                        :logoSrc="src2"
                        :text="url2"
                        :callback="test"
                        :size="200"
                    ></vue-qr>
                    <vue-qr
                        v-else
                        :logoSrc="src2"
                        :text="url"
                        :callback="test"
                        :size="200"
                    ></vue-qr>
                    <!-- <vue-qr text="Hello world!" :callback="test" qid="testid"></vue-qr> -->
                </div>
                <!-- <div
                class="flex justify-center absolute top-0 left-0 right-0 bottom-0 w-full h-full z-20"
            >
                <div
                    class="flex justify-center items-center font-medium text-base"
                    style="
                        width: 200px;
                        height: 200px;
                        background: rgba(255, 255, 255, 0.8);
                    "
                >
                    已绑定
                </div>
            </div> -->
            </div>
            <div class="px-5">
                <template v-if="info && info.thirdUserName">
                    <div
                        class="text-center text-primary-text dark:text-80-dark"
                    >
                        您可使用钉钉扫码或打开钉钉工作台访问<span
                            class="text-title dark:text-title-dark"
                            >上善能及储能</span
                        >小程序
                    </div>
                    <div
                        class="text-center text-primary-text dark:text-80-dark"
                    >
                        当前绑定钉钉账户：<span
                            class="text-title dark:text-title-dark"
                            >【{{ info.thirdUserName }}】</span
                        >
                    </div>
                </template>
                <template v-else>
                    <div
                        class="text-center text-primary-text dark:text-80-dark mb-8"
                    >
                        钉钉扫码绑定
                    </div>
                    <div
                        class="text-center text-primary-text dark:text-80-dark"
                    >
                        绑定完成后，即可访问钉钉小程序，在线查看设备运行状态与数据。
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script>
import { toRefs, watch } from 'vue'
export default {
    /*************  ✨ Codeium Command ⭐  *************/
    /**
     * Data properties
     *
     * @property {String} radioValue  - Type of QR code
     * @property {String} src2  - Logo of QR code
     * @property {String} url  - URL of QR code for binding
     * @property {String} url2  - URL of QR code for device home page
     */
    /******  85d1dfc9-ad2a-419c-90cb-130bb08885bf  *******/
    data() {
        return {
            // src: 'https://www.baidu.com/img/bd_logo1.png',
            radioValue: 'wechat',
            src2: require('@/assets/login/icon.png'),
            // 目前是跳转到详情页，后续需要修改为绑定页
            // 微信
            wUrl: 'https://open.weixin.qq.com/connect/qrconnect?appid=wxb5a2d0d1e0e9a3a6&redirect_uri=https%3A%2F%2Fwww.ourenergy.cn%2F&scope=snsapi_login&state=1&login_type=jssdk',
            wUrl2: `https://open.weixin.qq.com/connect/qrconnect?appid=wxb5a2d0d1e0e9a3a6&redirect_uri=https%3A%2F%2Fwww.ourenergy.cn%2F&scope=snsapi_login&state=1&login_type=jssdk`,
            // 钉钉
            url: `dingtalk://dingtalkclient/action/open_micro_app?appId=129525&page=pages%2Fbind%2Fbinding%3FuserId%3D${this.$store.state.user.userInfoData.userId}`,
            url2: `dingtalk://dingtalkclient/action/open_micro_app?appId=129525&page=pages%2Fdevice%2Findex%3FuserId%3D${this.$store.state.user.userInfoData.userId}`,
        }
    },
    created() {
        //
    },
    props: {
        info: {
            type: Object,
            default: () => {
                return {}
            },
        },
    },
    watch: {
        info: {
            deep: true,
            immediate: true,
            handler(newVal) {},
        },
    },
    methods: {
        test() {},
    },
}
</script>

<style lang="less" scoped></style>
