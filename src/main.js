import { createApp } from "vue";
import App from "./App.vue";
import Antd from "ant-design-vue";
import { Modal } from "ant-design-vue";
import "ant-design-vue/dist/antd.less";
import router from "@/router";
import './styles/theme.less'
import "@/less/app.less";
import "@/assets/iconfont/iconfont.css";
import "@/assets/iconfonts/iconfont.css"

import store from "@/store";
import exportButton from "@/components/btns/exportBtn.vue";
import confirmButton from "@/components/btns/confirmBtn.vue";
import comfirmPop from "@/components/pops/comfirmPop.vue";
import mwDrawer from "@/components/drawer.vue";
import goBack from "@/components/goback.vue";
import EmptyData from "@/components/emptyData.vue";
import mwTable from "@/components/table/mwTable.vue";
import ownerPermission from "@/common/permission.js";
import appConstant from "@/config/appConstant.js";
import iconSvg from "@/components/svgIcon";
import price from '@/components/price'
import Vue3ColorPicker from "vue3-colorpicker";
import "vue3-colorpicker/style.css";
import {
  CaretLeftOutlined,
  CaretRightOutlined,
  SearchOutlined,
  CloseOutlined,
  SyncOutlined,
  PlusOutlined,
  LoadingOutlined,
  CalendarOutlined
} from "@ant-design/icons-vue";
// import { Empty } from "ant-design-vue";
// import * as echarts from "echarts";
import "@/less/tailwind.css";

import vue3TreeOrg from 'vue3-tree-org';
import "vue3-tree-org/lib/vue3-tree-org.css";
// import { ConfigProvider } from "ant-design-vue";

// ConfigProvider.config({
//   theme: {
//     primaryColor: "#141414",
//     // primaryColor: "#ea0c28",
//   },
// });
import VueQr from 'vue-qr/src/packages/vue-qr.vue'
// import { ElDatePicker,ElConfigProvider,ElSelect ,ElOption,ElDialog,ElButton,ElTabs,ElTabPane,ElDrawer} from 'element-plus'
import ElementPlus from 'element-plus'
// import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/index.css'
import './styles/element/index.less'

import i18n from './lang'

import './permission' // permission control

const app = createApp(App);

app.directive("owner", {
  mounted: ownerPermission,
});
app.config.globalProperties.$message = Antd.message;
app.config.globalProperties.$Modal = Modal;
app.config.globalProperties.$appConstant = appConstant;
app.config.productionTip = false;
// app.config.globalProperties.$echarts = echarts;
app.use(i18n)
app.use(Antd);
app.use(router);
app.use(store);
app.use(Vue3ColorPicker);
app.use(vue3TreeOrg)

app.component("exportButton", exportButton);
app.component("confirmButton", confirmButton);
app.component("comfirmPop", comfirmPop);

app.component("go-back", goBack);
app.component("mw-drawer", mwDrawer);
app.component("mw-table", mwTable);
app.component("empty-data", EmptyData);
app.component('iconSvg', iconSvg)
app.component('price', price)
app.component("CaretLeftOutlined", CaretLeftOutlined);
app.component("CaretRightOutlined", CaretRightOutlined);
app.component("SearchOutlined", SearchOutlined);
app.component("CloseOutlined", CloseOutlined);
app.component("SyncOutlined", SyncOutlined);
app.component("PlusOutlined", PlusOutlined);
app.component("LoadingOutlined", LoadingOutlined);
app.component("CalendarOutlined", CalendarOutlined);
app.component("VueQr", VueQr);
// app.component("ElDatePicker",ElDatePicker)
// app.component("ElConfigProvider",ElConfigProvider)
// app.component("ElSelect",ElSelect)
// app.component("ElOption",ElOption)
// app.component("ElDialog",ElDialog)
// app.component("ElButton",ElButton)
// app.component("ElTabs",ElTabs)
// app.component("ElTabPane",ElTabPane)
// app.component("ElDrawer",ElDrawer)

app.use(ElementPlus)



app.mount("#app");
