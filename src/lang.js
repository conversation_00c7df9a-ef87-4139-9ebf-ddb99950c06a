import { createI18n } from 'vue-i18n'
import zh from '@/config/lang/zh'
import en from '@/config/lang/en'
const i18n = createI18n({
  locale: localStorage.getItem('lang') || 'zh',    // 前端选择语言的时候可以缓存起来，这样下次进入的时候就会是上次选择的语言包，默认是中文，
  fallbackLocale: 'zh',
  legacy: false,
  messages: {
    'zh': zh,   // 中文语言包    
    'en': en,    // 英文语言包
  },
  silentTranslationWarn: true,
  missingWarn: false,
  silentFallbackWarn: true,
  fallbackWarn: false

})
export default i18n