/* 白天模式默认 */
:root {
  /* --themeColor: #6FBECE; */
  --main-bg: rgba(1, 39, 60, 0.04);
  --second-bg:#fff;
  --input-bg: #ffffff;
  --input-color: #222222;
  --text-100: #222222;
  --text-80: rgba(34, 34, 34, 0.8);
  --text-60: rgba(34, 34, 34, 0.6);
  --text-40: rgba(34, 34, 34, 0.4);
  --text-20: rgba(34, 34, 34, 0.2);
  --theme-title: #222222;
  --tag-blue-bg: rgba(22, 119, 255, 0.1);
  --tag-blue-color: #1677FF;
  --tag-orange-bg: rgba(253, 117, 11, 0.1);
  --tag-orange-color: #fd750b;
  --tag-online-bg:rgba(62, 218, 205, 0.10);
  --tag-online-color:#3EDACD;
  --tag-offline-bg:rgba(255,81,81,0.1);
  --tag-offline-color:#FF5151;
  --border: rgba(34, 34, 34, 0.08);
  --bg-f5: rgba(245, 247, 247, 1);
  --selected-color: var(--themeColor);
  --header-bg: #ffffff;
  --echart-title: rgba(34, 34, 34, 0.6);
  --echart-line: rgba(34, 34, 34, 0.08);
  --echart-item-border: #fff;
  --car-pie-border:#fff;
  --bg-f5f7fa: #f5f7fa;
  --placeholder: rgba(34, 34, 34, 0.25);
  --input-disabled: rgba(0, 0, 0, 0.25);
  --table-hover-bg: #f5f5f5;
  --btn-border:#dcdfe6;
  --text-error:#FD0B0B;
  --bg-white:rgba(34, 34, 34, 0.04);
}

/* 黑夜模式 */
.dark {
  /* --themeColor: #ffffff; */
  --main-bg: #072347;
  --second-bg:#39506B;
  --input-bg: rgba(255, 255, 255, 0.2);
  --input-color: #ffffff;
  --text-100: #ffffff;
  --text-80: rgba(255, 255, 255, 0.8);
  --text-60: rgba(255, 255, 255, 0.6);
  --text-40: rgba(255, 255, 255, 0.4);
  --text-20: rgba(255, 255, 255, 0.2);
  --theme-title: #222222;
  --tag-blue-bg: #006AFF;
  --tag-blue-color: #ffffff;
  --tag-orange-bg: #FD750B;
  --tag-orange-color: #ffffff;
  --tag-online-bg:#3EDACD;
  --tag-online-color:#fff;
  --tag-offline-bg:#FF5151;
  --tag-offline-color:#fff;
  --border: rgba(255, 255, 255, 0.08);
  --bg-f5: rgba(245, 247, 247, 0.20);
  --selected-color: var(--themeColor);
  --header-bg: rgba(255, 255, 255, 0.2);
  --echart-title: rgba(255, 255, 255, 0.6);
  --echart-line: rgba(255, 255, 255, 0.08);
  --echart-item-border: #072347;
  --car-pie-border:#39506B;
  --bg-f5f7fa: rgba(245, 247, 250, 0.2);
  --placeholder: rgba(255, 255, 255, 0.4);
  --input-disabled: rgba(255, 255, 255, 0.25);
  --table-hover-bg: rgba(255, 255, 255, 0.08);
  --btn-border:#dcdfe6;
  --text-error:#FD0B0B;
  --bg-white:transparent;
}