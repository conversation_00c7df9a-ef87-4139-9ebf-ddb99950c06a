.list-card {
  margin-bottom: 16px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  transition: all 0.2s;
  &:hover {
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
    border-color: #141414;
  }
  .header {
    height: 44px;
    line-height: 44px;
    padding: 0 16px;
    border-bottom: 1px solid #d9d9d9;
  }
  .content {
    // height: 120px;
    padding: 16px 0;
    .col {
      // height: 88px;
      overflow: hidden;
      display: flex;
      flex-flow: column;
      justify-content: center;
      padding: 0 24px;
      & + .col {
        border-left: 1px solid #d9d9d9;
      }
    }
  }
}
