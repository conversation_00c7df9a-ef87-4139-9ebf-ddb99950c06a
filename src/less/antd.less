.ant-input-number .ant-input-number-handler-wrap {
  opacity: 1;
}

.ant-btn[type='button'] {
  border: 1px solid theme('colors.border');
}

.ant-btn[type='button'][linear] {
  border: 1px solid var(--themeColor);
  color: var(--themeColor);
}

.ant-modal-content{
  background-color: var(--input-bg) !important;
  backdrop-filter: blur(10px);
}
.ant-modal-header {
  border-radius: 8px 8px 0 0;
  background-color: var(--input-bg) !important;
}

.ant-modal-content {
  border-radius: 8px;
}

.ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date {
  background: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date {
  background: var(--themeColor);
}

.ant-input:hover {
  border-color: var(--themeColor);
}

.ant-input-affix-wrapper {
  &:focus {
    box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.1);
    border-color: var(--themeColor);
  }

  &:hover {
    box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.1);
    border-color: var(--themeColor);
  }
}

.ant-input-affix-wrapper-focused {
  border-color: var(--themeColor);
}

.ant-select-selector {
  border-radius: 4px !important;
  background-color: transparent !important;
}

.ant-input,
.ant-select {
  border-radius: 4px !important;
  background-color: transparent !important;
}

.ant-input-number {
  border-radius: 4px !important;
}

.ant-input-number:hover {
  border-color: var(--themeColor);
}

.ant-input-number:focus {
  border-color: var(--themeColor);
}

.ant-select:not(.ant-select-disabled):hover .ant-select-selector {
  border-color: var(--themeColor);
}

.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border-color: var(--themeColor);
}

.ant-calendar-picker:hover .ant-calendar-picker-input:not(.ant-input-disabled) {
  border-color: var(--themeColor);
}

.ant-calendar-today .ant-calendar-date {
  color: var(--themeColor);
  border-color: var(--themeColor);
}

.ant-calendar-date:active {
  background: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date:hover,
.ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date:hover {
  background: var(--themeColor);
}

.ant-input:focus {
  border-color: var(--themeColor);
}

.ant-calendar-picker:focus .ant-calendar-picker-input:not(.ant-input-disabled) {
  border-color: var(--themeColor);
}

.ant-calendar-picker-icon {
  font-size: 16px;
}

.ant-calendar-picker-clear,
.ant-calendar-picker-icon {
  width: 16px;
  height: 16px;
}

.ant-form-item-label>label {
  color: var(--text-60);
}

.ant-calendar-picker-container {
  z-index: 2490;
}

.ant-select-dropdown {
  z-index: 9999 !important;
}

.el-message-box__btns {
  column-gap: 12px;
}

.ant-modal-wrap {
  z-index: 2099;
}

.ant-modal-mask {
  z-index: 2098;
}

.ant-input::placeholder {
  color: var(--placeholder);
  opacity: 1;
  /* 需设置透明度覆盖默认样式 */
}

.ant-select::placeholder {
  color: var(--placeholder);
  opacity: 1;
  /* 需设置透明度覆盖默认样式 */
}

.ant-select-selection-placeholder {
  color: var(--placeholder);
}

.ant-select-selector {
  color: var(--input-color);
}

.ant-input {
  color: var(--input-color);
}

.ant-input[disabled] {
  color: var(--input-disabled);
}

.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  color: var(--input-disabled);
}

.ant-input-number {
  color: var(--input-color);
  background-color: var(--input-bg);
}

.dark {
  .ant-select-dropdown {
    background-color: var(--bg-f5);
    backdrop-filter: blur(10px);
  }

  .ant-select-tree-dropdown {
    background-color: var(--bg-f5);
    backdrop-filter: blur(10px);
  }

  .ant-select-item {
    color: var(--input-color);
  }

  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background-color: transparent;
    color: var(--themeColor);
  }

  .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
    background-color: transparent;
    color: var(--themeColor);
  }

  .ant-select-tree {
    color: var(--text-80);
  }

  .ant-select-tree li .ant-select-tree-node-content-wrapper {
    color: var(--text-80);
  }

  .ant-select-tree li .ant-select-tree-node-content-wrapper:hover {
    background-color: transparent;
    color: var(--themeColor);
  }

  li.ant-select-tree-treenode-disabled
    > .ant-select-tree-node-content-wrapper
    span{
      color: var(--text-80)
    }
  .ant-calendar {
    background: var(--bg-f5);
    backdrop-filter: blur(10px);

    .ant-calendar-input {
      background: transparent;

      &::placeholder {
        color: var(--placeholder)
      }
    }

    .ant-calendar-header .ant-calendar-century-select,
    .ant-calendar-header .ant-calendar-decade-select,
    .ant-calendar-header .ant-calendar-year-select,
    .ant-calendar-header .ant-calendar-month-select {
      color: var(--input-color);
    }

    .ant-calendar-date {
      color: var(--input-color);
    }

    .ant-calendar-last-month-cell .ant-calendar-date,
    .ant-calendar-next-month-btn-day .ant-calendar-date,
    .ant-calendar-last-month-cell .ant-calendar-date:hover,
    .ant-calendar-next-month-btn-day .ant-calendar-date:hover {
      color: var(--text-40);
    }

    .ant-calendar-column-header {
      color: var(--input-color);
    }

    .ant-calendar-footer {
      color: var(--input-color);
    }
  }
  .ant-input-affix-wrapper{
    color: var(--input-color);
    background-color: transparent !important;
    border-radius: 4px !important;
    &:hover{
      border-color: var(--themeColor);
    }
  }
  .ant-input-affix-wrapper:hover{
    box-shadow: none;
  }
}

.ant-layout {
  background-color: transparent;
}

.ant-spin-container::after {
  background-color: transparent;
}

.dark {
  .ant-table {
    background: transparent;
    color: var(--text-100);
  }

  .ant-table-placeholder {
    background-color: transparent;
    border-color: transparent;
  }

  .ant-table-thead>tr>th {
    border-color: var(--border);
    color: var(--text-60);
  }

  .ant-table-tbody>tr>td {
    border-color: var(--border);
  }
}

.ant-pagination-item-active {
  border-color: var(--themeColor);
  background: transparent;

  a {
    color: var(--themeColor);
  }
}

.ant-pagination-item a {
  color: var(--text-100)
}

.ant-pagination.mini .ant-pagination-item:not(.ant-pagination-item-active) {
  color: var(--text-100) !important;
}

.ant-pagination-prev,
.ant-pagination-next,
.ant-pagination-jump-prev,
.ant-pagination-jump-next {
  color: var(--text-100) !important;
}

.anticon {
  color: var(--text-100)
}

.ant-pagination-options-quick-jumper {
  color: var(--text-100)
}

.ant-pagination-options-quick-jumper input {
  background-color: var(--input-bg);
  color: var(--input-color);
}

.ant-divider {
  border-color: var(--border);
}

.ant-upload.ant-upload-select-picture-card {
  background-color: var(--header-bg);
}

.ant-upload-text {
  color: var(--text-60) !important;
}
.dark{
  .ant-select-multiple .ant-select-selection-item{
    background-color: var(--bg-f5);
  }
  .ant-popover-content{
    background: var(--bg-f5);
    backdrop-filter: blur(10px);
  }
  .ant-popover-inner{
    background-color: transparent;
  }
  .ant-popover-content{

  }
  .ant-input-number-handler-wrap{
    background-color: transparent;
  }
  .ant-calendar-input{
    color: var(--input-color);
  }
  .ant-select-tree
            li
            .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected{
              background-color: var(--header-bg);
              color: var(--themeColor);
            }
            .ant-select-disabled{
              opacity: 0.4;
            }
         
}
.ant-message{
  z-index:99999 !important;
}