<template>
  <div class="content-area p-4" v-html="htmlData"></div>
</template>
<script>
import { onBeforeMount, ref } from "vue";
import service from "@/apiService";

export default {
  setup() {
    const htmlData = ref("");
    onBeforeMount(async () => {
      let result = await service.getAgreements("service");
      htmlData.value = result.data;
    });
    return { htmlData };
  },
};
</script>
