<template>
  <div class="content-area p-4">
    <iframe src="https://devops.mingwork.com/contract.pdf" frameborder="0"></iframe>
  </div>
</template>
<script>
import { onBeforeMount, ref } from "vue";
// import service from "@/apiService/device";

export default {
  setup() {
    // const htmlData = ref("");
    onBeforeMount(async () => {
      // let result = await service.getAgreements();
      // htmlData.value = result;
    });
    // return { htmlData };
  },
};
</script>
