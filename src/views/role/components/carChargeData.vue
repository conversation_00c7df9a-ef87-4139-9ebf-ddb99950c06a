<template>
    <div class="">
        <div v-loading="loading" class="tables">
            <el-table
                v-if="tableData.length"
                border
                :data="tableData"
                style="width: 100%"
                class="tables"
            >
                <!--    -->
                <el-table-column
                    prop="date"
                    :label="$t('common_riqi')"
                    width="150"
                    align="center"
                    fixed="left"
                />
                <el-table-column
                    :label="$t('Charging amount') + '(kWh)'"
                    align="center"
                    prop="charge"
                >
                </el-table-column>
                <el-table-column
                    :label="$t('Discharging amount') + '(kWh)'"
                    align="center"
                    prop="discharge"
                >
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script setup>
import moment from 'moment'
import { watch, nextTick, ref } from 'vue'
// 日期选择⬆️
const props = defineProps({
    tableData: {
        type: Array,
        default: () => [],
    },
    type: {
        type: String,
        default: '',
    },
    loading: {
        type: Boolean,
        default: false,
    },
})
const setTableData = () => {
    if (props.tableData && props.tableData.length) {
        //
    }
}
const formatter = (e) => {
    return moment(new Date(e.time * 1000)).format('YYYY-MM-DD HH:mm:ss')
}
watch(
    () => props.tableData,
    () => {
        nextTick(() => {
            //
            setTableData()
        })
    }
)
</script>

<style lang="less" scoped>
.search {
    width: calc(~'100% - 420px');
}
</style>
