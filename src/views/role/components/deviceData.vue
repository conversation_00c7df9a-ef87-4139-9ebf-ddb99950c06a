<template>
    <div class="h-full">
        <div v-loading="loading" class="tables h-full">
            <el-table
                :data="tableData"
                style="width: 100%; height: 100%"
                class="tables h-full"
            >
                <template v-for="item in tableColumn" :key="item.value">
                    <!--    -->
                    <el-table-column
                        :prop="item.value"
                        :label="item.label"
                        :formatter="formatter"
                        align="left"
                        min-width="180"
                        v-if="item.value == 'time'"
                    />
                    <el-table-column
                        v-else-if="item.value == 's'"
                        :fixed="item.value == 'containerNo'"
                        :prop="item.value"
                        :label="item.label"
                        align="center"
                        :formatter="formatterS"
                        :minWidth="item.value == 'deviceSn' ? 220 : '140'"
                    />
                    <el-table-column
                        v-else
                        :fixed="item.value == 'containerNo'"
                        :prop="item.value"
                        :label="item.label"
                        align="center"
                        :minWidth="item.value == 'deviceSn' ? 220 : '140'"
                    />
                </template>
            </el-table>
        </div>
        <div class="text-center mt-4" v-if="activeSystem == 'car'">
            <el-button
                plain
                round
                v-if="showMore"
                :loading="loadMoreLoading"
                @click="loadMore"
                >{{ $t('jiazaigengduo') }}</el-button
            >
            <div v-if="!showMore && tableData.length">
                {{ $t('No more data') }}
            </div>
        </div>
    </div>
</template>

<script setup>
import moment from 'moment'
import { watch, nextTick, ref } from 'vue'
const activeSystem = ref(localStorage.getItem('activeSystem'))
const props = defineProps({
    tableData: {
        type: Array,
        default: () => [],
    },
    tableColumn: {
        type: Array,
        default: () => [],
    },
    loading: {
        type: Boolean,
        default: false,
    },
    showMore: {
        type: Boolean,
        default: false,
    },
    loadMoreLoading: {
        type: Boolean,
        default: false,
    },
})
const setTableData = () => {
    if (props.tableData && props.tableData.length) {
        //
    }
}
const formatter = (e) => {
    return moment(new Date(e.time * 1000)).format('YYYY-MM-DD HH:mm:ss')
}
const formatterS = (e) => {
    return e?.s?.toFixed(2)
}
const emits = defineEmits(['loadMore'])
const loadMore = () => {
    emits('loadMore')
}
watch(
    () => props.tableData,
    () => {
        nextTick(() => {
            //
            setTableData()
        })
    }
)
</script>

<style lang="less" scoped>
.search {
    width: calc(~'100% - 420px');
}
</style>
