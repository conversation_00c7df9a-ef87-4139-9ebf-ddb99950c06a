<template>
    <div class="mt-4 tabs">
        <div class="absolute right-0 top-3 z-10">
            <div v-show="activeName == 'charge'">
                <div class="flex items-center justify-end search">
                    <el-select
                        v-model="selectedStation"
                        placeholder="站点选择"
                        style="width: 120px"
                        class="ml-4 mr-4"
                        filterable
                        @change="onChangeStation"
                    >
                        <el-option
                            v-for="item in stations"
                            :key="item.stationNo"
                            :label="item.stationName"
                            :value="item.stationNo"
                        />
                    </el-select>
                    <!-- 'hour', -->
                    <date-search
                        :info="{
                            periodOptions: ['day', 'month'],
                            datePickerType: 'minute',
                            defaultPickerType: 'day',
                            minDate: startDate,
                        }"
                        v-model:dateSelect="dateSelectCharge"
                        @onChange="onSearchCharge"
                    />
                    <!-- <el-button round class="btn-hover" @click="onSearchCharge">
                        <span class="icon-box">
                            <iconSvg name="search" class="icon-default" />
                        </span>
                        <span>查询</span>
                    </el-button> -->
                    <export-button
                        :content="
                            '是否确认导出' +
                            (dateSelectCharge?.startDate ||
                                dateSelectCharge?.startMonth) +
                            '至' +
                            (dateSelectCharge?.endDate ||
                                dateSelectCharge?.endMonth) +
                            '的充放电量数据?'
                        "
                        :disabled="!chargeTableData.length"
                        @confirm="exportChargeData"
                    />
                </div>
            </div>
            <div v-show="activeName == 'profit'">
                <div class="flex items-center justify-end search">
                    <el-select
                        v-model="selectedStation"
                        placeholder="站点选择"
                        style="width: 120px"
                        class="ml-4 mr-4"
                        filterable
                        @change="onChangeStation"
                    >
                        <el-option
                            v-for="item in stations"
                            :key="item.stationNo"
                            :label="item.stationName"
                            :value="item.stationNo"
                        />
                    </el-select>
                    <date-search
                        :info="{
                            periodOptions: ['day', 'month'],
                            datePickerType: 'minute',
                            defaultPickerType: 'day',
                            minDate: startDate,
                        }"
                        v-model:dateSelect="dateSelectProfit"
                        @onChange="onSearchProfit"
                    />
                    <!-- <el-button round class="btn-hover" @click="onSearchProfit">
                        <span class="icon-box">
                            <iconSvg name="search" class="icon-default" />
                        </span>
                        <span>查询</span>
                    </el-button> -->
                    <export-button
                        :content="
                            '是否确认导出' +
                            (dateSelectProfit?.startDate ||
                                dateSelectProfit?.startMonth) +
                            '至' +
                            (dateSelectProfit?.endDate ||
                                dateSelectProfit?.endMonth) +
                            '的充放电收益数据?'
                        "
                        :disabled="!chargeFeeTableData.length"
                        @confirm="exportProfitData"
                    />
                </div>
            </div>
            <div v-show="activeName == 'netProfit'">
                <div class="flex items-center justify-end search">
                    <el-select
                        v-model="selectedStation"
                        placeholder="站点选择"
                        style="width: 120px"
                        class="ml-4 mr-4"
                        filterable
                        @change="onChangeStation"
                    >
                        <el-option
                            v-for="item in stations"
                            :key="item.stationNo"
                            :label="item.stationName"
                            :value="item.stationNo"
                        />
                    </el-select>
                    <date-search
                        :info="{
                            periodOptions: ['day', 'month'],
                            datePickerType: 'minute',
                            defaultPickerType: 'day',
                            minDate: startDate,
                        }"
                        v-model:dateSelect="dateSelectNetProfit"
                        @onChange="onSearchNetProfit"
                    />
                    <!-- <el-button
                        round
                        class="btn-hover"
                        @click="onSearchNetProfit"
                    >
                        <span class="icon-box">
                            <iconSvg name="search" class="icon-default" />
                        </span>
                        <span>查询</span>
                    </el-button> -->
                    <export-button
                        :content="
                            '是否确认导出' +
                            (dateSelectNetProfit?.startDate ||
                                dateSelectNetProfit?.startMonth) +
                            '至' +
                            (dateSelectNetProfit?.endDate ||
                                dateSelectNetProfit?.endMonth) +
                            '的净收益数据?'
                        "
                        :disabled="!netProfitTableData.length"
                        @confirm="exportNetProfitData"
                    />
                </div>
            </div>
            <div v-show="activeName == 'other'">
                <div class="flex items-center justify-end search">
                    <div class="flex items-center gap-x-3">
                        <div class="flex items-center">
                            <el-cascader
                                v-model="selectContainer"
                                :props="cascaderProps"
                                placeholder="站点选择"
                                @change="onStationChange"
                                @expand-change="onStationExpandChange"
                                class="ml-4 w-36 xl:w-40 xxl:w-52"
                            />
                        </div>

                        <div class="flex items-center">
                            <el-select
                                v-model="deviceType"
                                placeholder="设备类型"
                                style="width: 120px"
                                class=""
                                @change="onTypeChange"
                            >
                                <el-option
                                    v-for="item in deviceTypes"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </div>

                        <div class="flex items-center">
                            <el-select
                                v-model="showField"
                                placeholder="展示字段"
                                style="width: 150px"
                                class=""
                                multiple
                                :multiple-limit="30"
                                collapse-tags
                                collapse-tags-tooltip
                                @change="onShowFieldChange"
                            >
                                <el-option
                                    v-for="item in showFields"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </div>

                        <div class="flex items-center">
                            <date-search
                                :info="{
                                    periodOptions: [],
                                    datePickerType: 'day',
                                    minDate: cascaderStartDate,
                                    dayRangeLen: 2,
                                }"
                                v-model:dateSelect="rangeDate"
                                @onChange="onDateChange"
                            />
                        </div>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <export-button
                            :content="
                                '是否确认导出' +
                                rangeDate.startDate +
                                '至' +
                                rangeDate.endDate +
                                '的' +
                                deviceTypes.find(
                                    (item) => item.value == deviceType
                                )?.label +
                                '数据?'
                            "
                            :disabled="!otherTableData.length"
                            @confirm="exportOther"
                        />
                    </div>
                </div>
            </div>
        </div>

        <el-tabs v-model="activeName" @tab-change="handleTabChange">
            <template #default>
                <el-tab-pane label="充放电量" name="charge">
                    <div class="table-tabs">
                        <charge-data
                            v-model:tableData="chargeTableData"
                            :loading="tableLoading"
                        />
                    </div>
                </el-tab-pane>
                <el-tab-pane label="充放电收益" name="profit">
                    <div class="table-tabs">
                        <charge-fee-data
                            v-model:tableData="chargeFeeTableData"
                            :loading="tableLoading"
                        />
                    </div>
                </el-tab-pane>
                <el-tab-pane label="净收益" name="netProfit">
                    <div class="table-tabs">
                        <net-profit-data
                            v-model:tableData="netProfitTableData"
                            :loading="tableLoading"
                        />
                    </div>
                </el-tab-pane>
                <el-tab-pane label="其他数据" name="other">
                    <div class="table-tabs">
                        <device-data
                            v-model:tableData="otherTableData"
                            :tableColumn="otherTableColumn"
                            :loading="tableLoading"
                        />
                        <!-- 这个分页由于时间关系和数据在外面，所以先写外面了 -->
                        <div class="flex justify-end mt-4">
                            <el-pagination
                                background
                                layout="prev, pager, next"
                                :total="pageTotal"
                                v-model:current-page="pageInfo.current"
                                :page-size="pageInfo.size"
                                @change="pageChange"
                                @current-change="handleCurrentChange"
                            />
                        </div>
                    </div>
                </el-tab-pane>
            </template>
        </el-tabs>
    </div>
</template>

<script setup>
import { computed, nextTick, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import moment from 'moment'
import ChargeData from './chargeData.vue'
import ChargeFeeData from './chargeFeeData.vue'
import NetProfitData from './netProfitData.vue'
import deviceData from './deviceData.vue'
import api from '@/apiService/strategy'
import apiVpp from '@/apiService/vpp'
import apiService from '@/apiService/device'
import _cloneDeep from 'lodash/cloneDeep'
import { useStore } from 'vuex'
import DateSearch from '@/components/dateSearch.vue'
const route = useRoute()
const store = useStore()
const activeName = ref('charge')
const stations = ref([])

const exportExcel = (data, headers, fileName) => {
    // 此处当返回json文件时需要先对data进行JSON.stringify处理，其他类型文件不用做处理
    const blob = new Blob([data], {
        type: headers['content-type'],
    })
    let dom = document.createElement('a')
    let url = window.URL.createObjectURL(blob)
    dom.href = url
    dom.download = decodeURI(fileName)
    dom.style.display = 'none'
    document.body.appendChild(dom)
    dom.click()
    dom.parentNode.removeChild(dom)
    window.URL.revokeObjectURL(url)
}

//  ⬇️   ⬇️   ⬇️  charge
const selectedStation = ref('')
const dateSelectCharge = ref()
const chargeTableData = ref([])
const onSearchCharge = async () => {
    if (dateSelectCharge.value.periodType == 'hour') {
        const res = await apiService.statisticsStation24HourCharge({
            ...dateSelectCharge.value,
            stationNo: selectedStation.value,
        })
        chargeTableData.value = res.data.data.map((item) => {
            return {
                date: String(item.hour).padStart(2, '0') + ':00',
                charge: item.charge || 0,
                discharge: item.discharge || 0,
                profit: item.profit || 0,
            }
        })
        const chargeTotal = chargeTableData.value.reduce(
            (total, item) => total + item.charge,
            0
        )
        const dischargeTotal = chargeTableData.value.reduce(
            (total, item) => total + item.discharge,
            0
        )
        chargeTableData.value.push({
            date: '总计',
            charge: Math.round(chargeTotal * 100) / 100,
            discharge: Math.round(dischargeTotal * 100) / 100,
        })
    } else {
        const res = await apiVpp.statisticsDailyChargeAndProfitDetail({
            ...dateSelectCharge.value,
            stationNo: selectedStation.value,
            stationType: 'energy_storage_cabinet',
        })
        chargeTableData.value = res.data.data
    }
}
const exportChargeData = async () => {
    exportLoading.value = true
    const result = await apiService.exportStationDailyChargeDetail({
        ...dateSelectCharge.value,
        stationNo: selectedStation.value,
        stationType: 'energy_storage_cabinet',
    })
    const { data, headers } = result
    const stationName = stations.value.find(
        (item) => item.stationNo == selectedStation.value
    ).stationName
    const fileName = stationName
        ? stationName + '-' + '充放电量.xlsx'
        : '充放电量.xlsx'
    exportExcel(data, headers, fileName)
    exportLoading.value = false
}
//  ⬇️   ⬇️   ⬇️  profit
const dateSelectProfit = ref()
const chargeFeeTableData = ref([])
const onSearchProfit = async () => {
    const res = await apiVpp.statisticsDailyChargeAndProfitDetail({
        ...dateSelectProfit.value,
        stationNo: selectedStation.value,
        stationType: 'energy_storage_cabinet',
    })
    chargeFeeTableData.value = res.data.data
}

const exportProfitData = async () => {
    exportLoading.value = true
    const result = await apiService.exportStationDailyProfitDetail({
        ...dateSelectProfit.value,
        stationNo: selectedStation.value,
        stationType: 'energy_storage_cabinet',
    })
    const { data, headers } = result
    const stationName = stations.value.find(
        (item) => item.stationNo == selectedStation.value
    ).stationName
    const fileName = stationName
        ? stationName + '-' + '充放电收益.xlsx'
        : '充放电收益.xlsx'
    exportExcel(data, headers, fileName)
    exportLoading.value = false
}

//  ⬇️   ⬇️   ⬇️  netProfit
const dateSelectNetProfit = ref()
const netProfitTableData = ref([])
const onSearchNetProfit = async () => {
    const res = await apiVpp.statisticsStationDateChargeAndNetProfit({
        ...dateSelectNetProfit.value,
        stationNo: selectedStation.value,
        stationType: 'energy_storage_cabinet',
    })
    netProfitTableData.value = res.data.data
}

const exportNetProfitData = async () => {
    exportLoading.value = true
    const result = await apiService.exportStationDateChargeAndNetProfit({
        ...dateSelectNetProfit.value,
        stationNo: selectedStation.value,
        stationType: 'energy_storage_cabinet',
    })
    const { data, headers } = result
    const stationName = stations.value.find(
        (item) => item.stationNo == selectedStation.value
    ).stationName
    const fileName = stationName
        ? stationName + '-' + '净收益.xlsx'
        : '净收益.xlsx'
    exportExcel(data, headers, fileName)
    exportLoading.value = false
}
//  ⬇️   ⬇️   ⬇️  other
const cascaderProps = ref({})
const selectContainer = ref([])
const cascaderStartDate = ref('1971-01-01')
const onStationChange = async (e) => {
    console.log('[ 3 ] >', 3)
    tableLoading.value = true
    cascaderStartDate.value = stations.value.find(
        (item) => item.stationNo == e[0]
    ).operationDate
    selectedStation.value = e[0]
    if (pageInfo.value.current == 1) {
        await onSearchOther()
    } else {
        // pageInfo.value.current = 1
        handleCurrentChange(1)
        // bug
    }
    tableLoading.value = false
}
const onTypeChange = async () => {
    tableLoading.value = true
    if (pageInfo.value.current == 1) {
        await onSearchOther()
    } else {
        pageInfo.value.current = 1
    }
    tableLoading.value = false
}
const onDateChange = async () => {
    tableLoading.value = true
    if (pageInfo.value.current == 1) {
        await onSearchOther()
    } else {
        pageInfo.value.current = 1
    }
    tableLoading.value = false
}
const onStationExpandChange = (e) => {}
const deviceType = ref()
const types = ref([
    {
        label: 'pcs',
        value: 'SXBLQ',
    },
    {
        label: '堆',
        value: 'DUI',
    },
    {
        label: '簇',
        value: 'CU',
    },
    {
        label: '电芯',
        value: 'DCB',
    },
    // {
    //     label: '电表',
    //     value: 'DB',
    // },
    {
        label: '电网电表',
        value: 'gridmeter',
    },
    {
        label: '储能电表',
        value: 'bmsmeter',
    },
    {
        label: '液冷机',
        value: 'YLJ',
    },
    {
        label: '除湿机',
        value: 'KT',
    },
    {
        label: '消防',
        value: 'FIRE',
    },
    {
        label: 'DI',
        value: 'DI',
    },
])

const deviceTypes = computed(() => {
    return types.value
})
const showField = ref([])
const showFields = ref([])

// 日期选择  ⬇️
const detaultRangeDate = [
    moment().subtract(2, 'day').format('YYYY-MM-DD'),
    moment().format('YYYY-MM-DD'),
]
const rangeDate = ref({
    startDate: detaultRangeDate[0],
    endDate: detaultRangeDate[1],
})
const otherTableData = ref([])
const allOtherTableData = ref([])
const onShowFieldChange = async () => {
    const newArr = allOtherTableData.value.map((item) => {
        let newItem = { ...item }
        item.temperature &&
            typeof item.temperature == 'object' &&
            item.temperature.forEach((temp, index) => {
                newItem[`t${index + 1}`] = temp
            })
        typeof item.temperature == 'object' && delete newItem.temperature
        item.voltage &&
            typeof item.voltage == 'object' &&
            item.voltage.forEach((vol, index) => {
                newItem[`v${index + 1}`] = vol
            })
        typeof item.voltage == 'object' && delete newItem.voltage
        return newItem
    })
    // tables数据根据选中的字段进行筛选
    nextTick(() => {
        otherTableData.value = newArr.map((item) => {
            return Object.fromEntries(
                Object.entries(item).filter(([key]) =>
                    showField.value.includes(key)
                )
            )
        })
        const arr = Object.entries(filedsObj.value).map(([value, label]) => ({
            value,
            label,
        }))
        otherTableColumn.value = arr.filter((item, index) => {
            if (showField.value.includes(item.value)) return item
        })
    })
}
const pageInfo = ref({
    current: 1,
    size: 100,
})
const handleCurrentChange = (e) => {
    console.log('[ 111 ] >', 111)
    onSearchOther(true)
}
const handleSizeChange = (e) => {
    // console.log('[ e ] >', e)
}
const pageChange = () => {}
const pageTotal = ref(0)
const onSearchOther = async (flag) => {
    tableLoading.value = true
    await getEmsDataColumn(
        ['gridmeter', 'bmsmeter'].includes(deviceType.value)
            ? 'DB'
            : deviceType.value,
        flag
    )
    const arr = Object.entries(filedsObj.value).map(([value, label]) => ({
        value,
        label,
    }))
    showFields.value = arr
    showField.value = arr
        .filter((item, index) => {
            if (index < 30) return item.value
        })
        .map((item) => item.value)
    otherTableColumn.value = arr.filter((item, index) => {
        if (index < 30) return item.value
    })
    let params = {
        stationNo: selectContainer.value[0],
        containerNo: selectContainer.value[1],
        deviceType: ['gridmeter', 'bmsmeter'].includes(deviceType.value)
            ? 'DB'
            : deviceType.value,
        deviceSubType: ['gridmeter', 'bmsmeter'].includes(deviceType.value)
            ? deviceType.value
            : undefined,
        startDate: rangeDate.value.startDate,
        endDate: rangeDate.value.endDate,
        ...pageInfo.value,
    }
    const res = await apiService.getEmsRunDataLog(params)
    //   根据选择的字段进行筛选
    if (res.data.data) {
        pageTotal.value = res.data.data.total
        allOtherTableData.value = res.data.data.records
        await onShowFieldChange()
    } else {
        // pageInfo.value.current = current - 1
    }
    tableLoading.value = false
}
//  ⬆️   ⬆️   ⬆️
// 定义展示字段
const filedsObj = ref({})
const defaultContainer = ref()
const defaultDevice = ref()
const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])

const orgId = ref(
    getCompanyInfo?.value?.orgId ? getCompanyInfo.value.orgId : void 0
)
const activeSystem = computed(() => localStorage.getItem('activeSystem'))
const getdeviceList = async () => {
    if (stations.value.length >= 1) return
    const res = await api.getOrgAndSubOrgStationNameList({
        orgId: orgId.value || '1726907974555729921',
        stationType: 'energy_storage_cabinet',
    })
    stations.value = res.data.data
    if (!stations.value.length) {
        tableLoading.value = false
        return
    }
    selectedStation.value = route.query.stationNo || stations.value[0].stationNo // 默认选择第一个
    startDate.value = route.query.startDate || stations.value[0].operationDate
    // ⬇️ 赋值 级联选择框默认值
    const res1 = await apiService.getContainerList({
        stationNo: selectedStation.value,
    })
    defaultContainer.value = res1.data.data[0].containerNo
    selectContainer.value = [selectedStation.value, defaultContainer.value]
}

const otherTableColumn = ref([])
const exportLoading = ref(false)
const exportOther = async () => {
    exportLoading.value = true
    let result = await apiService.exportEmsRunDataLog({
        stationNo: selectContainer.value[0],
        containerNo: selectContainer.value[1],
        deviceType: ['gridmeter', 'bmsmeter'].includes(deviceType.value)
            ? 'DB'
            : deviceType.value,
        startDate: rangeDate.value.startDate,
        endDate: rangeDate.value.endDate,
    })
    const { data, headers } = result
    const stationName = stations.value.find(
        (item) => item.stationNo == selectContainer.value[0]
    ).stationName

    const fileName = stationName
        ? stationName +
          '-' +
          types.value.find((item) => item.value == deviceType.value).label +
          '数据.xlsx'
        : types.value.find((item) => item.value == deviceType.value).label +
          '数据.xlsx'
    exportExcel(data, headers, fileName)
    exportLoading.value = false
}
const getEmsDataColumn = async (type, flag) => {
    if (flag) return
    const res = await apiService.getEmsDataColumn({
        deviceType: type || deviceType.value,
        productType: 'energy_storage_cabinet',
    })
    filedsObj.value = res.data.data
}
const tableLoading = ref(false)

const getData = async () => {
    tableLoading.value = true
    if (activeName.value == 'charge') {
        await onSearchCharge()
    }
    if (activeName.value == 'profit') {
        await onSearchProfit()
    }
    if (activeName.value == 'netProfit') {
        await onSearchNetProfit()
    }
    if (activeName.value == 'other') {
        deviceType.value = deviceTypes.value[0].value
        cascaderProps.value = {
            lazy: true,
            async lazyLoad(node, resolve) {
                const page = {
                    orgId: orgId.value || '1726907974555729921',
                }
                if (node.level === 0) {
                    // const res = await api.getOrgAndSubOrgStationNameList(page)
                    // const data = res.data.data.map((item) => {
                    //     return {
                    //         value: item.stationNo,
                    //         label: item.stationName,
                    //         leaf: false,
                    //     }
                    // })
                    const data = stations.value.map((item) => {
                        return {
                            value: item.stationNo,
                            label: item.stationName,
                            leaf: false,
                        }
                    })
                    // const { level } = node
                    resolve(data)
                } else {
                    const res = await apiService.getContainerList({
                        stationNo: node.data.value,
                    })
                    const data = res.data.data.map((item, index) => {
                        let no = item.containerNo.split('CON')[1]
                        return {
                            value: item.containerNo,
                            label: Number(no) + '号机柜',
                            leaf: true,
                        }
                    })
                    resolve(data)
                }
            },
        }
        if (!stations.value.length) {
            tableLoading.value = false
            return
        }
        defaultDevice.value =
            selectContainer.value[0] ||
            route.query.stationNo ||
            stations.value[0].stationNo
        cascaderStartDate.value = stations.value.find(
            (item) => item.stationNo == defaultDevice.value
        ).operationDate
        await onSearchOther()
    }
    tableLoading.value = false
}
const handleTabChange = async (e) => {
    //
    // window.scrollTo(0, 0)
    if (activeName.value != 'other') {
        await getdeviceList()
    }
    nextTick(() => {
        getData()
    })
}
const startDate = ref('1971-01-01')
const onChangeStation = async (e) => {
    tableLoading.value = true
    startDate.value = stations.value.find(
        (item) => item.stationNo === e
    ).operationDate
    const res = await apiService.getContainerList({
        stationNo: e,
    })
    defaultContainer.value = res.data.data[0].containerNo
    selectContainer.value = [e, defaultContainer.value]
    //
    activeName.value == 'charge' && (await onSearchCharge())
    activeName.value == 'profit' && (await onSearchProfit())
    activeName.value == 'netProfit' && (await onSearchNetProfit())
    tableLoading.value = false
}
onMounted(async () => {
    //
    if (route.query.type) {
        activeName.value = route.query.type
    }
    await getdeviceList()
    await getData()
})
</script>

<style lang="less" scoped>
.tabs {
    position: relative;

    &::before {
        display: block;
        content: '';
        border-bottom: 4px solid rgba(34, 34, 34, 0.04);
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        position: absolute;
        left: 278px;
        bottom: 100%;
    }
}

.table-tabs {
    // height: calc(~'100vh - 320px');
}

:deep(.el-tabs__nav) {
    padding: 8px;
    // background: rgba(34, 34, 34, 0.04);
    border: 1px solid rgba(34, 34, 34, 0.08);
    border-radius: 8px;
}

:deep(.el-tabs__nav-wrap:after) {
    display: none;
}

:deep(.el-tabs__item) {
    color: rgba(34, 34, 34, 0.8);
    font-weight: normal;
}

:deep(.el-tabs__item.is-active) {
    color: var(--themeColor);
    // &::after {
    //     display: block;
    //     border: 1px solid #ededef;
    //     content: '';
    //     width: 18px;
    //     height: 18px;
    //     background: #fff;
    //     position: absolute;
    //     left: 0;
    //     right: 0;
    //     margin: auto;
    //     top: -12px;
    //     box-shadow: 0 -2px -4px 0 #f0f0f0;
    //     transform: rotate(135deg);
    //     z-index: 99;
    // }
}

:deep(.el-tabs__item.is-active) {
    background: #fff;
    padding: 0 20px;
    border-radius: 8px;
    text-align: center;
    font-weight: 500;
}

:deep(.el-tabs--bottom) {
    .el-tabs__item.is-bottom:last-child {
        padding: 0 20px;
    }

    .el-tabs__item.is-top:last-child {
        padding: 0 20px;
    }

    .el-tabs__item.is-bottom:nth-child(2) {
        padding: 0 20px;
    }

    .el-tabs__item.is-top:nth-child(2) {
        padding: 0 20px;
    }
}

:deep(.el-tabs--top) {
    .el-tabs__item.is-bottom:last-child {
        padding: 0 20px;
    }

    .el-tabs__item.is-top:last-child {
        padding: 0 20px;
    }

    .el-tabs__item.is-bottom:nth-child(2) {
        padding: 0 20px;
    }

    .el-tabs__item.is-top:nth-child(2) {
        padding: 0 20px;
    }
}

:deep(.el-tabs__active-bar) {
    display: none;
}
</style>
<style lang="less">
.tables {
    height: calc(~'100vh - 320px');
}
</style>
