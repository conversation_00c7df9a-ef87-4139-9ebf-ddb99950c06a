<template>
    <div class="map" id="mapContainer">
        <div class="select-box">
            <a-select
                v-model:value="searchValue"
                :placeholder="$t('placeholder_sousuodidian')"
                style="width: 100%"
                :filter-option="false"
                :not-found-content="fetching ? undefined : null"
                @search="fetchUser"
                show-search
                label-in-value
                @change="selectChange"
                class="select-content"
            >
                <template v-if="fetching" #notFoundContent>
                    <a-spin size="small" />
                </template>
                <template #suffixIcon>
                    <SearchOutlined />
                </template>
                <a-select-option
                    v-for="item in list"
                    :key="item.value"
                    class="select-option"
                >
                    <div class="option-title">{{ item.name }}</div>
                    <div class="option-content">{{ item.label }}</div>
                </a-select-option>
            </a-select>
        </div>
    </div>
</template>

<script setup>
import AMapLoader from '@amap/amap-jsapi-loader'
import { message } from 'ant-design-vue'
import { ref, onMounted, watch } from 'vue'
import { debounce } from 'lodash-es'
const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
})
watch(
    () => props.visible,
    (val) => {
        if (val) {
            mapInit()
        } else {
            clearMap()
        }
    }
)

const clearMap = () => {
    map && map.destroy()
}
// const map = ref(null)
let map
const aAMap = ref(null)
const markerArr = []
const list = ref([])
let lastFetchId = 0
const fetching = ref(false)
const searchValue = ref(void 0)
const emits = defineEmits(['queryClick'])
const mapLoad = () => {
    return new Promise((resovle, resject) => {
        new AMapLoader.load({
            // key: '83e3b6af5f77f6ad205c468c7cfac8f9',
            key: '2cbc3120e17cc96ecb0589e8b4c990ce',
            version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
            plugins: [
                'AMap.ControlBar',
                'AMap.ToolBar',
                'AMap.Geolocation',
                'AMap.AutoComplete',
                'AMap.PlaceSearch',
            ],
        })
            .then((AMap) => {
                resovle(AMap)
            })
            .catch((e) => {
                resject(e)
            })
    })
}
const isDark = ref(localStorage.getItem('cn-sys-theme') === 'dark')
const mapInit = async () => {
    try {
        const AMap = await mapLoad()
        aAMap.value = AMap
        let mapStyle = isDark.value
            ? 'amap://styles/darkblue'
            : 'amap://styles/normal'
        map = new AMap.Map('mapContainer', {
            //设置地图容器id
            viewMode: '2D', //是否为3D地图模式
            zoom: 15, //初始化地图级别
            mapStyle: mapStyle,
        })
        geoLocation()
    } catch (error) {
        //
    }
}

const geoLocation = () => {
    map.remove(markerArr)
    map.plugin('AMap.Geolocation', function () {
        const geolocation = new aAMap.value.Geolocation({
            enableHighAccuracy: true, // 是否使用高精度定位，默认：true
            timeout: 10000, // 设置定位超时时间，默认：无穷大
            offset: [10, 10], // 定位按钮的停靠位置的偏移量
            zoomToAccuracy: true, //  定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
            position: 'RB', //  定位按钮的排放位置,  RB表示右下
            panToLocation: true,
            extensions: 'all',
            showMarker: false,
            showCircle: false,
        })
        geolocation.getCurrentPosition()
        map.addControl(geolocation)
        aAMap.value.Event.addListener(geolocation, 'complete', onComplete) //返回定位信息
        aAMap.value.Event.addListener(geolocation, 'error', onError) //返回定位出错信息

        function onComplete(data) {
            // data是具体的定位信息
            const { info, position } = data
            if (info === 'SUCCESS') {
                if (position.lng && position.lat) {
                    const marker = new aAMap.value.Marker({
                        position: new aAMap.value.LngLat(
                            position.lng,
                            position.lat
                        ),
                    })
                    map.add(marker)
                    markerArr.push(marker)
                    montageAddss([position.lng, position.lat])
                }
            }
        }

        function onError(data) {
            // message.error('定位失败,请重试')
        }
    })
}

const mapSearch = (value, fetchId) => {
    fetching.value = true
    aAMap.value.plugin("'AMap.PlaceSearch", function () {
        const PlaceSearchOptions = {
            //设置PlaceSearch属性
            city: '全国', //城市
            type: '', //数据类别
            pageSize: 20, //每页结果数,默认10
            pageIndex: 1, //请求页码，默认1
            extensions: 'all',
        }
        if (fetchId !== lastFetchId) {
            return
        }
        const MSearch = new aAMap.value.PlaceSearch(PlaceSearchOptions) //构造PlaceSearch类
        //关键字查询
        MSearch.search(value)
        aAMap.value.Event.addListener(MSearch, 'complete', function (result) {
            fetching.value = false
            list.value = []
            const {
                info,
                poiList: { pois },
            } = result
            if (info === 'OK') {
                list.value =
                    pois?.map((item) => {
                        return {
                            label:
                                item.pname == item.cityname
                                    ? item.cityname + item.adname + item.address
                                    : item.pname +
                                      item.cityname +
                                      item.adname +
                                      item.address,
                            value: item.id,
                            ...item,
                        }
                    }) || []
            }
        }) //返回结果
    })
}

const montageAddss = (point, reduis = 1000, city = '全国', value = '') => {
    new aAMap.value.plugin('AMap.PlaceSearch', function () {
        const placeSearch = new aAMap.value.PlaceSearch({
            //city 指定搜索所在城市，支持传入格式有：城市名、citycode 和 adcode
            city: city,
            type: '公司企业|地名地址信息', //数据类别
            pageSize: 50, //每页结果数,默认10
            pageIndex: 1, //请求页码，默认1
            extensions: 'all',
        })
        const cpoint = point //中心点坐标
        placeSearch.searchNearBy(
            value,
            cpoint,
            reduis,
            function (status, result) {
                //查询成功时，result 即对应匹配的 POI 信息
                const {
                    info,
                    poiList: { pois },
                } = result
                if (info === 'OK') {
                    list.value =
                        pois?.map((item) => {
                            return {
                                label:
                                    item.pname == item.cityname
                                        ? item.cityname +
                                          item.adname +
                                          item.address
                                        : item.pname +
                                          item.cityname +
                                          item.adname +
                                          item.address,
                                value: item.id,
                                ...item,
                            }
                        }) || []
                }
            }
        )
    })
}

const fetchUser = debounce((value) => {
    lastFetchId += 1
    const fetchId = lastFetchId
    if (!value) {
        fetching.value = false
    }
    value && mapSearch(value, fetchId)
}, 300)

const selectChange = (val) => {
    const data = list.value.filter((item) => item.id == val.key)[0]
    data && emits('queryClick', data)
}

onMounted(() => {
    mapInit()
})
</script>

<style scoped lang="less">
.map {
    height: 500px;
    width: 802px;
    position: relative;
    .select-box {
        position: absolute;
        top: 30px;
        left: 30px;
        width: 400px;
        z-index: 99;
        :deep(.ant-select) {
            font-size: 14px;
            &:hover {
                .ant-select-selector {
                    border-color: var(--themeColor);
                }
            }
        }

        :deep(.ant-select-selector) {
            height: 32px;
            padding: 0 11px;

            .ant-select-selection-search-input {
                height: 30px;
            }

            .ant-select-selection-item {
                line-height: 30px;
                padding-right: 18px;
            }
        }

        :deep(.ant-select-arrow) {
            right: 11px;
            width: 12px;
            height: 12px;
            margin-top: -6px;
            font-size: 12px;
        }

        :deep(.ant-select-single) {
            &.ant-select-show-arrow {
                .ant-select-show-arrow {
                    margin-right: 18px;
                }
            }

            .ant-select-selector {
                .ant-select-selection-placeholder {
                    line-height: 30px;
                }
            }
        }

        :deep(.ant-select-focused) {
            .ant-select-selector {
                border-color: var(--themeColor) !important;
                box-shadow: none !important;
            }
        }
    }
}
</style>

<style lang="less">
.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background-color: #f5f5f5;
}

.ant-select-item {
    min-height: 32px;
    padding: 5px 12px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: normal;
    font-size: 14px;
    line-height: 22px;
    .option-title {
        font-size: 14px;
        width: 100%;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 溢出部分隐藏 */
        text-overflow: ellipsis; /* 显示省略号 */
    }

    .option-content {
        font-size: 12px;
        width: 100%;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 溢出部分隐藏 */
        text-overflow: ellipsis; /* 显示省略号 */
    }
}
</style>
