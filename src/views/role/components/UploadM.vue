<template>
    <el-upload
        class="upload-demo"
        :action="url + '/file/uploadFile'"
        list-type="picture-card"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :multiple="true"
        :limit="3"
        :data="getUploadData"
        :file-list="fileList"
        :before-upload="beforeUpload"
        @change="updateFileList"
        :headers="{
            Authorization: `Bearer ${token}`,
        }"
    >
        <i class="el-icon-plus"></i>
    </el-upload>
    <el-dialog v-model="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, computed } from 'vue'
import store from '@/store'
const token = store.getters['user/getNewToken']

const url =
    process.env.NODE_ENV == 'production'
        ? 'https://ems-api.ssnj.com'
        : 'https://ems-api-beta.ssnj.com'

const fileList = ref(props.imgList || [])
const dialogImageUrl = ref('')
const dialogVisible = ref(false)

const handlePreview = (file) => {
    dialogImageUrl.value = file.url
    dialogVisible.value = true
}

const handleRemove = (file, fileList) => {
    console.log(file, fileList)
}
const dataProps = computed(() => {
    return props.data
})
const beforeUpload = (file) => {
    let isJpgOrPng = ''
    if (dataProps.value.scene == 'icon') {
        isJpgOrPng = file.type === 'image/png'
    } else {
        isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
    }
    if (!isJpgOrPng) {
        // message.error(
        //     dataProps.value.scene == 'icon'
        //         ? '图片格式错误，图片格式应该为.png'
        //         : '图片格式错误，图片格式应该为.png或者.jpeg'
        // )
    }
    const isLt1M = file.size / 1024 / 1024 <= 1
    if (!isLt1M) {
        // message.error('图片大小不能超过1M')
    }
    return isJpgOrPng && isLt1M
}

const getUploadData = () => {
    return {
        scene: dataProps.value.scene,
        // 其他需要传递的数据
    }
}

const updateFileList = (newFileList, a) => {
    console.log('[ newFileList ] >', newFileList, a)
    emit('update:imgList', a)
}

watch(
    () => props.imgList,
    (newVal) => {
        fileList.value = newVal
    }
)
</script>
