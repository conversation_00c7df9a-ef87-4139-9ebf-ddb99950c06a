<template>
    <div class="">
        <div v-loading="loading" class="tables">
            <el-table
                v-if="tableData.length"
                :data="tableData"
                style="width: 100%"
                class="tables"
            >
                <!--    -->
                <el-table-column
                    prop="date"
                    label="日期"
                    width="150"
                    align="center"
                    fixed="left"
                />
                <el-table-column label="净利润" align="center">
                    <el-table-column
                        prop="netProfit"
                        label="净利润金额(元)"
                        width="140"
                        align="center"
                    />
                </el-table-column>
                <el-table-column label="收入" align="center">
                    <el-table-column
                        prop="charge"
                        label="充电量(kWh)"
                        align="center"
                    />
                    <el-table-column
                        prop="chargeFee"
                        label="充电成本(元)"
                        align="center"
                    />
                    <el-table-column
                        prop="discharge"
                        label="放电量(kWh)"
                        align="center"
                    />
                    <el-table-column
                        prop="dischargeFee"
                        label="放电成本(元)"
                        align="center"
                    />
                    <el-table-column
                        prop="arbitrageProfit"
                        label="充放电收益(元)"
                        width="130"
                        align="center"
                    />
                    <el-table-column
                        prop="demandProfit"
                        label="需求响应收益(元)"
                        width="140"
                        align="center"
                    />
                    <el-table-column
                        prop="profit"
                        label="收益总计(元)"
                        align="center"
                    />
                </el-table-column>
                <el-table-column label="成本" align="center">
                    <el-table-column
                        prop="operatingCost"
                        label="运营成本(元)"
                        align="center"
                    />
                    <el-table-column
                        prop="depreciationCost"
                        label="设备折旧成本(元)"
                        width="140"
                        align="center"
                    />
                    <el-table-column
                        prop="totalCost"
                        label="成本总计(元)"
                        width="120"
                        align="center"
                    />
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script setup>
import moment from 'moment'
import { watch, nextTick, ref } from 'vue'
// 日期选择⬆️
const props = defineProps({
    tableData: {
        type: Array,
        default: () => [],
    },
    type: {
        type: String,
        default: '',
    },
    loading: {
        type: Boolean,
        default: false,
    },
})
const setTableData = () => {
    if (props.tableData && props.tableData.length) {
        //
    }
}
const formatter = (e) => {
    return moment(new Date(e.time * 1000)).format('YYYY-MM-DD HH:mm:ss')
}
watch(
    () => props.tableData,
    () => {
        nextTick(() => {
            //
            setTableData()
        })
    }
)
</script>

<style lang="less" scoped>
.search {
    width: calc(~'100% - 420px');
}
</style>
