<template>
    <div class="h-full">
        <div v-loading="loading" class="tables h-full">
            <el-table
                v-if="tableData.length"
                :data="tableData"
                style="width: 100%; height: 100%"
                class="tables"
            >
                <!--    -->
                <el-table-column
                    prop="date"
                    :label="$t('common_riqi')"
                    width="150"
                    align="center"
                    fixed="left"
                />
                <el-table-column
                    :label="$t('Charging amount') + '(kWh)'"
                    align="center"
                >
                    <el-table-column
                        prop="jdCharge"
                        :label="$t('Peak')"
                        align="center"
                    />
                    <el-table-column
                        prop="fdCharge"
                        :label="$t('OnPeak')"
                        align="center"
                    />
                    <el-table-column
                        prop="pdCharge"
                        :label="$t('Flat')"
                        align="center"
                    />
                    <el-table-column
                        prop="gdCharge"
                        :label="$t('Valley')"
                        align="center"
                    />
                    <!-- <el-table-column
                        prop="gdCharge"
                        :label="深谷"
                        width="120"
                        align="center"
                    /> -->
                    <el-table-column
                        prop="charge"
                        :label="$t('station_zongji')"
                        align="center"
                    />
                </el-table-column>
                <el-table-column
                    :label="$t('Discharging amount') + '(kWh)'"
                    align="center"
                >
                    <el-table-column
                        prop="jdDischarge"
                        :label="$t('Peak')"
                        align="center"
                    />
                    <el-table-column
                        prop="fdDischarge"
                        :label="$t('OnPeak')"
                        align="center"
                    />
                    <el-table-column
                        prop="pdDischarge"
                        :label="$t('Flat')"
                        align="center"
                    />
                    <el-table-column
                        prop="gdDischarge"
                        :label="$t('Valley')"
                        align="center"
                    />
                    <!-- <el-table-column
                        prop="gdDischarge"
                        :label="深谷"
                        width="120"
                        align="center"
                    /> -->
                    <el-table-column
                        prop="discharge"
                        :label="$t('station_zongji')"
                        align="center"
                    />
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script setup>
import moment from 'moment'
import { watch, nextTick, ref } from 'vue'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
// 日期选择⬆️
const props = defineProps({
    tableData: {
        type: Array,
        default: () => [],
    },
    type: {
        type: String,
        default: '',
    },
    loading: {
        type: Boolean,
        default: false,
    },
})
const setTableData = () => {
    if (props.tableData && props.tableData.length) {
        //
    }
}
const formatter = (e) => {
    return moment(new Date(e.time * 1000)).format('YYYY-MM-DD HH:mm:ss')
}
watch(
    () => props.tableData,
    () => {
        nextTick(() => {
            //
            setTableData()
        })
    }
)
</script>

<style lang="less" scoped>
.search {
    width: calc(~'100% - 420px');
}
</style>
