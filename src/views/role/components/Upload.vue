<template>
    <a-upload
        list-type="picture-card"
        :class="customClassName"
        :show-upload-list="false"
        :action="url + '/file/uploadFile'"
        :before-upload="beforeUpload"
        @change="handleChange"
        :headers="{
            Authorization: `Bearer ${token}`,
        }"
        :data="dataProps"
        v-bind="$attrs"
    >
        <div v-if="imageUrl" class="img-box">
            <img :src="imageUrl" alt="avatar" />
            <span class="close-box" @click.stop="delImg">
                <CloseOutlined />
            </span>
        </div>
        <div v-else>
            <loading-outlined v-if="loading"></loading-outlined>
            <plus-outlined v-else></plus-outlined>
            <div class="ant-upload-text">
                {{ $t('upload_shangchuanwenjian') }}
            </div>
        </div>
    </a-upload>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { ref, computed, useAttrs, watch } from 'vue'
import store from '@/store'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
function getBase64(img, callback) {
    const reader = new FileReader()
    reader.addEventListener('load', () => callback(reader.result))
    reader.readAsDataURL(img)
}

const $attrs = useAttrs()

const emit = defineEmits(['success', 'delImgFile'])

const props = defineProps({
    data: { type: Object },
    className: { type: String },
    fileList: { type: Array },
})
// eslint-disable-next-line vue/no-setup-props-destructure
const dataProps = computed(() => {
    return props.data
})

const customClassName = computed(() => {
    return `avatar-uploader ${props.className}`
})

const loading = ref(false)
const imageUrl = ref('')

const handleChange = (info) => {
    if (info.file.status === 'uploading') {
        loading.value = true
        return
    }
    if (info.file.status === 'done') {
        info.scene = dataProps.value.scene
        emit('success', info)
        getBase64(info.file.originFileObj, (base64Url) => {
            imageUrl.value = base64Url
            loading.value = false
        })
    }
    if (info.file.status === 'error') {
        loading.value = false
        message.error(t('Upload Failed'))
    }
}

const beforeUpload = (file) => {
    let isJpgOrPng = ''
    if (dataProps.value.scene == 'icon') {
        isJpgOrPng = file.type === 'image/png'
    } else {
        isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
    }

    if (!isJpgOrPng) {
        message.error(
            dataProps.value.scene == 'icon'
                ? t('upload_tips002') + '.png'
                : t('upload_tips003')
                      .replace('s%', '.png')
                      .replace('s%', 'jpeg')
        )
    }
    const isLt1M = file.size / 1024 / 1024 <= 1
    if (!isLt1M) {
        message.error(t('upload_tips001') + '1M')
    }
    return isJpgOrPng && isLt1M
}

const delImg = () => {
    imageUrl.value = void 0
    emit('delImgFile', dataProps.value.scene)
}

const url =
    process.env.NODE_ENV == 'production'
        ? 'https://ems-api.ssnj.com'
        : 'https://ems-api-beta.ssnj.com'
const token = store.getters['user/getNewToken']

watch(
    () => props.fileList,
    (value) => {
        if (value && value.length > 0) {
            const [{ url, old }] = value
            if (!old) {
                imageUrl.value = url
            }
        } else {
            imageUrl.value = void 0
        }
    }
)
</script>
<style scoped lang="less">
.img-box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .close-box {
        position: absolute;
        top: 6px;
        right: 6px;
        font-size: 8px;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        border: 1px solid #d9d9d9;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 99;
    }
}
</style>
<style>
.avatar-uploader > .ant-upload {
    width: 128px;
    height: 128px;
}

.ant-upload-select-picture-card i {
    font-size: 20px;
    color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
    font-size: 14px;
}
</style>
