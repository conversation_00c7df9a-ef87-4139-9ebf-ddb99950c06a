<template>
    <div class="gauge-test-container">
        <h2>Gauge组件测试</h2>
        
        <div class="controls">
            <label>SOC值: {{ currentSoc }}%</label>
            <input 
                type="range" 
                min="0" 
                max="100" 
                v-model="currentSoc" 
                class="slider"
            />
        </div>
        
        <div class="gauge-wrapper">
            <Gauge :soc="Number(currentSoc)" />
        </div>
        
        <div class="test-buttons">
            <button @click="setSoc(0)">0%</button>
            <button @click="setSoc(5)">5% (红色)</button>
            <button @click="setSoc(20)">20% (橙色)</button>
            <button @click="setSoc(50)">50% (青色)</button>
            <button @click="setSoc(80)">80% (青色)</button>
            <button @click="setSoc(100)">100%</button>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import Gauge from './gauge.vue'

const currentSoc = ref(50)

const setSoc = (value) => {
    currentSoc.value = value
}
</script>

<style lang="less" scoped>
.gauge-test-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
    
    h2 {
        text-align: center;
        margin-bottom: 30px;
        color: #333;
    }
    
    .controls {
        margin-bottom: 30px;
        text-align: center;
        
        label {
            display: block;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: bold;
        }
        
        .slider {
            width: 300px;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            
            &::-webkit-slider-thumb {
                appearance: none;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: #3EDACD;
                cursor: pointer;
            }
            
            &::-moz-range-thumb {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: #3EDACD;
                cursor: pointer;
                border: none;
            }
        }
    }
    
    .gauge-wrapper {
        width: 400px;
        height: 300px;
        margin: 0 auto 30px;
        border: 1px solid #eee;
        border-radius: 8px;
        background: #f9f9f9;
    }
    
    .test-buttons {
        display: flex;
        justify-content: center;
        gap: 10px;
        flex-wrap: wrap;
        
        button {
            padding: 8px 16px;
            border: 1px solid #3EDACD;
            background: white;
            color: #3EDACD;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            
            &:hover {
                background: #3EDACD;
                color: white;
            }
        }
    }
}
</style>
