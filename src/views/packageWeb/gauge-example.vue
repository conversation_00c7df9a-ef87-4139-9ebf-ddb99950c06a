<template>
    <div class="gauge-example">
        <h2>Gauge组件使用示例</h2>
        
        <!-- 单个gauge组件 -->
        <div class="single-gauge">
            <h3>单个电池SOC显示</h3>
            <div class="gauge-item">
                <Gauge :soc="batterySOC" />
                <p>当前SOC: {{ batterySOC }}%</p>
            </div>
        </div>
        
        <!-- 多个gauge组件 -->
        <div class="multiple-gauges">
            <h3>多个电池组SOC显示</h3>
            <div class="gauge-grid">
                <div 
                    v-for="(battery, index) in batteries" 
                    :key="index"
                    class="gauge-item"
                >
                    <Gauge :soc="battery.soc" />
                    <p>{{ battery.name }}: {{ battery.soc }}%</p>
                </div>
            </div>
        </div>
        
        <!-- 控制面板 -->
        <div class="control-panel">
            <h3>控制面板</h3>
            <div class="controls">
                <div class="control-group">
                    <label>主电池SOC: {{ batterySOC }}%</label>
                    <input 
                        type="range" 
                        min="0" 
                        max="100" 
                        v-model="batterySOC" 
                        class="slider"
                    />
                </div>
                
                <div class="buttons">
                    <button @click="simulateCharging">模拟充电</button>
                    <button @click="simulateDischarging">模拟放电</button>
                    <button @click="resetAll">重置所有</button>
                    <button @click="randomizeAll">随机化所有</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import Gauge from './gauge.vue'

// 主电池SOC
const batterySOC = ref(50)

// 多个电池组数据
const batteries = ref([
    { name: '电池组1', soc: 75 },
    { name: '电池组2', soc: 45 },
    { name: '电池组3', soc: 88 },
    { name: '电池组4', soc: 12 },
])

let chargingInterval = null
let dischargingInterval = null

// 模拟充电
const simulateCharging = () => {
    clearIntervals()
    chargingInterval = setInterval(() => {
        if (batterySOC.value < 100) {
            batterySOC.value += 1
            // 同时更新其他电池组
            batteries.value.forEach(battery => {
                if (battery.soc < 100) {
                    battery.soc = Math.min(100, battery.soc + Math.random() * 2)
                }
            })
        } else {
            clearInterval(chargingInterval)
        }
    }, 200)
}

// 模拟放电
const simulateDischarging = () => {
    clearIntervals()
    dischargingInterval = setInterval(() => {
        if (batterySOC.value > 0) {
            batterySOC.value -= 1
            // 同时更新其他电池组
            batteries.value.forEach(battery => {
                if (battery.soc > 0) {
                    battery.soc = Math.max(0, battery.soc - Math.random() * 2)
                }
            })
        } else {
            clearInterval(dischargingInterval)
        }
    }, 200)
}

// 重置所有
const resetAll = () => {
    clearIntervals()
    batterySOC.value = 50
    batteries.value.forEach((battery, index) => {
        battery.soc = [75, 45, 88, 12][index]
    })
}

// 随机化所有
const randomizeAll = () => {
    clearIntervals()
    batterySOC.value = Math.floor(Math.random() * 101)
    batteries.value.forEach(battery => {
        battery.soc = Math.floor(Math.random() * 101)
    })
}

// 清除所有定时器
const clearIntervals = () => {
    if (chargingInterval) {
        clearInterval(chargingInterval)
        chargingInterval = null
    }
    if (dischargingInterval) {
        clearInterval(dischargingInterval)
        dischargingInterval = null
    }
}

onUnmounted(() => {
    clearIntervals()
})
</script>

<style lang="less" scoped>
.gauge-example {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    
    h2, h3 {
        color: #333;
        margin-bottom: 20px;
    }
    
    .single-gauge {
        margin-bottom: 40px;
        
        .gauge-item {
            display: flex;
            align-items: center;
            gap: 20px;
            
            > div {
                width: 300px;
                height: 300px;
            }
            
            p {
                font-size: 18px;
                font-weight: bold;
                color: #666;
            }
        }
    }
    
    .multiple-gauges {
        margin-bottom: 40px;
        
        .gauge-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            
            .gauge-item {
                text-align: center;
                padding: 15px;
                border: 1px solid #eee;
                border-radius: 8px;
                background: #f9f9f9;
                
                > div {
                    width: 100%;
                    height: 200px;
                    margin-bottom: 10px;
                }
                
                p {
                    margin: 0;
                    font-weight: bold;
                    color: #666;
                }
            }
        }
    }
    
    .control-panel {
        border-top: 2px solid #eee;
        padding-top: 20px;
        
        .controls {
            display: flex;
            flex-direction: column;
            gap: 20px;
            
            .control-group {
                label {
                    display: block;
                    margin-bottom: 10px;
                    font-weight: bold;
                }
                
                .slider {
                    width: 300px;
                    height: 6px;
                    border-radius: 3px;
                    background: #ddd;
                    outline: none;
                    
                    &::-webkit-slider-thumb {
                        appearance: none;
                        width: 20px;
                        height: 20px;
                        border-radius: 50%;
                        background: #3EDACD;
                        cursor: pointer;
                    }
                    
                    &::-moz-range-thumb {
                        width: 20px;
                        height: 20px;
                        border-radius: 50%;
                        background: #3EDACD;
                        cursor: pointer;
                        border: none;
                    }
                }
            }
            
            .buttons {
                display: flex;
                gap: 10px;
                flex-wrap: wrap;
                
                button {
                    padding: 10px 20px;
                    border: 1px solid #3EDACD;
                    background: white;
                    color: #3EDACD;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: all 0.3s;
                    
                    &:hover {
                        background: #3EDACD;
                        color: white;
                    }
                }
            }
        }
    }
}
</style>
