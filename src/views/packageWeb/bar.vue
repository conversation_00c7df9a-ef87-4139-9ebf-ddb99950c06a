<template>
    <div class="bar-chart-container">
        <!-- 顶部控制栏 -->
        <div class="chart-header">
            <!-- 左侧视图切换按钮 -->
            <div class="view-switch">
                <div class="switch-item" @click="switchView(currentView)">
                    <span class="switch-text">{{
                        currentView === 'capacity'
                            ? $t('Charge and discharge capacity')
                            : $t('Average running time')
                    }}</span>
                    <iconSvg
                        name="toggle"
                        class="icon-default w-4 h-4 toggle-icon"
                    />
                </div>
            </div>

            <!-- 右侧条件筛选下拉框 -->
            <div class="filter-dropdown">
                <el-select
                    v-model="currentOption"
                    :placeholder="$t('Device Type')"
                    style="width: 120px"
                    @change="onPeriodChange"
                >
                    <el-option
                        v-for="item in periodOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </div>
        </div>

        <!-- 图表容器 -->
        <div
            ref="chartContainer"
            class="chart-content"
            :style="{ height: '200px' }"
        ></div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { getChargeOption } from '@/views/device/const'
import { useI18n } from 'vue-i18n'
import * as echarts from 'echarts'
import _cloneDeep from 'lodash/cloneDeep'
import dayjs from 'dayjs'
import powerApi from '@/apiService/h5'

const { t, locale } = useI18n()

const props = defineProps({
    // 数据数组
    data: {
        type: Array,
        default: () => [],
    },
    // 图表高度
    height: {
        type: Number,
        default: 200,
    },
    // 图表宽度
    width: {
        type: [String, Number],
        default: '100%',
    },
})

const chartContainer = ref()
let myChart = null

// 当前视图类型
const currentView = ref('duration') // 'capacity' | 'duration'

// 当前时间选项
const currentOption = ref('week') // 'week' | 'month' | 'year'

// 时间选项配置
const periodOptions = ref([
    { label: t('Last Week'), value: 'week' },
    { label: t('Last 30 Days'), value: 'month' },
    { label: t('Last Year'), value: 'year' },
])

// 模拟数据
const statsPowerBattDailyUsageData = ref([])

// 计算时间参数
const getTimeParams = () => {
    if (currentOption.value === 'week') {
        return {
            startDate: dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
            endDate: dayjs().format('YYYY-MM-DD'),
            periodType: 'day',
        }
    } else if (currentOption.value === 'month') {
        return {
            startDate: dayjs().subtract(29, 'day').format('YYYY-MM-DD'),
            endDate: dayjs().format('YYYY-MM-DD'),
            periodType: 'day',
        }
    } else {
        return {
            startMonth: dayjs().subtract(11, 'month').format('YYYY-MM'),
            endMonth: dayjs().format('YYYY-MM'),
            periodType: 'month',
        }
    }
}

// 当前时间参数
const currentTimeParams = computed(() => getTimeParams())

// 切换视图
const switchView = (view) => {
    if (view == 'capacity') {
        currentView.value = 'duration'
    } else {
        currentView.value = 'capacity'
    }
    updateChart()
}

// 时间选项变化
const onPeriodChange = () => {
    console.log('时间选项变化:', currentOption.value, currentTimeParams.value)
    // 这里可以调用API获取新数据
    getChartData()
}

// 更新图表
const updateChart = () => {
    console.log(123)
    if (!chartContainer.value || !statsPowerBattDailyUsageData.value?.length) {
        return
    }
    const data = statsPowerBattDailyUsageData.value
    let x = data.map((item) => item.date)
    // 根据时间类型处理X轴数据
    if (currentTimeParams.value.periodType === 'day') {
        x = data.map((item) => dayjs(item.date).format('MM/DD')) // 显示月-日
    } else if (currentTimeParams.value.periodType === 'month') {
        x = data.map((item) => dayjs(item.date).format('YYYY/MM')) // 显示月-日
    } else {
        x = data.map((item) => dayjs(item.date).format('MM/DD')) // 显示月-日
    }
    // 根据视图类型获取数据
    let y1, y2, unit, seriesName1, seriesName2

    if (currentView.value === 'capacity') {
        y1 = data.map((item) => item.chargeCap)
        y2 = data.map((item) => item.dischargeCap)
        unit = 'Ah'
        seriesName1 = t('status_chongdian')
        seriesName2 = t('status_fangdian')
    } else {
        y1 = data.map((item) => item.chargeDur)
        y2 = data.map((item) => item.dischargeDur)
        unit = 'h'
        seriesName1 = t('status_chongdian')
        seriesName2 = t('status_fangdian')
    }

    // 获取基础配置
    let options = _cloneDeep(getChargeOption())
    options.legend.data = [t('status_chongdian'), t('status_fangdian')]
    // 调整布局
    // options.legend.top = '12px'
    options.grid.left = '5px'
    options.grid.right = '5px'
    options.grid.bottom = '0'

    options.xAxis.axisLabel.fontSize = '12px'
    options.yAxis.axisLabel.fontSize = '12px'
    options.yAxis.nameGap = 10

    // 根据数据量调整柱子宽度
    if (x.length >= 25) {
        options.series[0].barWidth = '6px'
        options.series[1].barWidth = '6px'
    } else if (x.length >= 10) {
        options.series[0].barWidth = '10px'
        options.series[1].barWidth = '10px'
    } else {
        options.series[0].barWidth = '13px'
        options.series[1].barWidth = '13px'
    }
    // 设置数据
    options.xAxis.data = x
    options.yAxis.name = unit
    options.series[0].data = y1
    options.series[1].data = y2
    options.series[0].name = seriesName1
    options.series[1].name = seriesName2
    options.legend.data = [seriesName1, seriesName2]

    // 设置提示框
    options.tooltip.formatter = function (params) {
        return (
            params[0].name +
            '<br/>' +
            params[0].marker +
            params[0].seriesName +
            ' : ' +
            (params[0].value || 0) +
            unit +
            '<br/>' +
            params[1].marker +
            params[1].seriesName +
            ' : ' +
            (params[1].value || 0) +
            unit
        )
    }

    // 初始化或更新图表
    if (!myChart) {
        myChart = echarts.init(chartContainer.value)
    }
    nextTick(() => {
        myChart.setOption(options, true)
        myChart.resize()
    })
}

// 初始化图表
const initChart = async () => {
    await nextTick()
    updateChart()
}

// 监听数据变化
watch(
    () => props.data,
    (newData) => {
        if (newData && newData.length > 0) {
            statsPowerBattDailyUsageData.value = newData
        }
        updateChart()
    },
    { deep: true, immediate: true }
)

// 监听语言变化
watch(
    () => locale.value,
    () => {
        updateChart()
    }
)
const getRequestParams = () => {
    // const currentOption = periodOptions.value[currentPeriodIndex.value]
    if (currentOption.value === 'week') {
        return {
            startDate: dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
            endDate: dayjs().format('YYYY-MM-DD'),
            periodType: 'day',
        }
    } else if (currentOption.value === 'month') {
        return {
            startDate: dayjs().subtract(29, 'day').format('YYYY-MM-DD'),
            endDate: dayjs().format('YYYY-MM-DD'),
            periodType: 'day',
        }
    } else {
        return {
            startMonth: dayjs().subtract(11, 'month').format('YYYY-MM'),
            endMonth: dayjs().format('YYYY-MM'),
            periodType: 'month',
        }
    }
}
import { useRoute } from 'vue-router'
const route = useRoute()

const getChartData = async () => {
    try {
        const requestParams = getRequestParams()
        console.log('请求参数:', requestParams)
        // 模拟接口调用
        // 模拟返回数据
        const mockData = await powerApi.statsPowerBattDailyUsage(
            {
                ...requestParams,
                sn: route.query.sn,
            },
            {
                sn: route.query.sn,
                signature: route.query.signature,
            }
        )
        statsPowerBattDailyUsageData.value = mockData.data.data
        // 重新渲染图表
        updateChart()
    } catch (error) {
        console.error('获取图表数据失败:', error)
    } finally {
        //
    }
}
onMounted(async () => {
    await getChartData()
    initChart()
})

onUnmounted(() => {
    if (myChart) {
        myChart.dispose()
        myChart = null
    }
})

// 暴露方法给父组件
defineExpose({
    updateChart,
    switchView,
    getCurrentView: () => currentView.value,
    getCurrentPeriod: () => currentOption.value,
    getCurrentTimeParams: () => currentTimeParams.value,
    getChartInstance: () => myChart,
})
</script>

<style lang="less" scoped>
.bar-chart-container {
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    background: #f5f5f5;
    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 0 12px;
        .view-switch {
            display: flex;
            .switch-item {
                display: flex;
                align-items: center;
                gap: 8px;
                border-radius: 20px;
                cursor: pointer;
                transition: all 0.3s;
                .switch-text {
                    font-size: 12px;
                    font-weight: 500;
                }
            }
        }

        .filter-dropdown {
            position: relative;

            .period-select {
                appearance: none;
                background: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px 30px 8px 12px;
                font-size: 14px;
                cursor: pointer;
                outline: none;

                &:focus {
                    border-color: #3edacd;
                }
            }

            .arrow-icon {
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
                pointer-events: none;
                color: #666;
                font-size: 12px;
            }
        }
    }

    .chart-content {
        width: 100%;
        // min-height: 300px;
    }
}
.toggle-icon {
    color: #6fbece;
}
</style>
