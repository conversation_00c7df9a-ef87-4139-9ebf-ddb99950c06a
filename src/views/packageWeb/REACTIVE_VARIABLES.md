# Reactive变量修改说明

## 概述

将 `basicInfo`、`realData`、`realCells` 三个变量从 `ref` 改为 `reactive`，以提供更好的响应式体验和更简洁的模板语法。

## 修改前后对比

### 修改前 (使用ref)
```javascript
const basicInfo = ref({
    productModel: undefined,
    bmsInfo: undefined,
})

// 模板中使用
{{ basicInfo.value?.bmsInfo.sn }}

// 接口赋值
basicInfo.value = res.data.data
```

### 修改后 (使用reactive)
```javascript
const basicInfo = reactive({
    bmsInfo: {
        sn: '',
        batteryNo: '',
        // ... 其他属性
    },
    productModel: {
        majorCustomers: [],
        // ... 其他属性
    }
})

// 模板中使用
{{ basicInfo.bmsInfo.sn }}

// 接口赋值
Object.assign(basicInfo, res.data.data)
```

## 详细变量结构

### 1. basicInfo (基础信息)

#### bmsInfo (BMS信息)
- `sn: string` - 设备编号
- `batteryNo: string` - 电池编号
- `hwid: string` - 硬件ID
- `imei: string` - IMEI号
- `softwareVersion: string` - 软件版本
- `activeTime: number | null` - 激活时间
- `serviceExpireDate: number | null` - 服务到期日期
- `projectId: string` - 项目ID

#### productModel (产品型号信息)
- `majorCustomers: string[]` - 主要客户列表
- `bmsSummaryInfo: object` - BMS汇总信息
  - `totalAlarms: number` - 总告警数
  - `chgTimeSum: number` - 累计充电时间
  - `dsgTimeSum: number` - 累计放电时间
  - `chgCapSum: number` - 累计充电容量
  - `dsgCapSum: number` - 累计放电容量
- `cellType: string` - 电芯类型
- `cellPack: string` - 电芯封装
- `tanks: number` - 电池箱数
- `cellVoltage: number` - 单体电压
- `ratedVoltage: number` - 额定总压
- `ratedCurrent: number` - 额定总流
- `ratedCapacity: number` - 额定容量
- `ratedEnergy: number` - 额定能量
- `model: string` - 设备型号
- `vehicleType: string` - 车辆类型

### 2. realData (实时数据)

#### 运行状态相关
- `status: number` - 运行状态 (0:待机, 1:放电, 2:充电, 3:离线)
- `signal4g: string` - 4G信号强度
- `soc: number` - 电量百分比 (0-100)
- `time: number` - 时间戳
- `address: string` - 地址信息

#### 电气参数
- `sysVoltage: number` - 总电压 (V)
- `sysCurrent: number` - 总电流 (A)
- `soh: number` - 电池健康度 (%)

#### 电压统计
- `volAvg: number` - 电压平均值 (V)
- `volMax: number` - 电压最大值 (V)
- `volMaxId: number` - 电压最大值电芯编号
- `volMin: number` - 电压最小值 (V)
- `volMinId: number` - 电压最小值电芯编号

#### 温度统计
- `tempAvg: number` - 温度平均值 (°C)
- `tempMax: number` - 温度最大值 (°C)
- `tempMaxId: number` - 温度最大值传感器编号
- `tempMin: number` - 温度最小值 (°C)
- `tempMinId: number` - 温度最小值传感器编号

#### 其他参数
- `cycleCount: number` - 循环次数
- `cellNub: number` - 电芯串数

### 3. realCells (电芯数据)

- `voltage: number[]` - 电芯电压数组
- `temperature: number[]` - 电芯温度数组

## 模板中的使用位置

### basicInfo 使用位置 (共24处)
1. 设备编号显示: `basicInfo.bmsInfo.sn`
2. 告警条件判断: `basicInfo.productModel.bmsSummaryInfo.totalAlarms`
3. 告警数量显示: `basicInfo.productModel.bmsSummaryInfo.totalAlarms`
4. 客户名称: `basicInfo.productModel.majorCustomers.join('、')`
5. 累计充电时间: `basicInfo.productModel.bmsSummaryInfo.chgTimeSum`
6. 累计放电时间: `basicInfo.productModel.bmsSummaryInfo.dsgTimeSum`
7. 累计充电容量: `basicInfo.productModel.bmsSummaryInfo.chgCapSum`
8. 累计放电容量: `basicInfo.productModel.bmsSummaryInfo.dsgCapSum`
9. 电芯类型: `basicInfo.productModel.cellType`
10. 电芯封装: `basicInfo.productModel.cellPack`
11. 电池箱数: `basicInfo.productModel.tanks`
12. 单体电压: `basicInfo.productModel.cellVoltage`
13. 额定总压: `basicInfo.productModel.ratedVoltage`
14. 额定总流: `basicInfo.productModel.ratedCurrent`
15. 额定容量: `basicInfo.productModel.ratedCapacity`
16. 额定能量: `basicInfo.productModel.ratedEnergy`
17. 设备编号(基础信息): `basicInfo.bmsInfo.sn`
18. 电池编号: `basicInfo.bmsInfo.batteryNo`
19. 设备型号: `basicInfo.productModel.model`
20. HWID: `basicInfo.bmsInfo.hwid`
21. IMEI: `basicInfo.bmsInfo.imei`
22. 软件版本: `basicInfo.bmsInfo.softwareVersion`
23. 激活日期: `basicInfo.bmsInfo.activeTime`
24. 服务到期日期: `basicInfo.bmsInfo.serviceExpireDate`

### realData 使用位置 (共20处)
1. 运行状态图标颜色: `realData.status`
2. 运行状态图标: `realData.status`
3. 运行状态标签: `realData.status`
4. 4G信号强度: `realData.signal4g`
5. 电量百分比: `realData.soc`
6. 时间显示: `realData.time`
7. 地址显示: `realData.address`
8. 总电压: `realData.sysVoltage`
9. 总电流: `realData.sysCurrent`
10. 电池健康度: `realData.soh`
11. 电压平均值: `realData.volAvg`
12. 电压最大值: `realData.volMax`
13. 电压最大值电芯编号: `realData.volMaxId`
14. 电压最小值: `realData.volMin`
15. 电压最小值电芯编号: `realData.volMinId`
16. 温度平均值: `realData.tempAvg`
17. 温度最大值: `realData.tempMax`
18. 温度最大值传感器编号: `realData.tempMaxId`
19. 温度最小值: `realData.tempMin`
20. 温度最小值传感器编号: `realData.tempMinId`
21. 循环次数: `realData.cycleCount`
22. 电芯串数: `realData.cellNub`

### realCells 使用位置 (共2处)
1. 电压热力分布数据: `realCells.voltage`
2. 温度热力分布数据: `realCells.temperature`

## 脚本中的使用位置

### computed 函数
- `vehicleType`: 使用 `basicInfo.productModel.vehicleType`

### API调用函数
- `statsPowerBattDurUsageSummary`: 使用 `basicInfo.bmsInfo.projectId`

## 接口赋值修改

### 修改前
```javascript
const getBasicInfo = async () => {
    let res = await powerApi.getBasicInfo(pageInfo.value.sn)
    basicInfo.value = res.data.data
}
```

### 修改后
```javascript
const getBasicInfo = async () => {
    let res = await powerApi.getBasicInfo(pageInfo.value.sn)
    Object.assign(basicInfo, res.data.data)
}
```

## 优势

1. **模板语法简化**: 不再需要 `.value`，代码更简洁
2. **类型安全**: 预定义了所有属性，避免运行时错误
3. **响应式优化**: reactive 对于对象属性的响应式处理更高效
4. **代码可读性**: 模板中的表达式更清晰，减少了可选链操作符的使用
5. **开发体验**: IDE 可以提供更好的类型提示和自动补全

## 注意事项

1. **初始值设置**: 所有属性都有合理的初始值，避免undefined错误
2. **数据更新**: 使用 `Object.assign()` 而不是直接赋值
3. **类型一致性**: 确保接口返回的数据结构与定义的结构一致
4. **错误处理**: 在接口调用失败时，reactive对象仍保持初始状态
