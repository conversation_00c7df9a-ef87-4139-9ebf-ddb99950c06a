<template>
    <el-upload
        :on-change="handleFileChange"
        :auto-upload="false"
        accept=".xlsx, .xls"
        :limit="1"
    >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
            Drop file here or <em>click to upload</em>
        </div>
        <template #tip>
            <div class="el-upload__tip">
                jpg/png files with a size less than 500kb
            </div>
        </template>
    </el-upload>
    <div v-if="selectedFile" style="margin-top: 10px">
        已选择文件：{{ selectedFile.name }}
    </div>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { ref, computed, useAttrs, watch } from 'vue'
import store from '@/store'
import { UploadFilled } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
const { t, locale } = useI18n()
function getBase64(img, callback) {
    const reader = new FileReader()
    reader.addEventListener('load', () => callback(reader.result))
    reader.readAsDataURL(img)
}

// const handleChange = (info) => {
//     if (info.file.status === 'uploading') {
//         loading.value = true
//         return
//     }
//     if (info.file.status === 'done') {
//         info.scene = dataProps.value.scene
//         emit('success', info)
//         getBase64(info.file.originFileObj, (base64Url) => {
//             imageUrl.value = base64Url
//             loading.value = false
//         })
//     }
//     if (info.file.status === 'error') {
//         loading.value = false
//         message.error(t('Upload Failed'))
//     }
// }
const selectedFile = ref(null)
const loading = ref(false)

const handleFileChange = (file) => {
    // 文件类型验证
    const allowedTypes = [
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ]
    const isExcel = allowedTypes.includes(file.raw.type)
    const isLt5M = file.raw.size / 1024 / 1024 < 5

    if (!isExcel) {
        ElMessage.error('只能上传Excel文件!')
        return
    }
    if (!isLt5M) {
        ElMessage.error('文件大小不能超过5MB!')
        return
    }

    selectedFile.value = file.raw
}
</script>
<style scoped lang="less"></style>
