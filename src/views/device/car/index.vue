<template>
    <div class="de">
        <!-- -->
        <div class="flex items-center justify-between mb-3">
            <div>
                <div
                    class="cursor-pointer leading-5.5 flex items-center orgName text-base"
                    @click="orgVisible = true"
                >
                    <div
                        class="w-8 h-8 rounded bg-ff dark:bg-ff-dark flex justify-center items-center mr-1"
                    >
                        <iconSvg
                            name="treeOpen"
                            class="w-5 h-5"
                            className="tree-svg-box"
                        />
                    </div>
                    <span>{{ orgNameValue }}</span>
                </div>
            </div>
            <div class="flex gap-x-3">
                <el-button
                    plain
                    round
                    @click="goOperationCar"
                    v-if="isOperator"
                >
                    <span> {{ $t('Management') }}</span>
                    <iconSvg name="operation" class="w-5 h-5 ml-0.5" />
                </el-button>
                <el-button plain round disabled>
                    {{ $t('Dashboard') }}
                    <iconSvg name="fullicon" class="w-5 h-5 ml-0.5" />
                </el-button>
            </div>
        </div>
        <div class="modules flex items-start justify-between gap-x-4">
            <div
                class="w-2/5 flex justify-end items-center rounded-lg bg-ff dark:bg-ff-dark cursor-pointer tabs-content"
                :class="activeKey == 'a' ? 'active' : ''"
                @click="changeTab('a')"
            >
                <div class="w-1/3 h-full statistics-bg relative"></div>
                <div class="w-3/5 px-4 pt-3 pb-5 relative z-10">
                    <div
                        class="sysbtjbt px-3 py-2"
                        :class="locale"
                        style="background: rgba(149, 158, 195, 0.1)"
                    >
                        <span class="name">
                            {{ $t('Total devices') }}
                        </span>
                        <span class="data">
                            {{ onesPieceData?.totalDeviceQuantity || 0 }}
                        </span>
                        <span class="unit">{{ $t('tai') }}</span>
                    </div>
                    <div
                        class="flex justify-between items-center px-3"
                        style="margin-top: 9px"
                    >
                        <div class="">
                            <div class="mb-3">
                                <span class="text-60"
                                    >{{ $t('home_fugaichengshi') }}：</span
                                >
                                <span
                                    class="text-base leading-none font-medium text-100"
                                >
                                    {{ onesPieceData?.cityNum || 0 }}</span
                                ><span
                                    class="ml-0.5 text-100 text-xs font-medium"
                                    >{{ $t('common_ge') }}</span
                                >
                            </div>
                            <div class="dark:text-80-dark">
                                <span class="text-60"
                                    >{{ $t('Loading Energy') }}：</span
                                >
                                <span
                                    class="text-base leading-none font-medium text-100"
                                >
                                    {{
                                        unitConversion(
                                            onesPieceData.totalEnergy,
                                            1000
                                        )
                                    }} </span
                                ><span
                                    class="ml-0.5 text-100 text-xs font-medium"
                                    >{{
                                        alternateUnits(
                                            onesPieceData.totalEnergy || 0,
                                            1000
                                        )
                                            ? 'MWh'
                                            : 'kWh'
                                    }}</span
                                >
                            </div>
                        </div>
                        <div>
                            <div
                                class="device-num-tag online items-center mb-2"
                            >
                                <span class="device-num-tag-name">{{
                                    $t('Onlined')
                                }}</span>
                                <div class="device-num-tag-data">
                                    <span
                                        class="font-medium inline-block text-right num"
                                        style="min-width: 17px"
                                        >{{
                                            onesPieceData?.onlineDeviceQuantity ||
                                            0
                                        }}</span
                                    ><span class="unit">{{ $t('tai') }}</span>
                                </div>
                            </div>
                            <div class="device-num-tag offline items-center">
                                <span class="device-num-tag-name">{{
                                    $t('status_lixian')
                                }}</span>
                                <div class="device-num-tag-data">
                                    <span
                                        class="font-medium inline-block text-right num"
                                        style="min-width: 17px"
                                        >{{
                                            onesPieceData?.offlineDeviceQuantity ||
                                            0
                                        }}</span
                                    ><span class="unit">{{ $t('tai') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="w-3/5 px-4 py-3 rounded-lg bg-ff dark:bg-ff-dark tabs-content"
                :class="activeKey == 'b' ? 'active' : ''"
                @click="changeTab('b')"
            >
                <statistics :data="chargeStatisticsData" />
            </div>
        </div>
        <div class="homeTabs">
            <a-tabs class="w-full tabs" :activeKey="activeKey">
                <a-tab-pane key="a" tab="a">
                    <!-- 选项卡一：设备列表 -->
                    <div
                        class="device-list-search flex items-center justify-end mb-5 mt-5"
                    >
                        <div class="search-input-group">
                            <!-- <el-select
                                class="search-select"
                                v-model="selectedDeviceName"
                            >
                                <el-option
                                    v-for="item in deviceOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select> -->
                            <el-input
                                v-model="keyword"
                                :placeholder="$t('placeholder')"
                                class="search-input"
                                clearable
                                @clear="handleSearch"
                            >
                                <template #append>
                                    <span
                                        class="inline-block px-3 cursor-pointer"
                                        @click="handleSearch"
                                        ><iconSvg
                                            class="w-4 h-4 align-middle -mt-0.5"
                                            name="search"
                                    /></span>
                                </template>
                            </el-input>
                        </div>
                    </div>

                    <div v-loading="loading">
                        <div v-if="deviceList.length" class="device-table-list">
                            <div
                                class="flex items-center px-4 mb-5 text-secondar-text dark:text-60-dark"
                            >
                                <div class="flex w-2/5 pr-2">
                                    {{ $t('Device info') }}
                                </div>

                                <div
                                    class="flex-1 flex items-center justify-between pl-3"
                                >
                                    <div class="runStatus w-32">
                                        {{ $t('station_yunxingzhuangtai') }}
                                    </div>

                                    <div class="BMSmodel w-32 text-center">
                                        {{ $t('Bms model') }}
                                    </div>

                                    <div class="cap w-18 text-center">
                                        {{ $t('Capacity') }}(Ah)
                                    </div>

                                    <div class="dur w-34 text-center">
                                        {{ $t('Charging duration') }}(h)
                                    </div>

                                    <div class="time w-28 text-right">
                                        {{ $t('service_expire_time') }}
                                    </div>
                                </div>
                            </div>
                            <div
                                v-for="item in deviceList"
                                :key="item.id"
                                class="device-table-row flex items-center bg-ff dark:bg-ff-dark rounded-lg mb-3 px-4 cursor-pointer hover:shadow border border-transparent hover:border-themeColor transition text-80 dark:text-80-dark"
                                @click="handleRowClick(item)"
                            >
                                <div
                                    class="flex w-2/5 border-r border-border pr-2 items-center"
                                >
                                    <div
                                        class="flex items-center justify-center mr-4"
                                    >
                                        <carImg
                                            :vehicleType="
                                                getSafeValue(
                                                    item.vehicleType,
                                                    'default'
                                                )
                                            "
                                            style="width: 84px; height: 84px"
                                        />
                                    </div>
                                    <div class="">
                                        <div>
                                            <span class="font-medium leading-6"
                                                >{{ $t('Device No') }}：{{
                                                    getSafeValue(item.sn)
                                                }}</span
                                            >
                                        </div>
                                        <div class="text-60 dark:text-60-dark">
                                            (
                                            <span
                                                class="text-xs leading-6 text-60 dark:text-60-dark"
                                                >{{ $t('BatteryNo') }}:
                                                {{
                                                    getSafeValue(item.batteryNo)
                                                }}</span
                                            >
                                            <span
                                                class="text-xs leading-6 ml-2 text-60 dark:text-60-dark"
                                                >HWID:
                                                {{
                                                    getSafeValue(item.hwid)
                                                }}</span
                                            >
                                            )
                                        </div>
                                    </div>
                                </div>
                                <div
                                    class="flex-1 flex items-center justify-between pl-3"
                                >
                                    <div class="runStatus w-32">
                                        <div class="flex items-center">
                                            <div
                                                class="flex items-center gap-x-1"
                                                :style="{
                                                    color: getState(
                                                        item.status,
                                                        'power'
                                                    ).color,
                                                }"
                                            >
                                                <i
                                                    class="w-6 h-6 text-2xl leading-6"
                                                    :class="[
                                                        'iconfont',
                                                        getState(
                                                            item.status,
                                                            'power'
                                                        ).icon,
                                                    ]"
                                                ></i>
                                                <span>{{
                                                    $t(
                                                        getState(
                                                            item.status,
                                                            'power'
                                                        ).label
                                                    )
                                                }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="BMSmodel w-32 text-center">
                                        {{ item.model }}
                                    </div>
                                    <div class="cap w-18 text-center">
                                        {{ item.ratedCapacity || 0 }}
                                    </div>
                                    <div class="dur w-34 text-center">
                                        {{ item.chgTimeSum || 0 }}
                                    </div>
                                    <div class="time w-28 text-right">
                                        {{
                                            item.serviceExpireDate
                                                ? dayjs(
                                                      item.serviceExpireDate
                                                  ).format('YYYY/MM/DD')
                                                : '-'
                                        }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <empty-data
                            v-else
                            :description="$t('zanwushuju')"
                            class="my-10"
                        />
                        <div class="flex justify-center mt-10">
                            <el-pagination
                                background
                                layout="prev, pager, next"
                                :total="pagination.total"
                                v-model:current-page="pagination.current"
                                :page-size="pagination.size"
                                @change="handlePageChange"
                                :disabled="pageLoading"
                            />
                        </div>
                    </div>
                </a-tab-pane>
                <a-tab-pane key="b" tab="b">
                    <!-- 选项卡二 -->
                    <div class="p-4 bg-ff dark:bg-ff-dark rounded-lg mt-5">
                        <div
                            class="flex justify-between items-center mb-6 text-title dark:text-title-dark"
                        >
                            <el-tabs
                                v-model="statisticsActiveName"
                                @tab-change="
                                    handleStatisticsActiveNameTabChange
                                "
                            >
                                <el-tab-pane
                                    :label="$t('Average running time')"
                                    name="b"
                                >
                                </el-tab-pane>
                                <el-tab-pane
                                    :label="$t('Charge and discharge capacity')"
                                    name="a"
                                >
                                </el-tab-pane>
                            </el-tabs>
                            <div class="flex justify-between gap-x-3">
                                <el-select
                                    v-model="projectChargeTotalDateType"
                                    placeholder="请选择"
                                    style="
                                        width: 120px;
                                        margin-right: 16px;
                                        border-radius: 8px;
                                    "
                                    @change="onProjectChargeTotalDateChange"
                                >
                                    <el-option
                                        v-for="item in chargeTotalDateOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </div>
                        </div>
                        <!-- 393px -->
                        <div class="flex gap-x-5">
                            <div class="flex-1 w-0">
                                <div
                                    class="w-full"
                                    style="height: 306px"
                                    id="incomeEcharts"
                                ></div>
                            </div>
                            <rank
                                :rankList="rankList"
                                @toggleRank="toggleRank"
                                :type="statisticsActiveName"
                            />
                        </div>
                    </div>
                    <div
                        class="flex items-center flex-row-reverse gap-x-3 mt-3"
                    >
                        <div
                            class="flex-1 bg-ff dark:bg-ff-dark rounded-lg p-4"
                        >
                            <div
                                class="w-full flex justify-between items-center text-title dark:text-title-dark"
                            >
                                <div>
                                    {{ $t('Operating Hours Distribution') }}
                                </div>
                                <div></div>
                            </div>
                            <div class="w-full" style="height: 336px">
                                <distributionChart
                                    :chartData="distributionChartDataDur"
                                    type="hour"
                                />
                            </div>
                        </div>
                        <div
                            class="flex-1 bg-ff dark:bg-ff-dark rounded-lg p-4"
                        >
                            <div
                                class="w-full flex justify-between items-center text-title dark:text-title-dark"
                            >
                                <div>
                                    {{ $t('Service Life Distribution') }}
                                </div>
                                <div></div>
                            </div>
                            <div class="w-full" style="height: 336px">
                                <distributionChart
                                    :chartData="distributionChartDataYears"
                                    type="month"
                                />
                            </div>
                        </div>
                    </div>
                </a-tab-pane>
            </a-tabs>
        </div>
    </div>
    <el-drawer
        v-model="orgVisible"
        direction="ltr"
        :size="486"
        :show-close="false"
        @close="onClose"
        wrapClassName="drawerBox"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header text-primary-text dark:text-80-dark">
                    <span>{{ orgNameValue }}</span>
                </div>
                <div>
                    <el-button plain round @click="onClose">{{
                        $t('common_guanbi')
                    }}</el-button>
                </div>
            </div>
        </template>
        <el-tree
            style="max-width: 600px"
            default-expand-all
            :data="treeData"
            :props="{ label: 'name', children: 'children' }"
            node-key="id"
            :expand-on-click-node="false"
            highlight-current
            @node-click="handleNodeClick"
            :current-node-key="selectedKeys[0]"
        />
    </el-drawer>
</template>

<script setup>
import { onMounted, ref, computed, nextTick, reactive, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import apiService from '@/apiService/device'
import carApi from '@/apiService/car'
import powerApi from '@/apiService/power'
import {
    pieData,
    chargeOptions,
    chargeStatus,
    unitConversion,
    alternateUnits,
    formatterKw,
    formatterAh,
    getChargeOption,
    updateEcharts,
} from '../const'
import statistics from '../components/statistics.vue'
import carImg from './components/carImg.vue'
import emptyData from '@/components/emptyData.vue'
import distributionChart from './components/distributionChart.vue'
import debounce from 'lodash/debounce'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import moment from 'moment'
import _cloneDeep from 'lodash/cloneDeep'
import Search from '@/components/search/index.vue'
import { getState, getSegmentTypeColor } from '@/common/util.js'
import useTheme from '@/common/useTheme'
import rank from './components/rank.vue'
import { roundUp } from './util/util.js'
const { t, locale } = useI18n()
const route = useRoute()
const router = useRouter()
const store = useStore()
const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])

const orgNameValue = ref()
const orgVisible = ref(false)
const onClose = () => {
    orgVisible.value = false
}
const treeData = ref([])
const selectedKeys = ref()

const addDraggedProperty = (tree) => {
    function traverse(node) {
        if (node.children && node.children.length > 0) {
            node.children.forEach(traverse)
        }
    }
    if (tree?.length) {
        tree.forEach(traverse)
        return tree
    }
}
const getTreeData = async () => {
    const {
        data: { data, code },
    } = await apiService.getDeviceTree({
        businessType: 'vehicle_battery',
    })
    if (code === 0) {
        let rootNode = {
            name: getCompanyInfo?.value?.orgName,
            id: getCompanyInfo?.value?.orgId,
            province: getCompanyInfo?.value?.province,
        }
        const tree = [
            {
                ...rootNode,
                children: addDraggedProperty(data) || [],
            },
        ]
        //
        treeData.value = tree
        selectedKeys.value = store.state.car.selectSupplierInfoCar?.id
            ? [store.state.car.selectSupplierInfoCar?.id]
            : [treeData.value[0].id]
        let info = store.state.car.selectSupplierInfoCar?.id
            ? store.state.car.selectSupplierInfoCar
            : tree[0]
        const { id, name, province } = info
        store.commit('car/setSelectSupplierInfoCar', {
            id,
            name,
            province,
        })
        // 从树结构中获取当前节点以及下属数据
        // getChartDataByTreeSelectedKey(selectedKeys.value)
    }
}
const deviceList = ref([])
const loading = ref(false)
const keyword = ref('')

const pagination = ref({ current: 1, size: 7, total: 0 })

// 顶部统计数据
const onesPieceData = reactive({
    totalStationQuantity: null,
    totalDeviceQuantity: null,
    activeDeviceQuantity: null,
    onlineDeviceQuantity: null,
    offlineDeviceQuantity: null,
    ratedPower: null,
    ratedCapacity: null,
    cityNum: null,
    totalEnergy: null,
})

// 获取组织/项目/客户参数
const getContextParams = () => {
    // 优先从store获取
    const supplierId = store.state.car?.selectSupplierInfoCar?.id || ''
    const projectId = route.query.projectId || ''
    const customerId = route.query.customerId || ''
    return { supplierId, projectId, customerId }
}

// 字段安全获取
const getSafeValue = (val, defaultVal = '-') => {
    if (val === undefined || val === null || val === '') return defaultVal
    return val
}

const getDeviceList = async () => {
    loading.value = true
    try {
        const { supplierId, projectId, customerId } = getContextParams()
        const params = {
            current: pagination.value.current,
            size: pagination.value.size,
            keyword: keyword.value,
            supplierId,
            projectId,
            customerId,
        }
        console.log('请求参数:', params)
        const res = await powerApi.getDevicePageList(params)
        deviceList.value = res.data.data.records || []
        pagination.value.total = res.data.data.total || 0
        console.log('获取设备列表成功，数据条数:', deviceList.value.length)
    } catch (e) {
        deviceList.value = []
        pagination.value.total = 0
        console.error('获取设备列表失败:', e)
    } finally {
        console.log('获取设备列表完成，设置 loading 为 false')
        loading.value = false
    }
}

const chargeStatisticsData = reactive({
    beforeYesterdayChargeDur: 0,
    beforeYesterdayDischargeDur: 0,
    comparedChargePercent: 0,
    comparedDischargePercent: 0,
    currentMonthChargeDur: 0,
    currentMonthDischargeDur: 0,
    totalChargeDur: 0,
    totalDischargeDur: 0,
    yesterdayChargeDur: 0,
    yesterdayDischargeDur: 0,
})

const statsPowerBattUsageSummary = async () => {
    //
    const { supplierId } = getContextParams()
    let res = await powerApi.statsPowerBattDurUsageSummary({
        supplierId,
    })
    if (res.data.code === 0) {
        Object.keys(res.data.data).forEach((key) => {
            chargeStatisticsData[key] = res.data.data[key] || 0
        })
    } else {
        Object.keys(chargeStatisticsData).forEach((key) => {
            chargeStatisticsData[key] = 0
        })
    }
}

// 获取顶部统计数据
const getStatisticsData = async () => {
    try {
        const { supplierId } = getContextParams()

        // 获取供应商BMS摘要信息
        const res = await powerApi.getSupplierBmsSummary({
            supplierId,
        })
        if (res.data.code === 0 && res.data.data) {
            const data = res.data.data
            Object.keys(data).forEach((key) => {
                onesPieceData[key] = data[key]
            })
        } else {
            // 如果API返回失败，使用默认值
        }
    } catch (e) {
        console.error('获取统计数据失败:', e)
    }
}

const handleSearch = debounce(async () => {
    try {
        pagination.value.current = 1
        await getDeviceList()
    } catch (error) {
        console.error('搜索失败:', error)
        ElMessage.error(t('搜索失败，请重试'))
    }
}, 300)

const pageLoading = ref(false)
const handlePageChange = debounce(async (page) => {
    // 滚动到页面顶部 280px 位置，动画时间 400ms
    const targetPosition = 0
    const startPosition =
        window.pageYOffset || document.documentElement.scrollTop
    const distance = targetPosition - startPosition
    const duration = 200
    const startTime = performance.now()

    const easeInOutQuad = (t) => {
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t
    }

    const animateScroll = (currentTime) => {
        const elapsed = currentTime - startTime
        const progress = Math.min(elapsed / duration, 1)
        const easedProgress = easeInOutQuad(progress)

        window.scrollTo(0, startPosition + distance * easedProgress)

        if (progress < 1) {
            requestAnimationFrame(animateScroll)
        }
    }

    requestAnimationFrame(animateScroll)
    pageLoading.value = true
    pagination.value.current = page
    await getDeviceList()
    pageLoading.value = false
}, 300)

const handleRowClick = (item) => {
    if (!item.sn || !item.id || !item.customerId) {
        ElMessage.error(t('设备信息不完整，无法跳转'))
        return
    }
    router.push({
        name: 'equipmentDetail',
        query: {
            sn: item.sn,
            id: item.id,
            customerId: item.customerId,
        },
    })
}

const handleNodeClick = async (data) => {
    try {
        const { id, name, province } = data
        orgNameValue.value = name
        selectedKeys.value = [id]
        orgVisible.value = false
        store.commit('car/setSelectSupplierInfoCar', { id, name, province })

        if (getCompanyInfo.value?.orgName) {
            orgNameValue.value = store.state.car.selectSupplierInfoCar?.id
                ? store.state.car.selectSupplierInfoCar?.name
                : getCompanyInfo.value.orgName
        }

        // 重新获取所有数据
        await getMainApi()

        // 如果当前在选项卡二，也重新获取选项卡二的数据
        if (activeKey.value === 'b') {
            await initTabTwoData()
        }
    } catch (error) {
        console.error('切换组织失败:', error)
        ElMessage.error(t('切换组织失败，请重试'))
    } finally {
        //
    }
}
const goOperationCar = () => {
    const origin = window.location.origin
    window.open(`${origin}/#/operation-vehicle`)
}

// 选项卡切换
const activeKey = ref('a')
const changeTab = async (key) => {
    if (activeKey.value === key) return // 避免重复切换

    activeKey.value = key
    if (key === 'b') {
        // 切换到选项卡二时初始化数据
        try {
            await nextTick()
            await initTabTwoData()
        } catch (error) {
            console.error('切换选项卡失败:', error)
            ElMessage.error(t('切换选项卡失败，请重试'))
        } finally {
            //
        }
    }
}

// 选项卡二相关变量
const statisticsActiveName = ref('b')
const projectChargeTotalDateType = ref('1')
const chargeTotalDateOptions = ref([
    {
        label: t('Last Week'),
        value: '1',
    },
    {
        label: t('Last Year'),
        value: '2',
    },
])
const chargeType = ref('charge')
const rankList = ref([])
const distributionChartDataDur = ref([])
const distributionChartDataYears = ref([])

// 选项卡二方法
const handleStatisticsActiveNameTabChange = async () => {
    setChargeOptions()
    getRankData()
}

const onProjectChargeTotalDateChange = async () => {
    chargeDateSearchChange()
}

const toggleRank = async (e) => {
    chargeType.value = e
    await getRankData()
}

// 图表数据
const dateList2 = ref([])
const chargeData2 = ref([])
const dischargeData2 = ref([])
const chargeDur2 = ref([])
const disChargeDur2 = ref([])

const setChargeOptions = () => {
    let options = _cloneDeep(getChargeOption())
    options.grid.bottom = 0
    options.grid.right = 10
    if (dateList2.value?.length) {
        options.xAxis.data = dateList2.value
    } else {
        if (projectChargeTotalDateType.value === '1') {
            options.xAxis.data = Array.from({ length: 7 }, (_, i) => {
                return dayjs()
                    .subtract(6 - i, 'day')
                    .format('MM/DD')
            })
        } else if (projectChargeTotalDateType.value === '2') {
            options.xAxis.data = Array.from({ length: 12 }, (_, i) => {
                return dayjs()
                    .subtract(11 - i, 'month')
                    .format('YYYY-MM')
            })
        }
    }

    if (projectChargeTotalDateType.value === '1') {
        options.series[0].barWidth = '24px'
        options.series[1].barWidth = '24px'
    } else {
        options.series[0].barWidth = '18px'
        options.series[1].barWidth = '18px'
    }

    if (statisticsActiveName.value == 'a') {
        options.yAxis.name = 'Ah'
        options.series[0].data = chargeData2.value
        options.series[1].data = dischargeData2.value
    } else if (statisticsActiveName.value == 'b') {
        options.yAxis.name = 'h'
        options.series[0].data = chargeDur2.value
        options.series[1].data = disChargeDur2.value
        options.series[0].name = t('Charging duration')
        options.series[1].name = t('Discharging duration')
        options.legend.data = [
            t('Charging duration'),
            t('Discharging duration'),
        ]
    }
    updateEcharts('incomeEcharts', options)
}

const chargeDateSearchChange = async () => {
    // 模拟数据获取
    await getChargeData()
    await getRankData()
}

const getChargeData = async () => {
    try {
        const { supplierId } = getContextParams()
        let params = {}

        if (projectChargeTotalDateType.value === '1') {
            params = {
                periodType: 'day',
                startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
                endDate: moment().format('YYYY-MM-DD'),
                supplierId,
            }
        } else if (projectChargeTotalDateType.value === '2') {
            params = {
                periodType: 'month',
                startMonth: moment().subtract(11, 'months').format('YYYY-MM'),
                endMonth: moment().format('YYYY-MM'),
                supplierId,
            }
        }

        const {
            data: { data, code },
        } = await powerApi.statsPowerBattDailyUsage(params)

        if (code === 0 && data?.length) {
            dateList2.value = data.map((item) => {
                if (params.periodType === 'month') {
                    return dayjs(item.date).format('YYYY-MM')
                }
                return dayjs(item.date).format('MM/DD')
            })
            chargeData2.value = data.map((item) => item.chargeCap || 0)
            dischargeData2.value = data.map((item) => item.dischargeCap || 0)
            chargeDur2.value = data.map((item) => item.chargeDur || 0)
            disChargeDur2.value = data.map((item) => item.dischargeDur || 0)
        } else {
            // 如果没有数据，使用默认的时间轴
            if (projectChargeTotalDateType.value === '1') {
                dateList2.value = Array.from({ length: 7 }, (_, i) => {
                    return dayjs()
                        .subtract(6 - i, 'day')
                        .format('MM/DD')
                })
            } else {
                dateList2.value = Array.from({ length: 12 }, (_, i) => {
                    return dayjs()
                        .subtract(11 - i, 'month')
                        .format('YYYY-MM')
                })
            }
            chargeData2.value = []
            dischargeData2.value = []
            chargeDur2.value = []
            disChargeDur2.value = []
        }

        setChargeOptions()
    } catch (error) {
        console.error('获取充放电数据失败:', error)
        // 发生错误时使用空数据
        dateList2.value = []
        chargeData2.value = []
        dischargeData2.value = []
        chargeDur2.value = []
        disChargeDur2.value = []
        setChargeOptions()
    }
}

const getRankData = async () => {
    try {
        const { supplierId } = getContextParams()
        let params = {
            periodType: 'day',
            startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
            endDate: moment().format('YYYY-MM-DD'),
            supplierId,
            chargeType: chargeType.value,
            rankType:
                statisticsActiveName.value == 'a' ? 'capacity' : 'duration',
        }

        if (projectChargeTotalDateType.value === '1') {
            params.startDate = moment().subtract(6, 'days').format('YYYY-MM-DD')
        } else if (projectChargeTotalDateType.value === '2') {
            params.startDate = moment()
                .subtract(365, 'days')
                .format('YYYY-MM-DD')
        }

        const res = await powerApi.statsDeviceChargeRank(params)
        rankList.value = res.data.data || []

        let max = 0
        rankList.value.forEach((item) => {
            if (+max < +item.quantity) max = +item.quantity
        })

        if (max) {
            rankList.value.forEach((item) => {
                item['ratio'] = +(item.quantity / max).toFixed(2)
            })
        }

        rankList.value.sort((a, b) => {
            return b.quantity - a.quantity
        })
    } catch (error) {
        console.error('获取排名数据失败:', error)
        rankList.value = []
    }
}

const transformData = (arr, xKey, yKey) => {
    if (!arr || arr.length === 0) {
        return []
    }

    // const maxX = Math.ceil(Math.max(...arr.map((item) => item[xKey])) * 1.2)
    const maxX = roundUp(Math.max(...arr.map((item) => item.time)))
    const timeMap = arr.reduce((acc, curr) => {
        acc[curr[xKey]] = curr[yKey]
        return acc
    }, {})

    const result = Array.from({ length: maxX + 1 }, (_, index) => ({
        x: index,
        value: timeMap[index] || 0,
    }))
    return result
}

const getStatisticDeviceData = async () => {
    try {
        const { supplierId } = getContextParams()
        // 获取设备使用时间分布
        const res1 = await powerApi.statisticDeviceUseTimeDistribution({
            customerId: supplierId,
        })
        let result1 = transformData(res1.data.data || [], 'time', 'quantity')
        distributionChartDataYears.value = result1

        // 获取设备运行时间分布
        const res2 = await powerApi.statisticDeviceRunTimeDistribution({
            customerId: supplierId,
        })
        let result2 = transformData(res2.data.data || [], 'time', 'quantity')
        distributionChartDataDur.value = result2
    } catch (error) {
        console.error('获取分布统计数据失败:', error)
        // 使用空数据
        distributionChartDataYears.value = []
        distributionChartDataDur.value = []
    }
}
const getMainApi = async () => {
    try {
        // 并行获取数据，但不影响列表的 loading 状态
        await Promise.all([
            getDeviceList(),
            getStatisticsData(),
            statsPowerBattUsageSummary(),
        ])
    } catch (error) {
        console.error('获取主要数据失败:', error)
        ElMessage.error(t('获取数据失败，请重试'))
    }
}

const initTabTwoData = async () => {
    try {
        await Promise.all([
            getChargeData(),
            getRankData(),
            getStatisticDeviceData(),
        ])
    } catch (error) {
        console.error('初始化选项卡二数据失败:', error)
        ElMessage.error(t('初始化数据失败，请重试'))
    }
}
const isOperator = computed(() => {
    return store.state.user.userInfoData.roles.includes('operation_staff')
    // return false
})

const selectedDeviceName = ref('name')
const deviceOptions = ref([
    {
        label: t('Station Name'),
        value: 'name',
    },
])
const { themeChangeComplete } = useTheme()
const isDark = computed(() => {
    return store.state.theme.isDark
})
watch([isDark, themeChangeComplete], ([newIsDark, isComplete]) => {
    if (isComplete) {
        if (activeKey.value == 'b') {
            setChargeOptions()
        }
    }
})
onMounted(async () => {
    try {
        await getTreeData()

        if (getCompanyInfo.value?.orgName) {
            orgNameValue.value = store.state.car.selectSupplierInfoCar?.id
                ? store.state.car.selectSupplierInfoCar?.name
                : getCompanyInfo.value.orgName
        }
        await getMainApi()
    } catch (error) {
        console.error('页面初始化失败:', error)
        ElMessage.error(t('页面初始化失败，请刷新重试'))
    } finally {
        //
    }
})
</script>

<style lang="less" scoped>
.de {
    min-height: calc(100vh - 128px);
    padding-top: 88px;
}
.orgName {
    color: var(--text-80);
}

:deep(.el-tree-node:focus > .el-tree-node__content) {
    background-color: transparent;
}
:deep(.el-tree-node__content) {
    flex-direction: row-reverse;
    height: 40px;
    border-radius: 4px;
    .el-tree-node__label {
        line-height: 40px;
        color: var(--text-80);
    }
    .el-text {
        color: var(--text-80) !important;
    }
    &:hover {
        // background: #f5f7f7;
        background-color: var(--bg-f5f7fa);
        color: var(--themeColor);
        .el-tree-node__label {
            &::before {
                border-color: var(--themeColor);
            }
        }
        * {
            fill: var(--themeColor);
        }
        .el-tree-node__expand-icon::before {
            background: var(--themeColor);
        }
        .el-tree-node__expand-icon.expanded::before {
            background: var(--themeColor);
        }
    }
}
:deep(.el-tree-node__label) {
    flex: 1;
    position: relative;
    text-indent: 8px;
    &::before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        border-bottom-left-radius: 2px;
        border-left: 1px solid var(--border);
        border-bottom: 1px solid var(--border);
        margin-right: 10px;
        position: absolute;
        left: -10px;
        top: 50%;
        transform: translateY(-50%);
    }
}
:deep(.el-tree) {
    display: block;
}
.el-tree {
    display: block;
    > :deep(.el-tree-node) {
        > .el-tree-node__content {
            > .el-tree-node__label {
                display: block;

                &::before {
                    display: none;
                }
            }
        }
    }
}
:deep(
        .el-tree--highlight-current
            .el-tree-node.is-current
            > .el-tree-node__content
    ) {
    border-radius: 4px;
    > .el-icon {
        color: #fff;
        fill: var(--themeColor);
    }
}
:deep(.el-tree-node) {
    .el-tree-node__expand-icon {
        font-size: 20px;
        color: #fff;
    }

    .el-tree-node__expand-icon::before {
        content: '+';
        font-size: 12px;
        width: 16px;
        height: 16px;
        text-align: center;
        background: rgba(34, 34, 34, 0.16);
        line-height: 16px;
        border-radius: 2px;
    }

    .el-tree-node__expand-icon.expanded::before {
        content: '-';
        font-size: 14px;
        width: 16px;
        height: 16px;
        text-align: center;
        background: rgba(34, 34, 34, 0.16);
        line-height: 16px;
        border-radius: 2px;
    }

    .el-tree-node__expand-icon.is-leaf {
        display: none;
    }
    .el-tree-node__expand-icon.el-icon-caret-right {
        transform: none !important;
    }
    .el-tree-node__expand-icon {
        font-style: normal !important;
        transform: none !important;
        svg {
            display: none !important;
        }
    }
    &.is-current {
        > .el-tree-node__content {
            > .el-tree-node__label {
                color: var(--themeColor);
            }
            > .el-icon {
                color: #fff;
                &:before {
                    background: var(--themeColor);
                }
            }
            .el-tree-node__label {
                &::before {
                    border-color: var(--themeColor);
                }
            }
        }
    }
}
.device-num-tag {
    // padding: 4px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 24px;
    align-items: center;
    padding: 0 15px;
    font-size: 0;
    height: 24px;
    display: flex;

    .device-num-tag-name {
        width: 29px;
        text-align: left;
        font-size: 14px;
    }
    .device-num-tag-data {
        width: 42px;
        text-align: right;
        display: flex;
        line-height: 24px;
        height: 24px;
        align-items: center;
        justify-content: flex-end;
    }
    span {
        font-size: 12px;
        // vertical-align: baseline;
    }
    .num {
        font-size: 16px;
        line-height: 1;
        // vertical-align: top;
    }
    .unit {
        line-height: 18px;
        padding-top: 1px;
        font-size: 12px;
    }

    // vertical-align: text-bottom;
}
.online {
    background: var(--tag-online-bg);
    color: var(--tag-online-color);
}

.offline {
    background: var(--tag-offline-bg);
    color: var(--tag-offline-color);
}
.text-60 {
    color: var(--text-60);
}
.text-100 {
    color: var(--text-100);
}

.device-table-list {
    min-height: 420px;
}
.device-table-row {
    transition: box-shadow 0.2s, border-color 0.2s;
    padding-top: 1px;
    padding-bottom: 1px;
}
.device-table-row:hover {
    border-color: var(--themeColor);
    box-shadow: 0 2px 12px 0 rgba(34, 34, 34, 0.08);
}

.quantity {
    flex-shrink: 0;
}

:deep(.homeTabs .ant-tabs-nav) {
    display: none !important;
}
:deep(.ant-tabs-bar) {
    border: 0;
    margin-bottom: 0;
}
.statistics-bg {
    background: url(../../../assets/car/statistics_bg.png) no-repeat center
        center;
    background-size: cover;
    width: 40%;
    position: absolute;
    left: 2px;
    top: 0;
    border-radius: 8px;
}
.homeTabs {
    :deep(.el-tabs__active-bar) {
        display: none;
    }

    :deep(.el-tabs__header) {
        margin-bottom: 0;
    }
}

.search-input-group {
    .search-select {
        width: 140px;
        position: relative;
        left: 1px;
        :deep(.el-select__wrapper) {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }
    }
    .search-input {
        width: 240px;
    }
    .el-select + .el-input {
        .el-input__wrapper {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
    }
    :deep(.el-input-group__append) {
        padding: 0;
        line-height: 32px;
    }
}
</style>
