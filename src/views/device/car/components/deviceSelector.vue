<template>
    <div class="device-selector-wrap">
        <div class="filter-area">
            <el-form :model="filterForm" class="filter-form">
                <!-- keyword 100% 宽度 -->
                <el-form-item class="full-width">
                    <el-input
                        v-model="filterForm.keyword"
                        :placeholder="$t('InputToSearch01')"
                        clearable
                        @input="KeywordSearch"
                    />
                </el-form-item>

                <!-- 其他筛选项 50% 宽度 -->
                <div class="half-width-row">
                    <el-form-item class="half-width">
                        <el-select
                            collapse-tags
                            v-model="filterForm.projectIds"
                            multiple
                            :placeholder="$t('Belonging project')"
                            clearable
                            @change="handleSearch"
                            class="w-full"
                        >
                            <el-option
                                v-for="item in projectOptions"
                                :key="item.id"
                                :label="item.projectName"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item class="half-width">
                        <el-tree-select
                            v-model="filterForm.cities"
                            :data="cityOptions"
                            :render-after-expand="true"
                            multiple
                            clearable
                            :placeholder="$t('Belonged City')"
                            class="w-full"
                            @change="handleSearch"
                        />
                    </el-form-item>
                </div>

                <div class="half-width-row">
                    <el-form-item class="half-width">
                        <el-select
                            v-model="filterForm.vehicleType"
                            :placeholder="$t('Vehicle type')"
                            clearable
                            multiple
                            @change="handleSearch"
                            class="w-full"
                        >
                            <el-option
                                v-for="item in vehicleTypeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item class="half-width">
                        <el-date-picker
                            v-model="filterForm.activeDate"
                            type="date"
                            :placeholder="$t('Activation time')"
                            value-format="YYYY-MM-DD"
                            clearable
                            @change="handleSearch"
                            class="w-full"
                        />
                    </el-form-item>
                </div>
            </el-form>
        </div>
        <div class="checkbox-area">
            <div class="checkbox-header">
                <el-checkbox
                    v-model="checkAll"
                    :indeterminate="isIndeterminate"
                    @change="handleCheckAllChange"
                    style="width: auto"
                >
                    {{ $t('Select all') }}
                </el-checkbox>
                <div class="selected-info">
                    {{ $t('Selected') }} {{ selectedDeviceIds.length }}
                    {{ $t('common_tiao') }}
                </div>
            </div>

            <div class="checkbox-list" v-loading="loading">
                <el-checkbox-group
                    :model-value="selectedDeviceIds"
                    @update:model-value="handleSelectionChange"
                    class="device-checkbox-group column-selector"
                >
                    <div
                        v-for="item in tableData"
                        :key="item.id"
                        class="device-checkbox-item"
                        :class="{
                            disabled: !!item.customerId || !item.projectId,
                        }"
                    >
                        <el-checkbox
                            :value="item.id"
                            :disabled="!!item.customerId || !item.projectId"
                            class="device-checkbox pl-4 relative"
                        >
                            <div
                                class="absolute w-2.5 h-2.5 border-l border-b left-0 rounded-bl-sm opacity-60"
                            ></div>

                            <el-tooltip
                                v-if="!!item.customerId || !item.projectId"
                                class="box-item"
                                :effect="isDark ? 'dark' : 'light'"
                                :content="
                                    !!item.projectId
                                        ? $t(
                                              'The device has been bound to the customer!'
                                          )
                                        : $t(
                                              'The device is bound to the project!'
                                          )
                                "
                                placement="top-start"
                            >
                                <div class="device-sn">{{ item.sn }}</div>
                            </el-tooltip>
                            <div class="device-info" v-else>
                                <div class="device-sn">{{ item.sn }}</div>
                            </div>
                        </el-checkbox>
                    </div>
                </el-checkbox-group>

                <div
                    v-if="!loading && tableData.length === 0"
                    class="empty-data"
                >
                    {{ $t('暂无数据') }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {
    ref,
    watch,
    defineProps,
    defineEmits,
    onMounted,
    reactive,
    computed,
} from 'vue'
import { useStore } from 'vuex'
import powerApi from '@/apiService/power'
import { useI18n } from 'vue-i18n'
import { debounce } from 'lodash-es'

const { t } = useI18n()
const store = useStore()
const isDark = computed(() => {
    return store.state.theme.isDark
})
const props = defineProps({
    // 已选择的设备ID
    selectedDeviceIds: {
        type: Array,
        default: () => [],
    },
})

const emit = defineEmits(['update:selectedDeviceIds'])

// 暴露重置方法给父组件
const resetFilters = () => {
    // 重置筛选条件
    filterForm.keyword = ''
    filterForm.projectIds = ''
    filterForm.vehicleType = ''
    filterForm.cities = ''
    filterForm.activeDate = ''

    // 重置分页
    pagination.current = 1

    // 重新获取数据
    getDeviceList()
}

// 暴露方法给父组件
defineExpose({
    resetFilters,
})

// 内部状态
const tableData = ref([])
const loading = ref(false)
const pagination = reactive({
    current: 1,
    size: 1000,
    total: 0,
})
const filterForm = reactive({
    keyword: '',
    projectIds: '',
    vehicleType: '',
    cities: '',
    activeDate: '',
})

const projectOptions = ref([])
const vehicleTypeOptions = ref([])
const cityOptions = ref([])
const checkAll = ref(false)
const isIndeterminate = ref(false)

/**
 * 获取可绑定设备列表
 */
const getDeviceList = async () => {
    loading.value = true
    try {
        // 构建请求参数
        const params = {
            current: pagination.current,
            size: pagination.size,
            keyword: filterForm.keyword || undefined,
            projectIds: filterForm.projectIds || undefined,
            vehicleType: filterForm.vehicleType || undefined,
            city: filterForm.cities || undefined,
            // activeDate: filterForm.activeDate || undefined,
            activeStartDate: filterForm.activeDate || undefined,
            activeEndDate: filterForm.activeDate || undefined,
        }

        // // 过滤掉空值
        // Object.keys(params).forEach((key) => {
        //     if (params[key] === undefined || params[key] === '') {
        //         delete params[key]
        //     }
        // })

        const res = await powerApi.getSimplePageList(params)

        if (res.data.code === 0 && res.data.data) {
            tableData.value = res.data.data.records || []
            pagination.total = res.data.data.total || 0
        } else {
            tableData.value = []
            pagination.total = 0
        }

        // 更新全选状态
        updateCheckAllStatus()
    } catch (error) {
        console.error('获取设备列表失败:', error)
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

const getFilterOptions = async () => {
    try {
        // 获取项目列表
        const projectRes = await powerApi.getProjectPageList({
            current: 1,
            size: 1000,
        })
        if (projectRes.data.code === 0 && projectRes.data.data) {
            projectOptions.value = projectRes.data.data.records || []
        }

        // 获取车辆类型
        await store.dispatch('dictionary/getDictionary', 'vehicleType')
        vehicleTypeOptions.value =
            store.state.dictionary.dictionaries.vehicleType || []

        // 获取城市列表
        await getCityOptions()
    } catch (error) {
        console.error('获取筛选选项失败:', error)
    }
}

// 获取城市选项
const getCityOptions = async () => {
    try {
        const res = await powerApi.getReginDeviceTree()
        if (res.data.code === 0 && res.data.data && res.data.data.provinces) {
            cityOptions.value = convertToTree(res.data.data)
        }
    } catch (error) {
        console.error('获取城市列表失败:', error)
    }
}

// 将省市数据转换为级联选择器格式
const convertToTree = (data) => {
    return data.provinces.map((province) => ({
        label: province.province,
        value: province.province,
        children: province.cities.map((city) => ({
            label: city.city,
            value: city.city,
        })),
    }))
}

onMounted(() => {
    getFilterOptions()
})

const handleSearch = () => {
    pagination.current = 1
    getDeviceList()
}

const KeywordSearch = debounce(() => {
    handleSearch()
}, 300)

const handleSelectionChange = (selectedIds) => {
    emit('update:selectedDeviceIds', selectedIds)
    updateCheckAllStatus()
}

// 全选/取消全选
const handleCheckAllChange = (val) => {
    if (val) {
        // 全选：选择所有可选设备
        const selectableIds = tableData.value
            .filter((item) => !item.customerId && !!item.projectId)
            .map((item) => item.id)
        emit('update:selectedDeviceIds', selectableIds)
    } else {
        // 取消全选：清空所有选择
        emit('update:selectedDeviceIds', [])
    }
    updateCheckAllStatus()
}

// 更新全选状态
const updateCheckAllStatus = () => {
    const selectableItems = tableData.value.filter(
        (item) => !item.customerId && !!item.projectId
    )
    const selectableIds = selectableItems.map((item) => item.id)
    const selectedIds = props.selectedDeviceIds.filter((id) =>
        selectableIds.includes(id)
    )

    if (selectableItems.length === 0) {
        checkAll.value = false
        isIndeterminate.value = false
    } else if (selectedIds.length === selectableItems.length) {
        checkAll.value = true
        isIndeterminate.value = false
    } else if (selectedIds.length > 0) {
        checkAll.value = false
        isIndeterminate.value = true
    } else {
        checkAll.value = false
        isIndeterminate.value = false
    }
}

// 监听外部变化
watch(
    () => props.selectedDeviceIds,
    () => {
        updateCheckAllStatus()
    }
)

// 监听内部变化
watch(tableData, () => {
    updateCheckAllStatus()
})
</script>

<style scoped lang="less">
.device-selector-wrap {
    display: flex;
    flex-direction: column;
    height: calc(100% - 52px);
}

.filter-area {
    padding-bottom: 16px;

    .filter-form {
        .full-width {
            width: 100%;
            margin-bottom: 8px;
        }

        .half-width-row {
            display: flex;
            gap: 12px;
            margin-bottom: 8px;

            .half-width {
                flex: 1;
                width: 50%;
            }
        }
    }

    :deep(.el-form-item) {
        margin-bottom: 0;
    }

    :deep(.el-select),
    :deep(.el-cascader),
    :deep(.el-date-picker) {
        width: 100%;
    }
}

.checkbox-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 0;

    .checkbox-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #ebeef5;

        .selected-info {
            color: var(--text-80);
            font-size: 14px;
        }
    }

    .checkbox-list {
        flex: 1;
        overflow-y: auto;
        padding-top: 12px;
        .device-checkbox-group {
            display: flex;
            flex-direction: column;
        }

        .device-checkbox-item {
            transition: all 0.3s;

            &.disabled {
                opacity: 0.6;
            }

            .device-checkbox {
                width: 100%;
                height: 30px;
                :deep(.el-checkbox__label) {
                    width: 100%;
                    padding-left: 8px;
                }
            }

            .device-info {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .device-sn {
                    font-weight: 500;
                    font-size: 14px;
                    color: var(--text-80);
                }

                .device-project {
                    font-size: 12px;
                    color: var(--text-60);
                }

                .bound-tag {
                    font-size: 12px;
                    color: #f56c6c;
                    background-color: #fef0f0;
                    padding: 2px 6px;
                    border-radius: 4px;
                    display: inline-block;
                    width: fit-content;
                }
            }
        }

        .empty-data {
            text-align: center;
            color: #909399;
            font-size: 14px;
            padding: 40px 0;
        }
    }
}

.pagination-area {
    display: flex;
    justify-content: flex-end;
    padding-top: 16px;
}
</style>
