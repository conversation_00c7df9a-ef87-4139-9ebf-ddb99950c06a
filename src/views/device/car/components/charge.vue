<template>
    <div class="chartbox">
        <div :id="idx" style="width: 100%; height: 100%"></div>
    </div>
</template>
<script>
import { updateEcharts, getChargeOption } from '../../const'
import { toRefs, computed, watch, nextTick, reactive } from 'vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import _cloneDeep from 'lodash/cloneDeep'
import { useStore } from 'vuex'
import useTheme from '@/common/useTheme'

export default {
    name: 'barCharts',
    props: {
        chartData: {
            type: Object,
            default: () => ({}),
        },
        chargeSelect: String,
    },
    setup(props) {
        const { chartData, chargeSelect } = toRefs(props)
        const state = reactive({})
        const idx = computed(() => {
            return 'id' + parseInt(Math.random() * 100000000)
        })

        const setOption = () => {
            nextTick(() => {
                if (chartData.value && chartData.value.length) {
                    const dateList = chartData.value.map((item) => {
                        if (chargeSelect.value == 'hour') {
                            return String(item.hour).padStart(2, '0') + ':00'
                        } else if (chargeSelect.value == 'day') {
                            return dayjs(item.date).format('MM/DD')
                        } else if (chargeSelect.value == 'month') {
                            return dayjs(item.date).format('YYYY/MM')
                        }
                        // return dayjs(item.day).format('YYYY/MM/DD')
                    })
                    const chargeCap = []
                    const chargeData = []
                    const dischargeCap = []
                    const dischargeData = []
                    chartData.value.forEach((item) => {
                        chargeData.push(item?.chargeCap || 0)
                        dischargeData.push(item?.dischargeCap || 0)
                        chargeCap.push({
                            value: item?.chargeCap || 0,
                            isBooan: false,
                        })
                        dischargeCap.push({
                            value: item?.dischargeCap || 0,
                            isBooan: false,
                        })
                    })
                    // const isBooan = someMax(
                    //     [...chargeData, ...dischargeData],
                    //     1000
                    // )
                    // if (isBooan) {
                    //     chargeCap.forEach((item) => {
                    //         item.value = roundNumFun(item.value / 1000, 2)
                    //         item.isBooan = true
                    //     })
                    //     dischargeCap.forEach((item) => {
                    //         item.value = roundNumFun(item.value / 1000, 2)
                    //         item.isBooan = true
                    //     })
                    // }
                    const options = _cloneDeep(getChargeOption())
                    options.xAxis.data = dateList
                    options.yAxis.name = 'Ah'
                    options.series[0].data = chargeCap
                    options.series[1].data = dischargeCap
                    options.series[0].type = 'bar'
                    options.series[1].type = 'bar'
                    options.series[0].symbol = 'none'
                    options.series[0].smooth = true
                    options.series[0].showSymbol = false
                    options.series[1].symbol = 'none'
                    options.series[1].smooth = true
                    options.series[1].showSymbol = false
                    if (dateList.length >= 15) {
                        options.series[0].stack = '1'
                        options.series[1].stack = '1'
                    } else {
                        options.series[0].stack = '0'
                        options.series[1].stack = '1'
                    }
                    if (dateList.length >= 15) {
                        options.series[0].barWidth = '8px'
                        options.series[1].barWidth = '8px'
                    } else if (dateList.length >= 8) {
                        options.series[0].barWidth = '10px'
                        options.series[1].barWidth = '10px'
                    } else {
                        options.series[0].barWidth = '16px'
                        options.series[1].barWidth = '16px'
                    }
                    options.tooltip.formatter = function (params) {
                        console.log('[ params ] >', params)
                        return (
                            params[0].name +
                            '<br/>' +
                            params[0].marker +
                            params[0].seriesName +
                            ' : ' +
                            params[0].value +
                            'Ah' +
                            '<br/>' +
                            params[1].marker +
                            params[1].seriesName +
                            ' : ' +
                            params[1].value +
                            'Ah'
                        )
                    }

                    updateEcharts(idx.value, options)
                }
            })
        }
        watch(
            chartData,
            async (val) => {
                if (val) {
                    setOption()
                }
                // 处理数据

                // await initEcharts()
            }
            // { immediate: true }
        )

        const store = useStore()
        const { themeColors, themeChangeComplete } = useTheme()
        const isDark = computed(() => {
            return store.state.theme.isDark
        })
        watch([isDark, themeChangeComplete], ([newIsDark, isComplete]) => {
            // Only update chart when theme change is complete
            if (isComplete) {
                setOption()
            }
        })
        return {
            ...toRefs(state),
            idx,
        }
    },
}
</script>
<style lang="less" scoped>
.chartbox {
    width: 100%;
    height: 306px;
}
</style>
