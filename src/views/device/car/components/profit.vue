<template>
    <div class="chartbox">
        <div :id="idx" style="width: 100%; height: 100%"></div>
    </div>
</template>
<script>
import { updateEcharts, chargeOption, someMax, roundNumFun } from '../../const'
import {
    toRefs,
    computed,
    watch,
    onMounted,
    ref,
    nextTick,
    reactive,
} from 'vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import _cloneDeep from 'lodash/cloneDeep'

export default {
    name: 'barCharts',
    props: {
        chartData: {
            type: Object,
            default: () => ({}),
        },
        chargeSelect: String,
    },
    setup(props) {
        const { chartData, chargeSelect } = toRefs(props)
        const state = reactive({})
        const idx = computed(() => {
            return 'id' + parseInt(Math.random() * 100000000)
        })

        const setOption = () => {
            nextTick(() => {
                const dateList = chartData.value.map((item) => {
                    if (chargeSelect.value == 'hour') {
                        return String(item.hour).padStart(2, '0') + ':00'
                    } else if (chargeSelect.value == 'day') {
                        return dayjs(item.date).format('MM/DD')
                    } else if (chargeSelect.value == 'month') {
                        return dayjs(item.date).format('YYYY/MM')
                    }
                })
                const profit = []
                const profitData = []

                chartData.value.forEach((item) => {
                    profitData.push(item?.profit || 0)
                    profit.push({
                        value: item?.profit || 0,
                        isBooan: false,
                    })
                })
                const isBooan = someMax([...profitData], 10000)
                if (isBooan) {
                    profit.forEach((item) => {
                        item.value = roundNumFun(item.value / 10000, 2)
                        item.isBooan = true
                    })
                }
                const options = _cloneDeep(chargeOption)
                options.grid.left = 50
                options.legend.data = ['收益']
                options.xAxis.data = dateList
                options.yAxis.name = '元'
                options.series[0].data = profit
                options.series[0].type = 'bar'
                options.series[0].name = '收益'
                options.series[0].symbol = 'none'
                options.series[0].smooth = true
                options.series[0].showSymbol = false
                options.series[1] = undefined
                // if(profit.length >= 12) {

                // }
                if (profit.length >= 13) {
                    options.series[0].barWidth = '8px'
                } else if (profit.length >= 8) {
                    options.series[0].barWidth = '12px'
                } else {
                    options.series[0].barWidth = '20px'
                }
                if (isBooan) {
                    options.yAxis.name = '万元'
                }
                updateEcharts(idx.value, options)
            })
        }
        watch(
            chartData,
            async (val) => {
                if (val) {
                    setOption()
                }
                // 处理数据

                // await initEcharts()
            }
            // { immediate: true }
        )

        return {
            ...toRefs(state),
            idx,
        }
    },
}
</script>
<style lang="less" scoped>
.chartbox {
    width: 100%;
    height: 306px;
}
</style>
