<template>
    <div
        class="flex items-center justify-between pl-2 mb-2 text-secondar-text dark:text-60-dark opacity-80 overflow leading-6"
    >
        <div>{{ $t('Device No') }}：{{ data.sn }}</div>
        <div
            class="flex items-center gap-x-1"
            :style="{
                color: getState(data.status, 'power').color,
            }"
        >
            <i :class="['iconfont', getState(data.status, 'power').icon]"></i
            ><span class="text-title dark:text-title-dark">{{
                getState(data.status, 'power').label
                    ? $t(getState(data.status, 'power').label)
                    : ''
            }}</span>
        </div>
        <!-- <div
            class="flex items-center gap-x-1 px-2"
            style="color: #52c41a; background: rgba(82, 196, 26, 0.1)"
        >
            <div class="text-xs leading-5.5">在线效率:{{ 99 }}</div>
        </div> -->
    </div>
    <div
        class="flex px-2 gap-x-2 py-1.5 mb-2 text-secondar-text dark:text-60-dark bg-f5f7f7 dark:bg-ffffff-dark rounded"
    >
        <div
            class="text-xs flex-1 overflow-hidden overflow-ellipsis whitespace-nowrap"
        >
            {{ $t('BatteryNo') }}：{{ data.batteryNo || '-' }}
        </div>
        <div
            class="text-xs flex-1 overflow-hidden overflow-ellipsis whitespace-nowrap"
        >
            HWID：
            <el-tooltip
                v-if="data.hwid"
                class="box-item"
                :effect="isDark ? 'dark' : 'light'"
                :content="data.hwid"
                placement="top-start"
            >
                <span class="text-secondar-text dark:text-60-dark">
                    {{ data.hwid }}
                </span>
            </el-tooltip>
            <span v-else>-</span>
        </div>
    </div>
    <div class="flex justify-between px-2">
        <div class="" style="width: 120px; padding-left: 0">
            <div class="text-secondar-text dark:text-60-dark">
                {{ $t('BMS型号') }}
            </div>
            <div
                class="text-base leading-5 font-medium dark:text-title-dark mt-2"
            >
                <template v-if="data.model?.length < 10">
                    <span class="dark:text-title-dark">
                        {{ data.model }}
                    </span>
                </template>
                <el-tooltip
                    v-else-if="data.model"
                    class="box-item"
                    :effect="isDark ? 'dark' : 'light'"
                    :content="data.model"
                    placement="top-start"
                >
                    <span
                        class="dark:text-title-dark inline-block w-28 overflow-hidden overflow-ellipsis whitespace-nowrap"
                    >
                        {{ data.model }}
                    </span>
                </el-tooltip>
            </div>
        </div>
        <a-divider
            type="vertical"
            class="h-12 m-0"
            style="border-color: var(--border)"
        />
        <div class="text-center" style="width: 96px">
            <div class="inline-block text-left">
                <div class="text-secondar-text dark:text-60-dark">
                    {{ $t('Capacity') }}
                </div>
                <div
                    class="text-base leading-5 font-medium dark:text-title-dark mt-2"
                >
                    {{ data.ratedCapacity || 0
                    }}<span class="text-xs"> Ah</span>
                </div>
            </div>
        </div>
        <a-divider
            type="vertical"
            class="h-12 m-0"
            style="border-color: var(--border); width: 2px"
        />
        <div class="text-center" style="width: 108px">
            <div class="inline-block text-left">
                <div class="text-secondar-text dark:text-60-dark">
                    {{ $t('Charging duration') }}
                </div>
                <div
                    class="text-base leading-5 font-medium dark:text-title-dark mt-2"
                >
                    {{ data.chgTimeSum || 0 }}<span class="text-xs"> h</span>
                </div>
            </div>
        </div>
        <a-divider
            type="vertical"
            class="h-12 m-0"
            style="border-color: var(--border)"
        />
        <div class="text-center" style="width: 114px; padding-left: 10px">
            <div class="inline-block text-right">
                <div class="text-secondar-text dark:text-60-dark">
                    {{ $t('service_expire_time') }}
                </div>
                <div
                    class="text-base leading-5 font-medium dark:text-title-dark mt-2"
                >
                    {{
                        data.serviceExpireDate
                            ? dayjs(data.serviceExpireDate).format('YYYY/MM/DD')
                            : '-'
                    }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { getState } from '@/common/util.js'
import dayjs from 'dayjs'
const { t, locale } = useI18n()
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
})
const store = useStore()
const isDark = computed(() => {
    return store.state.theme.isDark
})
</script>

<style></style>
