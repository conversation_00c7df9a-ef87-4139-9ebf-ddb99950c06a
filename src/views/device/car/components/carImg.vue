<template>
    <div class="w-full h-full">
        <img
            src="@/assets/device/bus.png"
            v-if="vehicleType === 'bus'"
            alt=""
            srcset=""
        />
        <img
            src="@/assets/device/forklift.png"
            v-else-if="vehicleType === 'forklift'"
            alt=""
            srcset=""
        />
        <img
            src="@/assets/device/loader.png"
            v-else-if="vehicleType === 'loader'"
            alt=""
            srcset=""
        />
        <img
            src="@/assets/device/tractor.png"
            v-else-if="vehicleType === 'tractor'"
            alt=""
            srcset=""
        />
        <!-- 叉车和升降车目前用同一张图 -->
        <img
            src="@/assets/device/forklift.png"
            v-else-if="vehicleType === 'liftTruck'"
            alt=""
            srcset=""
        />
        <img src="@/assets/device/tractor.png" v-else alt="" srcset="" />
    </div>
</template>

<script setup>
const props = defineProps({
    vehicleType: {
        type: String,
        default: '1',
    },
})
</script>

<style lang="less" scoped>
img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}
</style>
