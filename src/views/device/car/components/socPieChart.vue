<template>
    <div
        ref="chartContainer"
        style="width: 220px; height: 210px; margin: 0 auto"
    ></div>
</template>

<script>
import { ref, onMounted, watch, computed } from 'vue'
import * as echarts from 'echarts'
import { getCarPieOption } from '@/views/device/const'
import { useI18n } from 'vue-i18n'
import { echartsColorVars, getThemeColor } from '@/common/util'
import { useStore } from 'vuex'
import useTheme from '@/common/useTheme'
export default {
    name: 'soc<PERSON>ie<PERSON><PERSON>',
    props: {
        data: {
            type: Object,
            required: true,
        },
    },
    setup(props) {
        const chartContainer = ref(null)
        let chart = null

        const { t, locale } = useI18n()

        const initChart = () => {
            if (chartContainer.value) {
                chart = echarts.init(chartContainer.value)
                updateChart()
            }
        }

        const updateChart = () => {
            const { data } = props
            const options = getCarPieOption(t('SOC Distribution'), [
                {
                    value: data['1'],
                    name: '>60%',
                    itemStyle: {
                        color: 'rgba(111, 190, 206, 1)',
                        borderColor: '#fdfdfd',
                        borderWidth: 2,
                    },
                },
                {
                    value: data['2'],
                    name: '20%~60%',
                    itemStyle: {
                        color: 'rgba(111, 190, 206, 0.6)',
                        borderColor: '#fdfdfd',
                        borderWidth: 2,
                    },
                },
                {
                    value: data['3'],
                    name: '<20%',
                    itemStyle: {
                        color: 'rgba(111, 190, 206, 0.4)',
                        borderColor: '#fdfdfd',
                        borderWidth: 2,
                    },
                },
            ])
            options.title.top = locale.value == 'en' ? '64px' : '74px'
            chart.setOption(options)
        }

        onMounted(() => {
            initChart()
        })

        watch(
            () => props.data,
            () => {
                updateChart()
            },
            { deep: true }
        )

        const store = useStore()
        const { themeChangeComplete } = useTheme()
        const isDark = computed(() => {
            return store.state.theme.isDark
        })
        watch([isDark, themeChangeComplete], ([newIsDark, isComplete]) => {
            // Only update chart when theme change is complete
            if (isComplete) {
                console.log(isComplete, '111')
                updateChart()
            }
        })
        return { chartContainer }
    },
}
</script>

<style scoped>
/* 如果需要额外的样式,可以在这里添加 */
</style>
