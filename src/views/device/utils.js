export const decorateRankRatioFromSelfList = list => {
    let max = 0
    list.forEach(item => {
        if (+max < +item.amount) max = +item.amount
    })
    if (max) {
        list.forEach(item => {
            item.ratio = +(item.amount / max).toFixed(2)
        })
    }
    return list
}
export const transformNumber = (eformat) => {
    let number = eformat
    let tmpVal = '' + eformat
    let ee = tmpVal.replace(/[\d.-]/g, '')
    if (ee) {
      if (ee === 'E' || ee === 'e') {
        let pos = tmpVal.indexOf('E')
        if (pos === -1) {
          pos = tmpVal.indexOf('e')
        }
        let power = tmpVal.substr(pos + 1)
        if (power === '') {
          let baseValue = tmpVal.substr(0, pos)
          number = baseValue
          // 不调用Number函数了，防止转换为科学计数法了
        } else {
          let baseValue = tmpVal.substr(0, pos)
          let character = power.substr(0, 1)
          if (character === '-') {
            let times = power.substr(1)
            if (times === '' || times === '0') {
              number = baseValue 
              // 不调用Number函数了，防止转换为科学计数法了
            } else {
              times = Number(times)
              let baseValueLength = baseValue.length
              let floatLength = baseValue.indexOf('.') + 1
              let length = baseValueLength - floatLength
              number = eformat.toFixed(length + times)
              // 这里只能以字符串形式输出了，不能再调用Number函数
            }                  
          } else {
            let powerVal = Math.pow(10, power)
            number = baseValue * powerVal
          }
        }
      }
    }
    return number
}