<template>
    <div class="tree-item">
        <el-collapse v-model="activeNames">
            <el-collapse-item :name="item.id">
                <template #title>
                    <div class="item-title">
                        <div class="flex-1 overflow text-left name">
                            {{ item.name }}
                        </div>
                        <span
                            class="custom-icon"
                            v-if="item.children && item.children.length"
                            >{{
                                activeNames.includes(item.id) ? '-' : '+'
                            }}</span
                        >
                    </div>
                </template>

                <div
                    v-if="item.children && item.children.length"
                    class="children-container"
                >
                    <tree-item
                        v-for="child in item.children"
                        :key="child.id"
                        :item="child"
                    />
                </div>
            </el-collapse-item>
        </el-collapse>
    </div>
</template>

<script setup>
import { ref } from 'vue'

const activeNames = ref([])

defineProps({
    item: {
        type: Object,
        required: true,
    },
})
</script>

<style lang="less" scoped>
.tree-item {
    width: 100%;
    padding-left: 24px;
    padding-right: 16px;
    position: relative;
    &::before {
        content: '';
        display: inline-block;
        width: 12px;
        height: 12px;
        border-bottom-left-radius: 2px;
        border-left: 1px solid #eee;
        border-bottom: 1px solid #eee;
        margin-right: 10px;
        position: absolute;
        left: 4px;
        top: 14px;
    }

    :deep(.el-collapse) {
        border: none;
    }

    :deep(.el-collapse-item__header) {
        border: none;
        height: 40px;
        line-height: 40px;
        // padding-left: 10px;
        background-color: #fff;

        // 隐藏默认的箭头图标
        .el-collapse-item__arrow {
            display: none;
        }
    }

    :deep(.el-collapse-item__content) {
        // padding-left: 20px;
        padding-bottom: 0;
    }

    :deep(.el-collapse-item__wrap) {
        border: none;
    }

    .item-title {
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        .custom-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            margin-right: 8px;
            font-size: 16px;
            color: #909399;
            cursor: pointer;
        }
    }

    .children-container {
        width: 100%;
    }
    &:hover {
        &::before {
            border-color: var(--themeColor);
        }
    }
    .tree-item {
        &:hover {
            > .el-collapse
                > .el-collapse-item
                > .el-collapse-item__header
                > .item-title
                > .name {
                color: var(--themeColor);
            }
        }
    }
}
</style>