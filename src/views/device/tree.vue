<template>
    <div class="tree-container">
        <!-- 最外层不需要collapse，直接使用递归组件 -->
        <tree-item v-for="item in data" :key="item.id" :item="item" />
    </div>
</template>
  
  <script setup>
import { ref } from 'vue'
import TreeItem from './TreeItem.vue'

defineProps({
    data: {
        type: Array,
        default: () => [],
    },
})
</script>
  
  <style lang="less" scoped>
.tree-container {
    width: 100%;
}
</style>