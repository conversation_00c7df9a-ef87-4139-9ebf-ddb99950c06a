<template>
    <div
        class="w-full h-full relative text-center text-title dark:text-title-dark"
    >
        <!-- 圈1 -->
        <div class="w-28 absolute left-0 top-0">
            <div class="relative circle circle-1">
                <div
                    class="circle-box circle-box1 rounded-full border border-border"
                >
                    <div class="leading-5">
                        <iconSvg
                            name="dw"
                            className="dw"
                            class="w-6 h-6 mb-1"
                        />
                        <div v-show="gridPower != 0">
                            {{ gridPower }}
                        </div>
                        <div v-show="gridPower != 0">kW</div>
                    </div>
                </div>
            </div>

            <div class="mt-6 leading-5">{{ $t('station_dianwang') }}</div>
        </div>

        <!-- 圈2 -->
        <div class="absolute right-0 top-0">
            <div class="relative circle circle-2">
                <div
                    class="circle-box circle-box2 rounded-full border border-border"
                >
                    <div class="leading-5">
                        <iconSvg
                            name="cn"
                            className="cn"
                            class="w-6 h-6 mb-1"
                        />
                        <div v-show="Math.abs(bmsPower) > 1">
                            {{ Math.abs(bmsPower) > 1 ? bmsPower : 0 }}
                        </div>
                        <div v-show="Math.abs(bmsPower) > 1">kW</div>
                    </div>
                </div>
            </div>
            <div class="mt-6 leading-5">{{ $t('station_chuneng') }}</div>
        </div>

        <!-- 圈3 -->
        <div class="w-28 absolute left-1/2 -ml-14 bottom-0">
            <div class="relative circle circle-3">
                <div
                    class="circle-box circle-box3 rounded-full border border-border"
                >
                    <div class="leading-5">
                        <iconSvg
                            name="fz"
                            className="fz"
                            class="w-6 h-6 mb-1"
                        />
                    </div>
                </div>
            </div>
            <div class="mt-6 leading-5">{{ $t('station_fuzai') }}</div>
        </div>

        <!-- 线框 -->
        <div
            class="absolute z-20"
            style="
                width: calc(100% - 220px);
                height: calc(100% - 213px);
                left: 110px;
                bottom: 158px;
            "
        >
            <!-- 线1 -->
            <div class="absolute w-1/2 h-2 left-0 -top-1 overflow-hidden">
                <div
                    class="absolute w-full left-0 top-1 border-b border-dashed"
                    style="border-color: #e1b5fe"
                ></div>
                <!-- 箭头1 -->
                <div
                    v-if="gridPower !== 0 && status !== 3"
                    class="w-10 h-0 border-b border-solid absolute top-1"
                    :class="
                        gridPower > 0 ? 'direction-dw-reverse' : 'direction-dw'
                    "
                    style="border-color: #e1b5fe"
                >
                    <div class="absolute w-0 h-2 dw-arrow"></div>
                </div>
            </div>
            <!-- 中心点 -->
            <div
                class="absolute left-1/2 top-0 z-10"
                style="
                    width: 7px;
                    height: 7px;
                    border: 1px solid #d0d0d0;
                    border-radius: 50%;
                    background: #fff;
                    margin-left: -3px;
                    margin-top: -3px;
                "
            ></div>
            <!-- 线2 -->
            <div class="absolute w-1/2 h-2 right-0 -top-1 overflow-hidden">
                <div
                    class="absolute w-full left-0 top-1 border-b border-dashed"
                    style="border-color: #71d6d7"
                ></div>
                <!-- 箭头2 -->
                <div
                    v-if="
                        Math.abs(bmsPower) > 1 && status !== 3 && bmsPower !== 0
                    "
                    class="w-10 h-0 border-b border-solid absolute top-1"
                    :class="
                        bmsPower > 0
                            ? 'direction-cn-reverse'
                            : bmsPower < 0
                            ? 'direction-cn'
                            : ''
                    "
                    style="border-color: #71d6d7"
                >
                    <div class="absolute w-0 h-2 cn-arrow"></div>
                </div>
            </div>
            <!-- 线3 -->
            <div
                class="absolute w-2 h-full left-1/2 -ml-1 top-0 overflow-hidden"
            >
                <div
                    class="absolute h-full left-1 top-0 border-r border-dashed"
                    style="border-color: #a6e0fb"
                ></div>
                <!-- 箭头3 -->
                <div
                    class="h-10 w-0 border-r border-solid absolute left-1 top-0"
                    style="border-color: #a6e0fb"
                    :class="status === 3 ? 'border-none' : 'direction-b'"
                >
                    <div class="absolute w-2 fz-arrow"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { watch } from 'vue'
import { useI18n } from 'vue-i18n'
export default {
    props: {
        bmsPower: Number,
        gridPower: Number,
        status: Number,
    },
    setup(props) {
        watch(props, () => {
            // 监听数据，如果bmsPower的绝对值小雨
        })
        const { t, locale } = useI18n()
        return {
            t,
            locale,
        }
    },
}
</script>

<style lang="less" scoped>
// 添加左右移动动画
@keyframes arrowLMoveToLeft {
    0% {
        right: -40px;
    }
    100% {
        right: 100%;
    }
}
@keyframes arrowLMoveToRight {
    0% {
        left: -40px;
    }
    100% {
        left: 100%;
    }
}
.cn-arrow {
    width: 0;
    height: 7px;
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
    margin-top: -3px;
}
.direction-cn {
    animation: arrowLMoveToLeft 3s linear infinite;
    .cn-arrow {
        border-right: 6px solid #71d6d7;
        left: 0;
    }
}
.direction-cn-reverse {
    animation: arrowLMoveToRight 3s linear infinite;
    .cn-arrow {
        border-left: 6px solid #71d6d7;
        right: 0;
    }
}
@keyframes arrowRMoveToLeft {
    0% {
        right: -40px;
    }
    100% {
        right: 100%;
    }
}
@keyframes arrowRMoveToRight {
    0% {
        left: -40px;
    }
    100% {
        left: 100%;
    }
}

.dw-arrow {
    width: 0;
    height: 7px;
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
    margin-top: -3px;
}
.direction-dw {
    animation: arrowRMoveToLeft 3s linear infinite;
    .dw-arrow {
        border-right: 6px solid #e1b5fe;
        left: 0;
    }
}
.direction-dw-reverse {
    animation: arrowRMoveToRight 3s linear infinite;
    .dw-arrow {
        border-left: 6px solid #e1b5fe;
        right: 0;
    }
}

@keyframes arrowBMoveToBottom {
    0% {
        top: -40px;
    }
    100% {
        top: 100%;
    }
}
@keyframes arrowBMoveToTop {
    0% {
        top: 100%;
    }
    100% {
        top: -40px;
    }
}

.fz-arrow {
    width: 7px;
    height: 0;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    margin-left: -3px;
}
.direction-b {
    animation: arrowBMoveToBottom 2s linear infinite;
    .fz-arrow {
        border-top: 6px solid #a6e0fb;
        bottom: 0;
    }
}
.direction-b-reverse {
    animation: arrowBMoveToTop 2s linear infinite;
    .fz-arrow {
        border-bottom: 6px solid #a6e0fb;
        top: 0;
    }
}
// 添加放大缩小动画
@keyframes scaleToLg {
    0% {
        transform: scale(1.3);
    }
    50% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.3);
    }
}

.circle {
    width: 110px;
    height: 110px;
    position: relative;
    // background: #fff;
    z-index: 2;
    &::before {
        display: block;
        content: '';
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 1px solid #f2f2f2;
        position: absolute;
        left: 0;
        top: 0;
        animation: scaleToLg 2s linear infinite;
        z-index: 0;
    }
    &.circle-1 {
        &::before {
            border-color: #f7ecfe;
            // background: #fdf9ff;
        }
    }
    &.circle-2 {
        &::before {
            border-color: #eaffe6;
            // background: #fdfffc;
        }
    }
    &.circle-3 {
        &::before {
            border-color: #ebf4ff;
            // background: #f9fdff;
        }
    }
    .circle-box {
        width: 110px;
        height: 110px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        &.circle-box1 {
            border-color: #e1b5fd;
        }
        &.circle-box2 {
            border-color: #71d6d7;
        }
        &.circle-box3 {
            border-color: #a6e0fb;
        }
    }
}
</style>
