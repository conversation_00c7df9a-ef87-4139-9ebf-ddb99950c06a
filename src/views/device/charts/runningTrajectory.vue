<template>
    <div>
        <div id="runningTrajectory" style="height: 400px; width: 100%"></div>
    </div>
</template>

<script setup>
import { onMounted, watch, ref, nextTick } from 'vue'
import * as echarts from 'echarts'
import { fullTimePeriod } from '@/views/strategy/util.js'
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
})
function parseTime(timeStr) {
    const [hours, minutes] = timeStr.split(':').map(Number)
    return hours * 60 + minutes
}

function formatTime(minutes) {
    const hours = Math.floor(minutes / 60)
        .toString()
        .padStart(2, '0')
    const mins = (minutes % 60).toString().padStart(2, '0')
    return `${hours}:${mins}`
}

function generate15MinIntervals(start, end, value) {
    let intervals = {}
    for (let time = start + 15; time <= end; time += 15) {
        intervals[formatTime(time)] = value.toString()
    }
    return intervals
}

function transformData(source) {
    let result = {}
    for (let item of source) {
        const key = Object.keys(item)[0]
        const value = item[key]
        const [startTime, endTime] = key.split('-').map(parseTime)
        Object.assign(
            result,
            generate15MinIntervals(startTime - 15, endTime, value)
        )
    }
    return Object.values(result)
}

let i = 0
const options = {
    title: {
        text: '',
    },
    tooltip: {
        trigger: 'axis',
    },
    color: ['rgba(22, 119, 255, 1)', 'rgba(255, 112, 0,1)'],
    legend: {
        icon: 'rect',
        itemWidth: 10,
        itemHeight: 10,
        data: ['今日实际运行功率', '今日预测运行功率'],
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
    },

    xAxis: {
        type: 'category',
        boundaryGap: true,
        axisTick: {
            alignWithLabel: true,
        },
        axisLabel: {
            width: 100,
            overflow: 'truncate',
            formatter(v) {
                return ' '.repeat(3) + v + ' '.repeat(3)
            },
        },
        data: [
            '00:00',
            '00:15',
            '00:30',
            '00:45',
            '01:00',
            '01:15',
            '01:30',
            '01:45',
            '02:00',
            '02:15',
            '02:30',
            '02:45',
            '03:00',
            '03:15',
            '03:30',
            '03:45',
            '04:00',
            '04:15',
            '04:30',
            '04:45',
            '05:00',
            '05:15',
            '05:30',
            '05:45',
            '06:00',
            '06:15',
            '06:30',
            '06:45',
            '07:00',
            '07:15',
            '07:30',
            '07:45',
            '08:00',
            '08:15',
            '08:30',
            '08:45',
            '09:00',
            '09:15',
            '09:30',
            '09:45',
            '10:00',
            '10:15',
            '10:30',
            '10:45',
            '11:00',
            '11:15',
            '11:30',
            '11:45',
            '12:00',
            '12:15',
            '12:30',
            '12:45',
            '13:00',
            '13:15',
            '13:30',
            '13:45',
            '14:00',
            '14:15',
            '14:30',
            '14:45',
            '15:00',
            '15:15',
            '15:30',
            '15:45',
            '16:00',
            '16:15',
            '16:30',
            '16:45',
            '17:00',
            '17:15',
            '17:30',
            '17:45',
            '18:00',
            '18:15',
            '18:30',
            '18:45',
            '19:00',
            '19:15',
            '19:30',
            '19:45',
            '20:00',
            '20:15',
            '20:30',
            '20:45',
            '21:00',
            '21:15',
            '21:30',
            '21:45',
            '22:00',
            '22:15',
            '22:30',
            '22:45',
            '23:00',
            '23:15',
            '23:30',
            '23:45',
            '24:00',
        ],
    },
    yAxis: {
        type: 'value',
        name: '功率(kW)',
        nameTextStyle: {
            align: 'center',
        },
    },
    series: [
        {
            name: '今日实际运行功率',
            type: 'line',
            step: 'start',
            symbol: 'none',
            symbolSize: 0,
            data: [],

            areaStyle: {
                opacity: 0.4,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                        offset: 0,
                        color: 'rgba(22, 119, 255, 1)',
                    },
                    {
                        offset: 1,
                        color: 'rgba(178, 210 ,255,1)',
                    },
                ]),
            },
        },
        {
            name: '今日预测运行功率',
            type: 'line',
            step: 'start',
            symbol: 'none',
            symbolSize: 0,
            data: [],
            areaStyle: {
                opacity: 0.4,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                        offset: 0,
                        color: 'rgba(255, 112, 0,1)',
                    },
                    {
                        offset: 1,
                        color: 'rgba(255, 228, 207,1)',
                    },
                ]),
            },
        },
    ],
}
const realStrategySegments = ref([])
const aiRecommendStrategySegments = ref([])

// const formatData = (data) => {
//     const timeList = new Array(96).fill('')
//     const fullTimes = ['00:00', ...fullTimePeriod]
//     data.realStrategySegments.forEach((item) => {
//         const key = Object.keys(item)[0]
//         const val = item[key]
//         const startTime = key.split('-')[0]
//         const endTime = key.split('-')[1]
//         timeList.fill(
//             val,
//             fullTimes.findIndex((i) => i == startTime),
//             fullTimes.findIndex((i) => i == endTime)
//         )
//     })
//     realStrategySegments.value = timeList
//     const timeListAi = new Array(96).fill('')
//     data.aiRecommendStrategySegments.forEach((item) => {
//         const key = Object.keys(item)[0]
//         const val = item[key]
//         const startTime = key.split('-')[0]
//         const endTime = key.split('-')[1]
//         timeListAi.fill(
//             val,
//             fullTimes.findIndex((i) => i == startTime),
//             fullTimes.findIndex((i) => i == endTime)
//         )
//     })
//     aiRecommendStrategySegments.value = timeListAi
//     // realStrategySegments.value = transformData(data.realStrategySegments)
//     // aiRecommendStrategySegments.value = transformData(
//     //     data.aiRecommendStrategySegments
//     // )

//     options.series[1].data = realStrategySegments.value
//     options.series[0].data = aiRecommendStrategySegments.value
//     nextTick(() => {
//         const myChart = echarts.init(
//             document.getElementById('runningTrajectory')
//         )
//         myChart.setOption(options)
//     })
// }

const formatData = (data) => {
    realStrategySegments.value = data.realStrategySegments.map((item) => {
        return item.power
    })
    aiRecommendStrategySegments.value =
        data.aiRecommendStrategySegments?.length > 0
            ? data.aiRecommendStrategySegments.map((item) => {
                  return item.power
              })
            : []
    options.series[0].data = realStrategySegments.value
    options.series[1].data = aiRecommendStrategySegments.value
    nextTick(() => {
        const myChart = echarts.init(
            document.getElementById('runningTrajectory')
        )
        myChart.setOption(options)
    })
}
watch(
    () => props.data,
    (newVal) => {
        if (newVal && Object.keys(newVal).length) {
            formatData(newVal)
        }
    },
    { deep: true, immediate: true }
)

onMounted(() => {})
</script>

<style lang="less" scoped></style>
