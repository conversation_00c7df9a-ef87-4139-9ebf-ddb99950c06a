<template>
    <div>
        <div class="flex gap-x-6">
            <div class="flex-1">
                <div class="mb-4">
                    <span class="mr-2">{{ $t('Social Contribution') }}</span
                    ><a-tooltip
                        :title="
                            $t(
                                'The overall calculation logic of such data indicators is based on basic data such as system profit and electricity savings, combined with parameters such as carbon emission factors, to calculate the quantitative contribution of the energy storage system in energy conservation and emission reduction.'
                            )
                        "
                    >
                        <QuestionCircleOutlined />
                    </a-tooltip>
                </div>
                <div class="flex justify-between gap-x-3">
                    <div class="flex-1 px-10 py-9 h-40 flex-item1">
                        <div class="flex-box">
                            <div class="felx-item-top">
                                <span
                                    class="flex-number text-3.5xl leading-10 mb-1 font-bold"
                                    >{{
                                        kgUnitg(
                                            socialContribution.savingCarbonEmission
                                        )
                                    }}</span
                                >
                                <span
                                    class="flex-unit text-secondar-text dark:text-60-dark ml-2"
                                ></span>
                            </div>
                            <div class="felx-item-bottom">
                                <span class="flex-title">{{
                                    $t('Standard coal saved (tons)')
                                }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex-1 px-10 py-9 h-40 flex-item2">
                        <div class="flex-box">
                            <div class="felx-item-top">
                                <span
                                    class="flex-number text-3.5xl leading-10 mb-1 font-bold"
                                    >{{
                                        kgUnitg(
                                            socialContribution?.savingCarbonDioxideEmission
                                        )
                                    }}</span
                                >
                                <span
                                    class="flex-unit text-secondar-text dark:text-60-dark ml-2"
                                ></span>
                            </div>
                            <div class="felx-item-bottom">
                                <span class="flex-title">{{
                                    $t('CO₂ emission reduction (tons)')
                                }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex-1 px-10 py-9 h-40 flex-item3">
                        <div class="flex-box">
                            <div class="felx-item-top">
                                <span
                                    class="flex-number text-3.5xl leading-10 mb-1 font-bold"
                                    >{{
                                        socialContribution?.equivalentTreesQuantity ||
                                        socialContribution?.equivalentTreesQuantity ==
                                            0
                                            ? socialContribution.equivalentTreesQuantity
                                            : '-'
                                    }}</span
                                >
                                <span
                                    class="flex-unit text-secondar-text dark:text-60-dark ml-2"
                                >
                                </span>
                            </div>
                            <div class="felx-item-bottom">
                                <span class="flex-title">{{
                                    $t(
                                        'Equivalent tree planting amount (pieces)'
                                    )
                                }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { QuestionCircleOutlined } from '@ant-design/icons-vue'
// import BarProgress from '../components/barProgress.vue'
import { ref, watch } from 'vue'

import { someMax, kgUnitg, isUnits, roundNumFun } from '../const'
export default {
    components: {
        QuestionCircleOutlined,
        // BarProgress,
    },
    props: {
        dataSource: {
            type: Array,
            default: () => [],
        },
        socialContribution: {
            type: Object,
            default: () => ({}),
        },
        profitUnit: {
            type: String,
            default: () => '元',
        },
    },
    setup(props) {
        const profits = ref([])
        watch(
            () => props.dataSource,
            (val) => {
                if (val) {
                    profits.value = val.filter((item) => {
                        return item.profit > 10000
                    })

                    const isBoolean = profits.value.length
                }
            },
            { immediate: true }
        )
        return {
            someMax,
            kgUnitg,
            isUnits,
            profits,
        }
    },
}
</script>

<style lang="less" scoped>
.rank-header {
    width: 62px;
    background: linear-gradient(270deg, #ffffff 0%, #f6f6f6 100%);
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    border: 2px solid #ffffff;
}
.flex-item1 {
    background-image: url('../../../assets/device/bg-3.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.flex-item2 {
    background-image: url('../../../assets/device/bg-2.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.flex-item3 {
    background-image: url('../../../assets/device/bg-1.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
}
</style>
