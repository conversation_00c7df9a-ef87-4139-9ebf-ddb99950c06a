<template>
    <div class="chartbox">
        <div :id="idx" style="width: 100%; height: 100%"></div>
    </div>
</template>
<script>
import { updateEcharts, someMax, roundNumFun, earningsOption } from '../const'
import { toRefs, computed, watch, nextTick, reactive } from 'vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import _cloneDeep from 'lodash/cloneDeep'
import { useI18n } from 'vue-i18n'
export default {
    name: 'barCharts',
    props: {
        chartData: {
            type: Object,
            default: () => ({}),
        },
        incomeSelect: String,
        xType: {
            type: String,
            default: '1',
        },
    },
    setup(props) {
        const { t, locale } = useI18n()
        const { chartData, incomeSelect, xType } = toRefs(props)
        const state = reactive({})
        const idx = computed(() => {
            return 'id1' + parseInt(Math.random() * 100000000)
        })

        const setOption = () => {
            nextTick(() => {
                const dateList = chartData.value.map((item) => {
                    if (xType.value == '1') {
                        return dayjs(item.date).format('YYYY/MM/DD')
                    } else if (xType.value == '2') {
                        return dayjs(item?.date).format('YYYY/MM')
                    }
                })

                const profit = []
                const profitData = []

                chartData.value.forEach((item) => {
                    profitData.push(item?.profit || 0)
                    profit.push({
                        value: item?.profit || 0,
                        isBooan: false,
                        isDate: incomeSelect.value == 3 ? true : false,
                    })
                })

                const isBooan = someMax(profitData, 10000)

                const options = _cloneDeep(earningsOption)
                if (isBooan && locale.value == 'zh') {
                    profit.forEach((item) => {
                        item.value = roundNumFun(item.value / 10000, 2)
                        item.isBooan = true
                    })
                    options.yAxis.name = '收益/万元'
                }

                options.xAxis.data = dateList
                options.yAxis.splitNumber = 5
                options.series[0].data = profit

                if (profit.length >= 25) {
                    options.series[0].barWidth = '20px'
                } else if (profit.length >= 12) {
                    options.series[0].barWidth = '30px'
                } else {
                    options.series[0].barWidth = '35px'
                }
                // updateEcharts(id, options)
                // echartInit(idx, options)
                updateEcharts(idx.value, options)
            })
        }
        watch(
            chartData,
            async (val) => {
                if (val) {
                    setOption()
                }
                // 处理数据

                // await initEcharts()
            }
            // { immediate: true }
        )

        return {
            ...toRefs(state),
            idx,
        }
    },
}
</script>
<style lang="less" scoped>
.chartbox {
    height: 306px;
}
</style>
