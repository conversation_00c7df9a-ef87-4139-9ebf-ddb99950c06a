<template>
    <el-drawer
        :model-value="visible"
        size="486"
        @close="onClose"
        :maskClosable="true"
        :show-close="false"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header text-primary-text dark:text-80-dark">
                    <span>{{
                        isAddNew ? $t('AddStation') : $t('bianjizhandianxinxi')
                    }}</span>
                </div>
                <div class="flex gap-x-3">
                    <el-button plain round @click="onClose">
                        <span>{{ $t('Cancle') }}</span>
                    </el-button>
                    <confirm-button
                        v-if="showConfirm"
                        :title="$t('biangengriqi')"
                        @confirm="onSave"
                        placement="bottom-end"
                    >
                        <template #reference>
                            <el-button plain round type="primary">
                                <span>{{ $t('Save') }}</span>
                            </el-button>
                        </template>
                    </confirm-button>
                    <el-button
                        v-else
                        plain
                        round
                        @click="onSave"
                        :loading="addLoading"
                        type="primary"
                        >{{ $t('Save') }}</el-button
                    >
                </div>
            </div>
        </template>
        <div class="add-site">
            <a-form
                ref="formRef"
                :model="formState"
                :rules="rules"
                autocomplete="off"
                :hideRequiredMark="true"
                :label-col="labelCol"
                labelAlign="left"
            >
                <div>
                    <a-form-item
                        :label="$t('station_bangdingqiye')"
                        name="orgId"
                    >
                        <a-tree-select
                            v-model:value="formState.orgId"
                            style="width: 100%"
                            :replaceFields="{
                                title: 'name',
                                children: 'children',
                                key: 'id',
                                value: 'id',
                            }"
                            :tree-data="treeData"
                            :placeholder="$t('placeholder_qingxuanze')"
                            tree-default-expand-all
                            :disabled="inputDisabled"
                        >
                        </a-tree-select>
                    </a-form-item>

                    <a-form-item
                        :label="$t('station_zhandianbianhao')"
                        name="stationNo"
                    >
                        <a-input
                            v-model:value="formState.stationNo"
                            :placeholder="
                                $t('placeholder_qingshuruzhandianbianhao')
                            "
                            @input="(e) => inputChange(e, 'stationNo')"
                            :maxLength="20"
                            :disabled="isAddNew ? false : true"
                        />
                    </a-form-item>

                    <a-form-item :label="$t('Station Name')" name="stationName">
                        <a-input
                            v-model:value="formState.stationName"
                            :placeholder="
                                $t('placeholder_qingshuruzhandianmingcheng')
                            "
                            :maxLength="20"
                        />
                    </a-form-item>

                    <a-form-item
                        :label="$t('station_yongdianleixing')"
                        name="electricType"
                    >
                        <a-select
                            v-model:value="formState.electricType"
                            :placeholder="$t('placeholder_qingxuanze')"
                            :options="electricTypes"
                        />
                    </a-form-item>
                    <a-form-item
                        :label="$t('station_zhandiandiqu')"
                        name="areaId"
                    >
                        <a-select
                            v-model:value="formState.areaId"
                            :placeholder="
                                $t('placeholder_qingxuanzezhandiandiqu')
                            "
                            :options="queryAreaListData"
                        />
                    </a-form-item>

                    <a-form-item
                        :label="$t('station_zhandiandizhi')"
                        name="address"
                    >
                        <div
                            class="flex justify-between items-center leading-8 h-8 border border-d9 px-2.5 cursor-pointer address-input rounded"
                            :title="formState.address"
                            @click="mapShow"
                        >
                            <div
                                class="flex-1 overflow w-0"
                                :style="{
                                    color: formState.address
                                        ? 'var(--input-color)'
                                        : 'var(--placeholder)',
                                }"
                            >
                                {{
                                    formState.address ||
                                    $t('placeholder_qingxuanzezhandiandizhi')
                                }}
                            </div>
                            <div>
                                <iconSvg name="ip" :className="'iconsIp'" />
                            </div>
                        </div>
                        <!-- <a-input
                            v-model:value="formState.address"
                           :placeholder="$t('placeholder_qingxuanze')"
                            disabled
                        >
                            <template #suffix>
                                <iconSvg
                                    name="ip"
                                    :className="'iconsIp'"
                                    @click="mapShow"
                                />
                            </template>
                        </a-input> -->
                    </a-form-item>

                    <a-form-item :label="$t('station_zhandianzuobiao')">
                        <a-input
                            disabled
                            :value="
                                formState.longitude &&
                                $t('longitude') +
                                    formState.longitude +
                                    $t('latitude') +
                                    formState.latitude
                            "
                            :title="
                                formState.longitude &&
                                $t('longitude') +
                                    formState.longitude +
                                    $t('latitude') +
                                    formState.latitude
                            "
                        >
                        </a-input>
                    </a-form-item>
                    <a-form-item
                        :label="$t('station_bianyaqishuliang')"
                        name="transformerTotal"
                        v-if="!inputDisabled"
                    >
                        <a-select
                            v-model:value="formState.transformerTotal"
                            :placeholder="$t('placeholder_qingxuanze')"
                            :options="totals"
                            :disabled="!isAddNew"
                            @change="onTotalChange"
                        />
                    </a-form-item>
                    <template
                        v-for="(item, index) in formState.transformers"
                        :key="index"
                    >
                        <a-form-item
                            :label="
                                $t('haobianyaqirongliang').replace(
                                    'n%',
                                    index + 1
                                )
                            "
                        >
                            <a-input-number
                                v-model:value="item.transformerCapacity"
                                :min="0"
                                :max="10000"
                                :step="1"
                                :precision="0"
                                background="#fff"
                                class="w-40"
                                @change="onCapacityChange($event, item, index)"
                            ></a-input-number>
                        </a-form-item>
                    </template>
                    <a-form-item
                        :label="$t('station_touyunzhuangtai')"
                        name="operationStatus"
                        v-if="!inputDisabled"
                    >
                        <a-select
                            v-model:value="formState.operationStatus"
                            :placeholder="$t('placeholder_qingxuanze')"
                            :options="operationStatuss"
                        />
                    </a-form-item>
                    <a-form-item
                        :label="$t('station_touyunriqi')"
                        v-if="!inputDisabled"
                    >
                        <a-date-picker
                            class="w-full"
                            v-model:value="formState.startDate"
                            valueFormat="YYYY-MM-DD"
                            showArrow
                        />
                    </a-form-item>
                    <!-- <a-form-item label="变压器容量" name="transformerCapacity">
                        <a-input-number
                            v-model:value="formState.transformerCapacity"
                            :min="0"
                            :step="1"
                            :precision="2"
                            background="#fff"
                            class="w-40"
                        ></a-input-number>
                        <span class="text-secondar-text dark:text-60-dark ml-1">kW</span>
                    </a-form-item> -->

                    <a-form-item
                        :label="$t('station_weihurenyuan')"
                        name="maintenanceStaffId"
                        v-if="!inputDisabled"
                    >
                        <a-select
                            v-model:value="formState.maintenanceStaffId"
                            :placeholder="$t('placeholder_qingxuanze')"
                            :options="maintenanceStaffs"
                        />
                    </a-form-item>
                    <a-form-item
                        :label="$t('station_zhandiantupian')"
                        v-show="!isAddNew"
                    >
                        <div style="width: 160px; height: 92px">
                            <Upload
                                :data="{ scene: 'stationPic' }"
                                :className="'orgLogo'"
                                @success="success"
                                v-model:file-list="formState.stationPic"
                                @delImgFile="delImgFile"
                            />
                        </div>
                    </a-form-item>

                    <div class="h-1"></div>
                </div>
                <div>
                    <div class="flex justify-between items-center mb-3">
                        <div class="text-secondar-text dark:text-60-dark">
                            {{ $t('Device info') }}:
                        </div>
                        <el-button
                            plain
                            round
                            size="small"
                            type="primary"
                            @click="addNewDevice"
                            >{{ $t('add_device') }}</el-button
                        >
                    </div>

                    <div
                        class="bg-background dark:bg-ffffff-dark px-3 py-4 mb-3 rounded"
                        v-for="(item, index) in devices"
                        :key="index"
                    >
                        <div class="mb-2 text-title dark:text-title-dark">
                            {{ $t('device_shebeixuhao') }}：{{ index + 1 }}
                        </div>
                        <a-form-item :label="$t('device_chanpinxinghao')">
                            <a-select
                                class="w-40"
                                style="width: 160px"
                                v-model:value="item.productModel"
                                :placeholder="$t('placeholder_qingxuanze')"
                                :options="productModels"
                                :disabled="!!item.id"
                                @change="
                                    changeModels(
                                        index,
                                        item.productModel,
                                        $event
                                    )
                                "
                            />
                        </a-form-item>
                        <a-form-item
                            :label="$t('station_zhuangjirongliang')"
                            name="installedCapacity"
                        >
                            <a-input
                                class="w-40"
                                :value="item.installedCapacity"
                                disabled
                            >
                            </a-input
                            ><span
                                class="text-secondar-text dark:text-60-dark ml-1"
                                >kWh</span
                            >
                        </a-form-item>
                        <a-form-item :label="$t('Device No')" name="deviceSn">
                            <a-input
                                v-model:value="item.deviceSn"
                                :disabled="!!item.id"
                            >
                            </a-input>
                        </a-form-item>
                        <a-form-item
                            :label="$t('device_suoshubianyaqi')"
                            name="emsSystemId"
                        >
                            <a-select
                                v-model:value="item.emsSystemId"
                                :placeholder="$t('placeholder_qingxuanze')"
                                :options="realTransformers"
                                :disabled="!!item.id"
                                @change="changeTransformers($event, item)"
                            >
                            </a-select>
                        </a-form-item>
                        <a-form-item label="EMSUrl">
                            <a-input v-model:value="item.emsUrl"> </a-input>
                        </a-form-item>

                        <div
                            class="flex justify-center items-center gap-x-3 text-xs"
                        >
                            <el-button
                                round
                                size="small"
                                linear
                                @click="deleteItem(index)"
                                >{{ $t('Delete') }}</el-button
                            >
                            <el-button
                                plain
                                round
                                size="small"
                                type="primary"
                                @click="copyItem(index)"
                                >{{ $t('Copy') }}</el-button
                            >
                        </div>
                    </div>
                </div>
            </a-form>
        </div>
        <a-modal
            v-model:visible="mapVisible"
            :title="$t('ditu')"
            :footer="null"
            :destroyOnClose="true"
            :width="850"
            @close="onModalClose"
            wrapClassName="modal-box"
        >
            <gaodeMap
                @queryClick="queryClick"
                :visible="mapVisible"
                v-if="mapVisible"
            />
        </a-modal>
    </el-drawer>
</template>

<script setup>
import { reactive, ref, watch, computed, onMounted, nextTick } from 'vue'
import { useStore } from 'vuex'
import apiService from '@/apiService/device'
import {
    roundNumFun,
    operationStatuss,
    electricTypes,
} from '@/views/device/const.js'
import gaodeMap from '@/views/role/components/map.vue'
import Upload from '@/views/role/components/Upload.vue'
import { message } from 'ant-design-vue'
import { useRouter, useRoute } from 'vue-router'
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const labelCol = computed(() => {
    let res =
        locale.value == 'zh'
            ? {
                  span: 6,
              }
            : locale.value == 'en'
            ? {
                  span: 10,
              }
            : {
                  span: 10,
              }
    return res
})
const icon = require('@/assets/login/icon.png')
const route = useRoute(),
    router = useRouter()
const props = defineProps({
    visible: Boolean,
    info: {
        type: Object,
        default: () => {},
    },
    isAddNew: {
        type: Boolean,
        default: false,
    },
})
const emit = defineEmits(['onClose', 'update'])

const oldCreateTime = computed(() => props.info?.createTime || '')
const showConfirm = computed(() => {
    return (
        !inputDisabled.value &&
        !props.isAddNew &&
        oldCreateTime.value !== formState.startDate
    )
})
const getUserNames = async () => {
    let res = await apiService.getStaffByRole({ roleId: 3 })
    maintenanceStaffs.value = res.data.data.map((item) => ({
        label: item.name || '',
        value: item.id,
    }))
}
watch(
    () => props.visible,
    async (val) => {
        if (val) {
            await getUserNames()
            await getTreeData()
            await PotsQueryAreaList()
        }
        if (val && !props.isAddNew) {
            const getCompanyInfo = computed(
                () => store.getters['user/getUserInfoData']
            )
            formState.orgId = props.info.customerId
            formState.stationNo = props.info.stationNo
            formState.stationName = props.info.stationName
            formState.electricType = props.info.electricType

            formState.province = props.info.province
            formState.city = props.info.city
            formState.district = props.info.district
            formState.address = props.info.address
            formState.longitude = props.info.longitude
            formState.latitude = props.info.latitude
            formState.startDate = props.info.createTime
            formState.areaId = props.info.areaId
            formState.operationStatus = props.info.operationStatus || 0
            formState.maintenanceStaffId = props.info.maintenanceStaffId
            // formState.transformerCapacity = props.info.transformerCapacity
            formState.transformers = props.info.transformers
            realTransformers.value = props.info.transformers.map(
                (item, index) => {
                    return {
                        ...item,
                        label: t('haobianyaqi').replace('n%', index + 1),
                        value: String(item.emsSystemId),
                    }
                }
            )
            formState.transformerTotal = props.info.transformers.length
            devices.value = convertToFrontendFormat(props.info.transformers)
            // formState.orgId = inputDisabled.value ? props.info.orgId : 0
            nextTick(() => {
                formState.stationPic = props.info.stationPic
                    ? [
                          {
                              name: 'stationPic',
                              url: props.info.stationPic
                                  ? props.info.stationPic
                                  : void 0,
                          },
                      ]
                    : []
            })
        }
        if (val && props.isAddNew) {
            //
            addNewDevice()
        }
    },
    { immediate: true }
)

const onClose = () => {
    formRef.value?.clearValidate()
    formRef.value?.resetFields()
    formState.transformers = []
    realTransformers.value = []
    devices.value = []
    formState.transformerTotal = undefined
    emit('onClose')
}
const addLoading = ref(false)
const hasUniqueIds = (arr) => {
    const seenIds = new Set()
    for (const item of arr) {
        if (seenIds.has(item.deviceSn)) {
            return false // 发现重复的 deviceSn
        }
        seenIds.add(item.deviceSn)
    }
    return true // 所有 deviceSn 都是唯一的
}
const onSave = async () => {
    //
    addLoading.value = true
    let params = {
        ...formState,
        bindTransformerList: convertToBackendFormat(devices.value),
        bindDeviceList: undefined,
        transformers: undefined,
        transformerTotal: undefined,
        transformerCapacity: undefined,
        stationPic: formState.stationPic?.[0]?.url || '',
        startDate: !inputDisabled.value
            ? dayjs(formState.startDate).format('YYYY-MM-DD')
            : undefined,
        bindOrgId: formState.orgId,
    }
    let hasRepeatDeviceSn = hasUniqueIds(devices.value)
    // 先进行表单验证
    formRef.value
        .validate()
        .then(async () => {
            //
            if (devices.value.length <= 0) {
                addLoading.value = false
                message.error(t('tianjiashebei_tips01'))
            } else if (devices.value.some((item) => !item.deviceSn)) {
                addLoading.value = false
                message.error(t('tianjiashebei_tips02'))
            } else if (!hasRepeatDeviceSn) {
                addLoading.value = false
                message.error(t('tianjiashebei_tips03'))
            } else if (devices.value.some((item) => !item.emsSystemId)) {
                addLoading.value = false
                message.error(t('tianjiashebei_tips04'))
            } else {
                if (props.isAddNew) {
                    try {
                        let res = await apiService.bindStation(params)
                        if (res.data.code === 0) {
                            message.success(t('Successed'))
                            const currentRoute = router.currentRoute.value
                            if (currentRoute.path == '/device') {
                                setTimeout(() => {
                                    window.location.reload()
                                }, 300)
                            }
                            onClose()
                        }
                        addLoading.value = false
                    } catch (error) {
                        addLoading.value = false
                    }
                } else {
                    try {
                        let res = await apiService.updateStationInfo(params)
                        if (res.data.code === 0) {
                            message.success(t('Successed'))
                            onClose()
                            emit('update')
                        }
                        addLoading.value = false
                    } catch (error) {
                        addLoading.value = false
                    }
                }
            }
        })
        .catch((error) => {
            addLoading.value = false
        })

    addLoading.value = false
}

const store = useStore()
const isOperator = computed(() => {
    return store.state.user.userInfoData.roles.includes('operation_staff')
})
const inputDisabled = computed(() => {
    // 新增，可以输入,编辑不可以输入,但如果编辑的时候，是操作员，那么可以输入
    return props.isAddNew ? false : isOperator.value ? false : true
})
const rules = {
    orgId: [
        {
            required: true,
            message: t('placeholder_qingxuanzebangdingqiye'),
            trigger: 'blur',
        },
    ],
    stationNo: [
        {
            required: true,
            message: t('placeholder_qingshuruzhandianbianhao'),
            trigger: 'blur',
        },
    ],
    stationName: [
        {
            required: true,
            message: t('placeholder_qingshuruzhandianmingcheng'),
            trigger: 'blur',
        },
    ],
    areaId: [
        {
            required: true,
            message: t('placeholder_qingxuanzezhandiandiqu'),
            trigger: 'change',
            type: 'number',
        },
    ],
    address: [
        {
            required: true,
            message: t('placeholder_qingxuanzezhandiandizhi'),
            trigger: 'change',
        },
    ],
    electricType: [
        {
            required: true,
            message: t('placeholder_qingxuanze'),
            trigger: 'change',
            type: 'number',
        },
    ],
    transformerTotal: [
        {
            required: true,
            message: t('placeholder_qingxuanzebianyaqishuliang'),
            trigger: 'change',
            type: 'number',
        },
    ],
    transformerCapacity: [
        {
            required: true,
            type: 'number',
            message: t('placeholder_qingshuru'),
            trigger: ['blur', 'change'],
        },
    ],

    maintenanceStaffId: [
        {
            required: true,

            message: t('placeholder_qingxuanzeweihurenyuan'),
            trigger: ['change'],
        },
    ],
}

const mapVisible = ref(false)

const formRef = ref(null)
const productModels = ref([
    {
        name: 'SE215',
        value: 'SE215',
        data: 215,
    },
])
const formState = reactive({
    orgId: undefined,
    stationNo: void 0,
    stationName: void 0,
    province: void 0,
    city: void 0,
    district: void 0,
    address: void 0,
    latitude: void 0,
    longitude: void 0,
    areaId: void 0,
    operationStatus: 0,
    transformerTotal: undefined,
    electricType: 1,
    containerQuantity: 1,
    containerQuantityCunt: void 0,
    stationPic: undefined,
    transformerCapacity: undefined,
    maintenanceStaffId: undefined,
    transformers: [],
})

const inputChange = (e, key) => {
    formState[key] = formState[key].replace(
        /[^\w\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]/g,
        ''
    )
}

const queryAreaListData = ref([])

const PotsQueryAreaList = async (val) => {
    let {
        data: { data },
    } = await apiService.PotsQueryAreaList()
    queryAreaListData.value =
        data?.map((item) => {
            return { label: item.areaName, value: item.id }
        }) || []
}

const changeModels = (index, value, data) => {
    const item = productModels.value.findIndex((d) => d.value == value)
    devices.value[index].installedCapacity = productModels.value[item].data
}

const queryClick = (data) => {
    mapVisible.value = false
    formState.address = data.label
    formState.province = data.pname
    formState.city = data.cityname
    formState.district = data.adname
    formState.longitude = data.location.lng
    formState.latitude = data.location.lat
}
const onModalClose = () => {
    mapVisible.value = false
}

const mapShow = () => {
    mapVisible.value = true
}

const submit = () => {
    return formRef.value.validate()
}

const clearValidate = () => {
    formRef.value.clearValidate()
}

const resetFields = () => {
    formRef.value.resetFields()
}
const treeData = ref([])
const getTreeData = async () => {
    const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])
    const {
        data: { data, code },
    } = await apiService.getDeviceTree({
        supplierId: getCompanyInfo?.value?.orgId,
        businessType: 'energy_storage_cabinet',
    })
    if (code === 0) {
        const tree = [
            {
                name: getCompanyInfo?.value?.orgName,
                id: getCompanyInfo?.value?.orgId,
                children: data || [],
            },
        ]
        treeData.value = tree
    }
}
const success = (info) => {
    const {
        scene,
        file: {
            response: {
                data: { fileVisitUrl },
            },
        },
    } = info
    // currentOrgData.loginBaner = fileVisitUrl ? fileVisitUrl : void 0
    formState.stationPic = [
        { name: 'stationPic', url: fileVisitUrl ? fileVisitUrl : void 0 },
    ]
}
const delImgFile = () => {
    formState.stationPic = []
}

defineExpose({ submit, clearValidate, resetFields, formState })

const maintenanceStaffs = ref([])

const masterFlags = ref([
    {
        label: '主',
        value: 1,
    },
    {
        label: '从',
        value: 0,
    },
])
onMounted(async () => {
    // await getUserNames()
})
const totals = ref([
    {
        label: 1,
        value: 1,
    },
    {
        label: 2,
        value: 2,
    },
    {
        label: 3,
        value: 3,
    },
    {
        label: 4,
        value: 4,
    },
    {
        label: 5,
        value: 5,
    },
])
const realTransformers = ref([])

const onTotalChange = (e) => {
    const newTotal = formState.transformerTotal
    const currentLength = formState.transformers.length
    if (newTotal > currentLength) {
        for (let i = currentLength; i < newTotal; i++) {
            formState.transformers.push({
                transformerCapacity: 0,
                devices: [],
                emsSystemId: undefined,
            })
        }
    } else if (newTotal < currentLength) {
        formState.transformers.splice(newTotal)
    }
    realTransformers.value = formState.transformers.map((item, index) => {
        return {
            ...item,
            label: t('haobianyaqi').replace('n%', index + 1),
            value: index + 1,
            emsSystemId: String(index + 1),
        }
    })
}
const onCapacityChange = (e, item, index) => {
    realTransformers.value = formState.transformers.map((item, index) => {
        return {
            ...item,
            label: t('haobianyaqi').replace('n%', index + 1),
            value: index + 1,
            emsSystemId: String(index + 1),
        }
    })
    devices.value = convertToFrontendFormat(formState.transformers)
}
const devices = ref([])

const convertToFrontendFormat = (backendData) => {
    return backendData.flatMap((transformer, index) =>
        transformer.devices?.map((device) => ({
            ...device,
            emsSystemId: String(transformer.emsSystemId),
            transformerCapacity: transformer.transformerCapacity,
        }))
    )
}
const convertToBackendFormat = (frontendData) => {
    const groupedDevices = frontendData.reduce((acc, device) => {
        const id = String(device.emsSystemId)

        if (!acc[id]) {
            acc[id] = {
                transformerCapacity: device.transformerCapacity,
                devices: [],
            }
        }
        acc[id].devices.push({
            // ...device,
            productModel: device.productModel,
            deviceSn: device.deviceSn,
            emsUrl: device.emsUrl,
        })
        return acc
    }, {})
    return Object.entries(groupedDevices).map(([id, group]) => ({
        transformerCapacity: group.transformerCapacity,
        emsSystemId: String(id),
        bindDeviceList: group.devices,
    }))
}
const addNewDevice = () => {
    devices.value.push({
        productModel: 'SE215',
        installedCapacity: 215,
        deviceSn: undefined,
        emsSystemId: undefined,
        transformerCapacity: undefined,
    })
}
// 切换变压器
const changeTransformers = (e, item) => {
    const ite = realTransformers.value.find((i) => i.emsSystemId == e)
    console.log('[ ite,item ] >', ite, item)
    item.transformerCapacity = ite.transformerCapacity
}
const deleteItem = (index) => {
    //
    devices.value.splice(index, 1)
}
const copyItem = (index) => {
    //
    devices.value.push({
        ...devices.value[index],
        id: undefined,
    })
    // devices.value.splice(index + 1, 0, )
}
</script>

<style scoped lang="less">
.add-site {
    :deep(.iconsIp) {
        width: 16px;
        height: 16px;
        margin-right: 0;
        cursor: pointer;
    }

    :deep(.ant-form) {
        .ant-form-item-has-success {
            // margin-bottom: 24px !important;
        }

        .ant-form-item-with-help {
            margin-bottom: 0px !important;
        }

        .ant-form-item {
            font-size: 14px;
            margin-bottom: 12px;

            .ant-form-item-label {
                width: 72px;
                font-size: 14px;
                color: rgba(34, 34, 34, 0.65);

                > label {
                    height: 32px;
                    font-size: 14px;
                }
            }

            .ant-form-item-explain,
            .ant-form-item-explain-error {
                min-height: 24px;
                font-size: 14px;
            }

            .ant-form-item-control-input {
                min-height: 32px;

                .ant-form-item-control-input-content {
                    input {
                        padding: 4px 10px;
                        font-size: 14px;
                    }

                    .ant-input-affix-wrapper {
                        padding: 4px 10px;

                        input {
                            padding: 0;
                        }

                        .ant-input-suffix {
                            margin-left: 4px;
                        }

                        &::before {
                            height: 22px;
                        }

                        &:focus {
                            border-color: var(--themeColor);
                            box-shadow: none;
                        }

                        &:hover {
                            border-color: var(--themeColor);
                        }
                    }

                    .ant-input-affix-wrapper-focused {
                        border-color: var(--themeColor);
                        box-shadow: none;
                    }

                    .ant-input {
                        &:hover {
                            border-color: var(--themeColor);
                        }

                        &:focus {
                            border-color: var(--themeColor);
                            box-shadow: none;
                        }
                    }

                    .ant-select-multiple {
                        font-size: 14px;

                        .ant-select-selector {
                            padding: 1px 4px;
                            font-size: 14px;

                            .ant-select-selection-item {
                                height: 24px;
                                margin-top: 2px;
                                margin-bottom: 2px;
                                line-height: 22px;
                                margin-inline-end: 4px;
                                padding-inline-start: 8px;
                                padding-inline-end: 8px;
                            }

                            &::after {
                                line-height: 24px;
                            }
                        }

                        .ant-select-selection-search-input,
                        .ant-select-multiple
                            .ant-select-selection-search-mirror {
                            height: 24px;
                        }
                    }

                    .ant-select {
                        font-size: 14px;

                        &:not(.ant-select-disabled) {
                            &:hover {
                                .ant-select-selector {
                                    border-color: var(--themeColor);
                                }
                            }
                        }

                        .ant-select-selector {
                            height: 32px;
                            padding: 0 10px;

                            .ant-select-selection-search-input {
                                height: 30px;
                            }

                            .ant-select-selection-item {
                                line-height: 30px;
                                padding-right: 18px;
                            }
                        }

                        .ant-select-arrow {
                            right: 10px;
                            width: 12px;
                            height: 12px;
                            margin-top: -6px;
                            font-size: 12px;
                        }
                    }

                    .ant-select-single,
                    .ant-select-show-arrow {
                        .ant-select-selection-placeholder {
                            padding-right: 18px;
                            line-height: 30px;
                        }
                    }
                }
            }
        }

        .ant-select-focused {
            .ant-select-selector {
                border-color: var(--themeColor);
                box-shadow: none !important;
            }
        }
    }
}

:deep(.orgLogo) {
    .ant-upload {
        margin: 0;
        width: 94px;
        height: 92px;
        padding: 0px;
        border-style: solid;
        border-color: #d9d9d9;

        img {
            max-height: 90px;
        }
    }
}

:deep(.ant-btn-primary) {
    background: var(--themeColor);
    border-color: var(--themeColor);
    outline-color: var(--themeColor);
}

:deep(.ant-btn-primary) {
    color: #fff;
    background-color: var(--themeColor);
    border-color: var(--themeColor);

    &:hover {
        color: #fff;
        border-color: var(--themeColor);
    }
}

.address-input {
    &:hover {
        border-color: var(--themeColor);
    }
}

:deep(.ant-calendar-picker .ant-calendar-picker-clear) {
    display: block;
}

:deep(.ant-popover-inner-content) {
    width: 240px;
}
:deep(.iconsIp) {
    color: var(--input-color);
}
</style>

<style lang="less">
.modal-box {
    .ant-modal-header {
        padding: 16px 24px;
        border-radius: 8px 8px 0 0;

        .ant-modal-title {
            font-size: 16px;
        }
    }

    .ant-modal-close-x {
        width: 56px;
        height: 56px;
        font-size: 16px;
        line-height: 56px;
    }

    .ant-modal-body {
        // padding: 10px;
    }
    .ant-modal-content {
        background: var(--bg-f5);
        backdrop-filter: blur(10px);
    }
    .ant-modal-header {
        background: transparent;
    }
}
</style>
