<template>
    <div class="chartbox">
        <div :id="idx" style="width: 100%; height: 100%"></div>
    </div>
</template>
<script>
import { updateEcharts, chargeOption, someMax, roundNumFun } from '../const'
import {
    toRefs,
    computed,
    watch,
    onMounted,
    ref,
    nextTick,
    reactive,
} from 'vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import _cloneDeep from 'lodash/cloneDeep'
import { useI18n } from 'vue-i18n'
export default {
    name: 'barCharts',
    props: {
        chartData: {
            type: Object,
            default: () => ({}),
        },
        chargeSelect: String,
    },
    setup(props) {
        const { t, locale } = useI18n()
        const { chartData, chargeSelect } = toRefs(props)
        const state = reactive({})
        const idx = computed(() => {
            return 'id' + parseInt(Math.random() * 100000000)
        })

        const setOption = () => {
            nextTick(() => {
                const dateList = chartData.value.map((item) => {
                    if (chargeSelect.value == 'hour') {
                        return String(item.hour).padStart(2, '0') + ':00'
                    } else if (chargeSelect.value == 'day') {
                        return dayjs(item.date).format('YYYY/MM/DD')
                    } else if (chargeSelect.value == 'month') {
                        return dayjs(item.date).format('YYYY/MM')
                    }
                    // return dayjs(item.day).format('YYYY/MM/DD')
                })
                const dataList = Object.values(chartData.value)
                const charge = []
                const chargeData = []
                const discharge = []
                const dischargeData = []
                chartData.value.forEach((item) => {
                    chargeData.push(item?.charge || 0)
                    dischargeData.push(item?.discharge || 0)
                    charge.push({
                        value: item?.charge || 0,
                        // isBooan: false,
                    })
                    discharge.push({
                        value: item?.discharge || 0,
                        // isBooan: false,
                    })
                })
                // const isBooan = someMax([...chargeData, ...dischargeData], 1000)
                // if (isBooan) {
                //     charge.forEach((item) => {
                //         item.value = roundNumFun(item.value / 1000, 2)
                //         item.isBooan = true
                //     })
                //     discharge.forEach((item) => {
                //         item.value = roundNumFun(item.value / 1000, 2)
                //         item.isBooan = true
                //     })
                // }
                const options = _cloneDeep(chargeOption)
                console.log('[  t ] >', t('Charging amount'))
                options.legend.data = [
                    t('Charging amount'),
                    t('Discharging amount'),
                ]
                options.xAxis.data = dateList
                // options.grid.left = '4%'
                // options.yAxis.splitNumber = 10
                options.series[0].data = charge
                options.series[1].data = discharge
                if (charge.length >= 25) {
                    options.series[0].barWidth = '12px'
                    options.series[1].barWidth = '12px'
                } else if (charge.length >= 12) {
                    options.series[0].barWidth = '30px'
                    options.series[1].barWidth = '30px'
                } else {
                    options.series[0].barWidth = '35px'
                    options.series[1].barWidth = '35px'
                }
                // if (isBooan) {
                //     options.yAxis.name = 'MWh'
                // }
                updateEcharts(idx.value, options)
            })
        }
        watch(
            chartData,
            async (val) => {
                if (val) {
                    setOption()
                }
                // 处理数据

                // await initEcharts()
            }
            // { immediate: true }
        )

        return {
            ...toRefs(state),
            idx,
        }
    },
}
</script>
<style lang="less" scoped>
.chartbox {
    width: 100%;
    height: calc(~'100vh - 420px');
    min-height: 460px;
}
</style>
