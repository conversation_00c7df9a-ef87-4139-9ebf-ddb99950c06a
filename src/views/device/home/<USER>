<template>
    <div
        class="bg-ff dark:bg-transparent rounded device-box p-4 cursor-pointer"
    >
        <div class="flex justify-between gap-x-3 mb-5">
            <div class="w-36 h-27 overflow-hidden rounded">
                <img :src="imgPic" class="w-full h-full" alt="" srcset="" />
            </div>
            <div class="flex-1">
                <div class="flex justify-between items-center">
                    <div
                        class="text-xs leading-5 text-secondar-text dark:text-60-dark text-left"
                    >
                        {{ $t('No') }}：{{ data.stationNo || '-' }}
                    </div>
                    <div
                        class="flex items-center gap-x-1"
                        :style="{
                            color: getState(data.status).color,
                        }"
                    >
                        <i
                            class="w-6 h-6 text-2xl leading-6"
                            :class="['iconfont', getState(data.status).icon]"
                        ></i
                        ><span>{{
                            getState(data.status).label
                                ? $t(getState(data.status).label)
                                : ''
                        }}</span>
                    </div>
                </div>

                <div
                    class="overflow mb-3 text-base font-medium text-left text-title dark:text-title-dark"
                    :title="data.stationName"
                >
                    {{ data.stationName }}
                </div>
                <div
                    class="flex items-center mb-2.5 gap-x-2"
                    style="font-size: 13px"
                >
                    <div
                        class="flex items-center gap-x-1 px-2"
                        style="
                            color: #fd750b;
                            background: rgba(253, 117, 11, 0.1);
                        "
                    >
                        <!-- <iconSvg name="income" class="w-4 h-4" style="" /> -->
                        <div class="text-xs leading-5.5">
                            {{ $t('station_shouyi') }}:
                            <!-- {{
                                transformPrice(data.totalProfit)
                            }} -->
                            <!-- {{ $t('common_wanyuan') }} -->
                            <price :price="data.totalProfit" />
                        </div>
                    </div>
                    <div
                        class="flex items-center gap-x-1 px-2"
                        style="
                            color: #52c41a;
                            background: rgba(82, 196, 26, 0.1);
                        "
                    >
                        <div class="text-xs leading-5.5">
                            {{ $t('station_yunxingxiaolv') }}:
                            {{
                                data.efficiency >= 50
                                    ? data.efficiency + '%'
                                    : '-'
                            }}
                        </div>
                    </div>
                    <!-- <div
                        class="flex items-center ml-2 gap-x-1"
                        style="color: #fd0b0b"
                    >
                        <i
                            class="iconfont icon-a-ica-dianchi-guzhangbeifen16"
                        ></i>
                        <div class="text-sm leading-6">
                            <span class="align-middle">异常</span
                            ><span class="leading-6 align-middle font-medium">{{
                                data.alarmQuantity
                            }}</span>
                        </div>
                    </div> -->
                </div>
                <div
                    class="flex text-secondar-text dark:text-60-dark opacity-80"
                >
                    <iconSvg
                        name="ip"
                        class="w-4 h-4"
                        style="margin-top: 3px"
                    />
                    <div class="ml-1 w-0 flex-1 overflow" :title="data.address">
                        {{ data.address || '-' }}
                    </div>
                </div>
            </div>
        </div>
        <div class="flex justify-between px-2 mb-3">
            <div class="flex-1">
                <div class="text-secondar-text dark:text-60-dark">
                    {{ $t('station_zhuangjirongliang') }}
                </div>
                <div
                    class="text-base leading-5 mt-2 font-medium dark:text-title-dark"
                >
                    {{ data.installedCapacity }}
                    <span class="text-xs font-medium">kWh</span>
                </div>
            </div>
            <a-divider
                type="vertical"
                class="h-12"
                style="border-color: var(--border)"
            />
            <div class="flex-1 text-center">
                <div class="inline-block text-left">
                    <div class="text-secondar-text dark:text-60-dark">
                        {{ $t('station_zhuangjigonglv') }}
                    </div>
                    <div
                        class="text-base leading-5 mt-2 font-medium dark:text-title-dark"
                    >
                        {{ data.installedPower }}
                        <span class="text-xs font-medium">kW</span>
                    </div>
                </div>
            </div>
            <a-divider
                type="vertical"
                class="h-12"
                style="border-color: var(--border)"
            />
            <div class="flex-1 text-right">
                <div class="inline-block text-left">
                    <div class="text-secondar-text dark:text-60-dark">
                        {{ $t('station_touyunriqi') }}
                    </div>
                    <div
                        class="text-base leading-5 mt-2 font-medium dark:text-title-dark"
                    >
                        {{
                            data.createTime
                                ? dayjs(data.createTime).format('YYYY/MM/DD')
                                : '-'
                        }}
                    </div>
                </div>
            </div>
        </div>
        <!-- <div>{{ userType }}</div> -->
        <div
            v-if="getCompanyInfo?.orgType == 'supplier'"
            class="px-2 py-2 bg-f5f7f7 dark:bg-ffffff-dark rounded text-secondar-text dark:text-60-dark opacity-80 overflow leading-4"
        >
            <div
                class="overflow float-left text-left"
                :title="data.supplierName"
                :style="{
                    width: supplierWidth * 14 + 'px',
                }"
            >
                {{ $t('station_fuwushang') }}：{{ data.supplierName }}
            </div>
            <div
                class="overflow float-left"
                style="padding-left: 2%; border-left: 1px solid #d9d9d9"
                :style="{
                    maxWidth: `calc(100% - ${supplierWidth * 14 + 'px'})`,
                }"
                :title="data.customerName"
            >
                {{ $t('station_kehu') }}：{{ data.customerName }}
            </div>
        </div>
        <!-- <div>{{ supplierWidth }}</div> -->
    </div>
</template>

<script>
import { toRefs, watch, ref, computed } from 'vue'
import { getState } from '@/common/util.js'
import dayjs from 'dayjs'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'

import { unitConversion, alternateUnits, transformPrice } from '../const'
export default {
    props: {
        data: {
            type: Object,
            default: () => ({}),
        },
        userType: {
            type: String,
            default: '',
        },
    },
    setup(props) {
        const { data, userType } = toRefs(props)
        const imgPic = ref('')
        watch(
            props.data,
            () => {
                // console.log('[ props.data ] >', props.data)
                if (props.data.stationPic) {
                    imgPic.value = props.data.stationPic
                } else {
                    imgPic.value = require('@/assets/device/defaultImg.png')
                }
            },
            { immediate: true, deep: true }
        )
        const supplierWidth = ref(0)
        watch(
            data,
            (val) => {
                // console.log('[ data.supplierName ] >', data.value.supplierName)
                if (val.supplierName) {
                    supplierWidth.value =
                        val.supplierName.replace(/[\u4e00-\u9fa5]/g, 'a')
                            .length + 5
                }
            },
            { immediate: true, deep: true }
        )
        const store = useStore()
        const getCompanyInfo = computed(
            () => store.getters['user/getUserInfoData']
        )
        const { t, locale } = useI18n()
        return {
            getState,
            dayjs,
            unitConversion,
            alternateUnits,
            imgPic,
            transformPrice,
            supplierWidth,
            getCompanyInfo,
            t,
        }
    },
}
</script>

<style lang="scss" scoped></style>
