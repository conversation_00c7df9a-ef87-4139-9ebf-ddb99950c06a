<template>
    <div>
        <div>
            <a-empty v-if="data.length <= 0" class="mx-auto" />
        </div>
        <div class="record-1 heade-record" v-if="data.length">
            <div class="time">中标时段</div>
            <div class="power">中标容量(kWh)</div>
            <div class="price">中标价格(元/kWh)</div>
            <div class="validNgy"></div>
            <div class="profit">预计收益</div>
        </div>
        <!-- 待响应 -->
        <a-collapse :bordered="false" v-if="data.length">
            <template #expandIcon="{ isActive }">
                <span>{{ isActive ? '-' : '+' }}</span>
            </template>
            <a-collapse-panel v-for="(item, index) in data" :key="index">
                <template #header>
                    <!-- 每日响应总计 -->
                    <div class="record-1">
                        <div class="time">
                            <div>
                                {{ moment(item.runDt).format('MM/DD') }}
                                {{
                                    getTotalTimeSlot(item.bidPowerAndPriceList)
                                }}
                            </div>
                            <div>{{ '填谷' }}</div>
                        </div>
                        <div class="power">
                            {{ item.bidPower }}
                        </div>
                        <div class="price">
                            {{
                                (
                                    getTotalPrice(item.bidPowerAndPriceList) ||
                                    0
                                ).toFixed(2)
                            }}
                        </div>
                        <div class="validNgy"></div>
                        <div class="profit">
                            {{ item.predictAmount }}
                        </div>
                    </div>
                </template>
                <div v-for="(ite, ind) in item.bidPowerAndPriceList" :key="ind">
                    <div class="record-1 child-record">
                        <div class="time child-time">
                            <div>
                                {{
                                    ite.startTime.slice(0, 5) +
                                    '-' +
                                    ite.endTime.slice(0, 5)
                                }}
                            </div>
                            <div>{{ '填谷' }}</div>
                        </div>
                        <div class="power">
                            {{ ite.bidPower }}
                        </div>
                        <div class="price">
                            {{ (ite.bidPrice || 0).toFixed(2) }}
                        </div>
                        <div class="validNgy"></div>
                        <div class="profit">
                            {{ ite.bidAmount }}
                        </div>
                    </div>
                </div>
            </a-collapse-panel>
        </a-collapse>
    </div>
</template>

<script setup>
import moment from 'moment'
import { onMounted } from 'vue'

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    columns: {
        type: Array,
        default: () => [],
    },
})
const getTotalTimeSlot = (arr) => {
    return (
        arr[0].startTime.slice(0, 5) +
        '-' +
        arr[arr.length - 1].endTime.slice(0, 5)
    )
}
const getTotalPrice = (arr) => {
    return arr.reduce((a, b) => a + b.bidPrice, 0)
}
</script>

<style lang="less" scoped>
.record-1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
    color: theme('colors.secondar-text');
    &.child-record {
        padding-left: 24px;
    }
    &.heade-record {
        padding: 12px 16px;
        padding-left: 40px;
    }
    .time {
        width: 210px;
        text-align: left;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &.child-time {
            padding-left: 44px;
        }
    }
    .power {
        width: 100px;
        text-align: center;
    }
    .price {
        width: 120px;
        text-align: center;
    }
    .validNgy {
        width: 124px;
    }
    .profit {
        width: 64px;
        text-align: right;
    }
}
</style>
