<template>
    <div class="translate_detail flex flex-col" :style="{ ...objectStyle }">
        <div class="bg-title-t" v-if="headers">
            <slot name="detail"></slot>
        </div>
        <div class="flex-1">
            <slot name="content"></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'translateDetail',
    props: {
        objectStyle: {
            type: Object,
            default: () => {
                return {
                    height: '100%',
                }
            },
        },
        headers: {
            type: Boolean,
            default: true,
        },
    },
}
</script>

<style scoped lang="less">
.translate_detail {
    .bg-title-t {
        background-color: var(--bg-f5);
        color: var(--text-100);
        font-size: 0.75rem;
        // padding: 10px;
        height: 40px;
    }
}
</style>
