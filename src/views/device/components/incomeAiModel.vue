<template>
    <div
        class="p-4 bg-ff dark:bg-ff-dark rounded-lg mt-3"
        style="height: 520px"
        v-if="true"
    >
        <div class="flex justify-between gap-x-4">
            <div class="flex-1">
                <div class="flex justify-between">
                    <div class="flex items-center gap-x-2">
                        <div>全周期收益预估</div>
                        <img
                            src="@/assets/device/ai.png"
                            class="w-5 h-5"
                            alt=""
                            srcset=""
                        />
                    </div>
                    <div>
                        <el-button plain round class="btn-hover">
                            <span>配置</span>
                            <span class="icon-box ml-0.5">
                                <iconSvg name="shezhi" class="icon-default" />
                            </span>
                        </el-button>
                    </div>
                </div>
                <div class="flex justify-between mb-6">
                    <div class="flex-1">
                        <div class="inline-block text-left">
                            <div class="text-secondar-text dark:text-60-dark">
                                当前总收益(
                                {{
                                    alternateUnits(80000, 10000)
                                        ? '万元'
                                        : '元'
                                }})
                            </div>
                            <div class="text-base font-medium">
                                {{ unitConversion(80000, 10000) }}
                            </div>
                        </div>
                    </div>
                    <a-divider type="vertical" class="h-12" />
                    <div class="flex-1 text-center">
                        <div class="inline-block text-left">
                            <div class="text-secondar-text dark:text-60-dark">
                                当前资产净值(
                                {{
                                    alternateUnits(300000000, 10000)
                                        ? '万元'
                                        : '元'
                                }})
                            </div>
                            <div class="text-base font-medium">
                                {{ unitConversion(300000000, 10000) }}
                            </div>
                        </div>
                    </div>
                    <a-divider type="vertical" class="h-12" />
                    <div class="flex-1">
                        <div class="inline-block text-left">
                            <div class="text-secondar-text dark:text-60-dark">
                                标准策略预估收益({{
                                    alternateUnits(500000, 10000)
                                        ? '万元'
                                        : '元'
                                }})
                            </div>
                            <div class="text-base font-medium">
                                {{ unitConversion(500000, 10000) }}
                            </div>
                        </div>
                    </div>
                    <a-divider type="vertical" class="h-12" />
                    <div class="flex-1">
                        <div class="inline-block text-left">
                            <div class="text-secondar-text dark:text-60-dark">
                                AI策略预估收益({{
                                    alternateUnits(550000, 10000)
                                        ? '万元'
                                        : '元'
                                }})
                            </div>
                            <div class="text-base font-medium">
                                {{ unitConversion(550000, 10000) }}
                            </div>
                        </div>
                    </div>
                    <a-divider type="vertical" class="h-12" />
                    <div class="flex-1">
                        <div class="inline-block text-left">
                            <div class="text-secondar-text dark:text-60-dark">
                                标准策略全周期IRR
                            </div>
                            <div class="text-base font-medium">
                                {{ '23.44%' }}
                            </div>
                        </div>
                    </div>
                    <a-divider type="vertical" class="h-12" />
                    <div class="flex-1">
                        <div class="text-left">
                            <div class="text-secondar-text dark:text-60-dark">
                                AI策略全周期IRR
                            </div>
                            <div class="text-base font-medium">
                                <div class="flex justify-between items-center">
                                    <div class="flex-1">29.44%</div>
                                    <div>
                                        <percentage :num="6.6" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="" style="height: 388px">
                    <div id="aiProfit" style="height: 400px; width: 100%"></div>
                </div>
            </div>
            <div
                style="width: 368px; line-height: 38px"
                class="border border-border rounded p-1 gap-1"
            >
                <div class="flex justify-between">
                    <div class="year">
                        <div>年份</div>
                        <div v-for="item in incomeList" :key="item.year">
                            {{ item.year }}
                        </div>
                    </div>
                    <div class="basicIncome">
                        <div>标准策略收益(万元)</div>
                        <div v-for="item in incomeList" :key="item.year">
                            {{ item.basicIncome }}
                        </div>
                    </div>
                    <div class="aiIncome">
                        <div>AI策略收益(万元)</div>
                        <div v-for="item in incomeList" :key="item.year">
                            {{ item.aiIncome }}
                        </div>
                    </div>
                </div>
                <div class="flex justify-between total">
                    <div class="year">总计</div>
                    <div class="basicIncome">23322.3</div>
                    <div class="aiIncome">44224.44</div>
                </div>
            </div>
        </div>
    </div>
    <div class="flex mt-3 gap-x-3">
        <div class="flex-1" v-if="data.aiTrainStatus == 1">
            <div
                class="bg-ff dark:bg-ff-dark p-3 rounded-lg"
                style="height: 490px; overflow-y: auto"
            >
                <div class="bg-f5 rounded-lg p-3">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center gap-x-2">
                            <span>需求响应记录-待响应</span>
                            <img
                                src="@/assets/device/ai.png"
                                class="w-5 h-5"
                                alt=""
                                srcset=""
                            />
                        </div>
                    </div>
                    <record :data="data.pendingData" :columns="columns" />
                </div>
                <div class="bg-f5 rounded-lg mt-3 p-3">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center gap-x-2">
                            <span>需求响应记录-已响应</span>
                            <img
                                src="@/assets/device/ai.png"
                                class="w-5 h-5"
                                alt=""
                                srcset=""
                            />
                        </div>
                        <div>
                            <el-button
                                plain
                                round
                                v-if="data.responseData.length > 0"
                                @click="showRecord"
                                >查看记录</el-button
                            >
                        </div>
                    </div>
                    <!-- 已响应 -->
                    <div>
                        <record :data="shortResponseData" :columns="columns" />
                    </div>
                </div>
            </div>
        </div>
        <div class="flex-1 h-full">
            <div class="bg-ff dark:bg-ff-dark rounded-lg p-3 h-full">
                <div class="flex justify-between items-center">
                    <div class="flex items-center gap-x-2">
                        <span>近期净利润趋势</span>
                        <img
                            src="@/assets/device/ai.png"
                            class="w-5 h-5"
                            alt=""
                            srcset=""
                        />
                    </div>
                    <div class="space-x-2">
                        <el-button
                            plain
                            round
                            @click="showSetting"
                            class="btn-hover"
                        >
                            <span>配置</span>
                            <span class="icon-box ml-0.5">
                                <iconSvg name="shezhi" class="icon-default" />
                            </span>
                        </el-button>
                        <!-- <el-button plain round @click="lookDetail('netProfit')" class="btn-hover">
                            <span>查看明细</span>
                            <span class="icon-box ml-0.5">
                                <iconSvg name="search" class="icon-default" />
                            </span>
                        </el-button> -->
                        <el-button
                            plain
                            round
                            @click="showIncomeCount"
                            class="btn-hover"
                        >
                            <span>统计</span>
                            <span class="icon-box ml-0.5">
                                <iconSvg
                                    name="statistics"
                                    class="icon-default"
                                />
                            </span>
                        </el-button>
                    </div>
                </div>
                <div class="">
                    <Net-Profit-chart :chartData="data.profitChartData" />
                </div>
            </div>
        </div>
    </div>
    <a-modal
        :visible="recordModalVisible"
        :footer="null"
        @cancel="onModalClose"
        :width="1200"
    >
        <div class="record">
            <div class="flex justify-between items-center mb-4">
                <div class="flex items-center gap-x-2">
                    <div>需求响应记录-已响应</div>
                    <img
                        src="@/assets/device/ai.png"
                        class="w-5 h-5"
                        alt=""
                        srcset=""
                    />
                </div>
                <div>
                    <!-- <a-date-picker v-model:value="value1" /> -->
                </div>
            </div>
        </div>
        <div class="table-box p-3 bg-f5 rounded-lg">
            <record :data="data.responseData" :columns="columns" />
        </div>
        <div class="flex justify-center mt-4">
            <el-button plain round @click="onModalClose">{{
                $t('common_guanbi')
            }}</el-button>
        </div>
    </a-modal>

    <a-modal
        :visible="incomeCountVisible"
        :footer="null"
        @cancel="onIncomeCountModalClose"
        :width="1200"
        :maskClosable="false"
    >
        <div class="record">
            <div class="flex justify-between items-center mb-3">
                <div class="flex items-center gap-x-2">
                    <div>收益统计</div>
                    <img
                        src="@/assets/device/ai.png"
                        class="w-5 h-5"
                        alt=""
                        srcset=""
                    />
                </div>
                <div></div>
            </div>
            <div class="flex justify-between mb-3 items-center">
                <div class="flex items-center p-2 bg-background rounded">
                    <div
                        class="px-4 py-2 rounded cursor-pointer"
                        v-for="item in DateFilter"
                        :key="item.value"
                        :class="
                            selectedDateType == item.value
                                ? 'bg-ff dark:bg-ff-dark'
                                : ''
                        "
                        @click="selectDateType(item)"
                    >
                        {{ item.label }}
                    </div>
                </div>
                <div>
                    <!-- 不要在意这些细节。我也不想这样的。要怪就怪这个垃圾antdv居然不支持月份区间选择 -->
                    <el-config-provider
                        :locale="zhCn"
                        v-if="selectedDateType == 'day'"
                    >
                        <el-date-picker
                            v-model="incomeRangeDate"
                            type="daterange"
                            range-separator="-"
                            :start-placeholder="$t('common_kaishiriqi')"
                            :end-placeholder="$t('common_jieshuriqi')"
                            value-format="YYYY-MM-DD"
                            @calendar-change="onCalendarChange"
                            :disabled-date="disabledDates"
                            @change="incomeDateChange"
                            style="width: 240px"
                        />
                    </el-config-provider>
                    <el-config-provider
                        :locale="zhCn"
                        v-if="selectedDateType == 'month'"
                    >
                        <el-date-picker
                            v-model="incomeRangeMonth"
                            type="monthrange"
                            range-separator="-"
                            :start-placeholder="$t('kaishiyuefen')"
                            :end-placeholder="$t('jieshuyuefen')"
                            value-format="YYYY-MM"
                            @calendar-change="onCalendarChangeM"
                            @change="incomeDateChangeM"
                            :disabled-date="disabledMonths"
                            style="width: 240px"
                        />
                    </el-config-provider>
                    <export-button
                        content="
                           是否导出「收益统计」数据?
                        "
                        @confirm="confirmExportProfit"
                    />
                </div>
            </div>
            <div class="income-table-box">
                <a-table
                    :dataSource="incomeTableData"
                    :columns="incomeColumns"
                    :customRow="Rowclick"
                    :rowKey="(record) => record.id"
                    @change="onTableChange"
                    :showRefresh="false"
                    class="text-sm"
                >
                    <template #date="{ text }">
                        <div class="">
                            {{ text }}
                        </div>
                    </template>
                </a-table>
            </div>
            <div class="flex justify-center mt-1">
                <el-button plain round @click="onIncomeCountModalClose">{{
                    $t('common_guanbi')
                }}</el-button>
            </div>
        </div>
    </a-modal>
    <el-drawer
        v-model="settingDrawerVisible"
        :size="486"
        :show-close="false"
        @close="onSettingDrawerClose"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header text-primary-text dark:text-80-dark">
                    <span>{{ '电站收益参数配置' }}</span>
                </div>
                <div class="flex gap-x-3 items-center">
                    <el-button plain round @click="onSettingDrawerClose"
                        >取消</el-button
                    >
                    <el-button plain type="primary" round @click="onsave"
                        >保存配置</el-button
                    >
                </div>
            </div>
        </template>
        <a-form :model="formState" ref="formRef" labelAlign="left">
            <a-form-item label="设备售价（万元）" name="deviceSalePrice">
                <div class="text-right">
                    ¥
                    <a-input-number
                        v-model:value="formState.deviceSalePrice"
                        :min="0"
                        :max="200"
                        :step="1"
                        :precision="2"
                        class="text-left ml-1"
                        style="width: 88px"
                    ></a-input-number>
                </div>
            </a-form-item>
            <a-form-item label="运维成本（万元/年）" name="operationFee">
                <div class="text-right">
                    ¥
                    <a-input-number
                        v-model:value="formState.operationFee"
                        :min="0"
                        :max="10"
                        :step="1"
                        :precision="2"
                        class="text-left ml-1"
                        style="width: 88px"
                    ></a-input-number>
                </div>
            </a-form-item>
        </a-form>
    </el-drawer>
</template>

<script setup>
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { onMounted, ref, toRefs, watch, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { unitConversion, alternateUnits, aiProfitOption } from '../const'
import * as echarts from 'echarts'
import Percentage from './percentage.vue'
import dictionary from '@/components/table/dictionary'
import NetProfitChart from './netProfitChart.vue'
import { usePagenation } from '@/common/setup'
import moment from 'moment'
import apiVpp from '@/apiService/vpp'
import record from './record.vue'
import * as XLSX from 'xlsx' //引入 xlsx 库，将数据转换为 Excel 并下载
const route = useRoute()
const router = useRouter()
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
})
const { data } = toRefs(props)

const { paginationProps, changePage, onTableChange, refresh, pageParam } =
    usePagenation(getTableData)

const incomeList = ref([])

const selectedDateType = ref('day')
const DateFilter = ref([
    {
        label: '日',
        value: 'day',
    },
    {
        label: '月',
        value: 'month',
    },
])

// 收益预估
const setChartData = () => {
    // const myChart = echarts.init(document.getElementById('aiProfit'))
    // myChart.setOption(aiProfitOption)
}

const columns = ref([
    {
        title: '中标时段',
        dataIndex: 'runDt',
        key: 'runDt',
        align: 'left',
        slots: {
            customRender: 'runDt',
        },
    },
    {
        title: '中标容量(kWH)',
        dataIndex: 'bidPower',
        key: 'bidPower',
        align: 'center',
    },
    {
        title: '中标价格(元/kWH)',
        dataIndex: 'bidPrice',
        key: 'bidPrice',
        align: 'center',
    },
    {
        title: 'AI策略预估收益',
        dataIndex: 'aiProfit',
        key: 'aiProfit',
        align: 'center',
    },
])

const modalTableColums = ref([
    {
        title: '中标时段',
        dataIndex: 'year',
        key: 'year',
        align: 'left',
        width: 300,
    },
    {
        title: '中标容量(kWH)',
        dataIndex: 'bidPower',
        key: 'bidPower',
        align: 'center',
    },
    {
        title: '中标价格(元/kWH)',
        dataIndex: 'bidPrice',
        key: 'bidPrice',
        align: 'center',
    },
    {
        title: 'AI策略预估收益',
        dataIndex: 'aiProfit',
        key: 'aiProfit',
        align: 'center',
    },
])

const incomeTableData = ref([])

const incomeColumns = ref([
    {
        title: '日期',
        dataIndex: 'date',
        key: 'date',
        align: 'center',
        fixed: 'left',
        width: '108px',
        slots: {
            customRender: 'date',
        },
    },
    {
        title: '净利润',
        align: 'center',
        children: [
            {
                title: '净利润金额(元)',
                dataIndex: 'netProfit',
                key: 'netProfit',
                align: 'center',
                width: 124,
            },
        ],
    },
    {
        title: '收入',
        align: 'center',
        children: [
            {
                title: '充电量(kWh)',
                dataIndex: 'charge',
                key: 'charge',
                align: 'center',
                width: 112,
            },
            {
                title: '充电成本(元)',
                dataIndex: 'chargeFee',
                key: 'chargeFee',
                align: 'center',
                width: 112,
            },
            {
                title: '放电量(kWh)',
                dataIndex: 'discharge',
                key: 'discharge',
                align: 'center',
                width: 112,
            },

            {
                title: '放电收益(元)',
                dataIndex: 'dischargeFee',
                key: 'dischargeFee',
                align: 'center',
                width: 112,
            },
            {
                title: '充放电收益(元)',
                dataIndex: 'arbitrageProfit',
                key: 'arbitrageProfit',
                align: 'center',
                width: 120,
            },
            {
                title: '需求响应收益(元）',
                dataIndex: 'demandProfit',
                key: 'demandProfit',
                align: 'center',
                width: 136,
            },
            {
                title: '收入总计(元）',
                dataIndex: 'profit',
                key: 'profit',
                align: 'center',
                width: 112,
            },
        ],
    },
    {
        title: '成本',
        children: [
            {
                title: '设备折旧成本(元)',
                dataIndex: 'depreciationCost',
                key: 'depreciationCost',
                align: 'center',
                width: 136,
            },
            {
                title: '运营成本(元)',
                dataIndex: 'operatingCost',
                key: 'operatingCost',
                align: 'center',
                width: 146,
            },
            {
                title: '成本总计(元)',
                dataIndex: 'totalCost',
                key: 'totalCost',
                align: 'center',
                width: 112,
            },
        ],
    },
])

const Rowclick = (record) => {
    //
}
const getTableData = () => {
    //
}
const recordModalVisible = ref(false)
const showRecord = () => {
    //
    //  请求所有响应记录

    recordModalVisible.value = true
}
const onModalClose = () => {
    recordModalVisible.value = false
}

const settingDrawerVisible = ref(false)
const formRef = ref()
const formState = reactive({
    deviceSalePrice: undefined,
    operationFee: undefined,
})
const showSetting = async () => {
    // 获取数据
    const res = await apiVpp.getConfigEarningParams({
        stationId: props.data.stationId,
    })
    formState.deviceSalePrice = (res.data.data.deviceSalePrice / 10000).toFixed(
        2
    )
    formState.operationFee = (res.data.data.operationFee / 10000).toFixed(2)
    settingDrawerVisible.value = true
}
const onSettingDrawerClose = () => {
    settingDrawerVisible.value = false
    formRef.value.resetFields()
}
const onsave = async () => {
    //
    await apiVpp.configEarningParams({
        stationId: props.data.stationId,
        deviceSalePrice: (formState.deviceSalePrice * 10000).toFixed(0),
        operationFee: (formState.operationFee * 10000).toFixed(0),
    })
    onSettingDrawerClose()
}
const incomeCountVisible = ref(false)

const showIncomeCount = async () => {
    //
    await getIncomeTableData()
    incomeCountVisible.value = true
}
const onIncomeCountModalClose = () => {
    incomeCountVisible.value = false
}
const getIncomeTableData = async () => {
    let params = {
        stationNo: props.data.stationNo,
        startDate:
            selectedDateType.value === 'day'
                ? incomeRangeDate.value[0]
                : undefined,
        endDate:
            selectedDateType.value === 'day'
                ? incomeRangeDate.value[1]
                : undefined,
        startMonth:
            selectedDateType.value === 'month'
                ? incomeRangeMonth.value[0]
                : undefined,
        endMonth:
            selectedDateType.value === 'month'
                ? incomeRangeMonth.value[1]
                : undefined,
        periodType: selectedDateType.value,
    }
    const res = await apiVpp.statisticsStationDateChargeAndNetProfit(params)
    const result = res.data.data
    incomeTableData.value = result
}
// 日期选择  ⬇️
const incomeRangeDate = ref([
    moment().subtract(7, 'days').format('YYYY-MM-DD'),
    moment().format('YYYY-MM-DD'),
])
const incomeDateChange = (value) => {
    //
    getIncomeTableData()
}
const dates = ref([])

const onCalendarChange = (val) => {
    dates.value = val
}

const disabledDates = (current) => {
    if (!dates.value || dates.value.length === 0) {
        return false
    }
    const diffDate = moment(current).diff(dates.value[0], 'days')
    // Can not select days before today and today
    return Math.abs(diffDate) > 31
    // current && current > moment().endOf('day') &&
}

// 日期选择⬆️
// 月选择  ⬇️
const incomeRangeMonth = ref([moment().subtract(11, 'months'), moment()])
const months = ref()
const onCalendarChangeM = (val) => {
    months.value = val
}
const disabledMonths = (current) => {
    if (!months.value || months.value.length === 0) {
        return false
    }
    const diffDate = moment(current).diff(months.value[0], 'month')
    // Can not select days before today and today
    return Math.abs(diffDate) > 11
    // current && current > moment().endOf('day') &&
}
const incomeDateChangeM = () => {
    getIncomeTableData()
}
// 月选择  ⬆️
const selectDateType = (item) => {
    //
    selectedDateType.value = item.value
    // 初始化日期或月份
    if (item.value === 'day') {
        incomeRangeDate.value = [
            moment().subtract(7, 'days').format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD'),
        ]
    } else if (item.value === 'month') {
        incomeRangeMonth.value = [
            moment().subtract(11, 'months').format('YYYY-MM'),
            moment().format('YYYY-MM'),
        ]
    }
    getIncomeTableData()
}

const lookDetail = (type) => {
    router.push({
        path: '/rolePage',
        query: {
            stationNo: route.query.stationNo,
            stationId: props.data.stationInfo.id,
            stationOrgId: props.data.stationInfo.customerId,
            stationName: props.data.stationInfo.stationName,
            type: type,
            activeKey: '3',
        },
    })
}
const confirmExportProfit = () => {
    //
    let data = incomeTableData.value.map((obj) => ({
        日期: obj?.date || '',
        '净利润金额(元)': obj?.netProfit || 0,
        '充电量(kWh)': obj?.charge || 0,
        '充电成本(元)': obj?.chargeFee || 0,
        '放电量(kWh)': obj?.discharge || 0,
        '放电收益(元)': obj?.dischargeFee || 0,
        '充放电收益(元)': obj?.arbitrageProfit || 0,
        '需求响应收益(元）': obj?.demandProfit || 0,
        '收入总计(元）': obj?.profit || 0,
        '设备折旧成本(元)': obj?.depreciationCost || 0,
        '运营成本(元)': obj?.operatingCost || 0,
        '成本总计(元)': obj?.totalCost || 0,
    }))
    const worksheet = XLSX.utils.json_to_sheet(data)
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
    XLSX.writeFile(workbook, '收益统计.xlsx')
}
// 首页展示的已响应数据
const shortResponseData = ref([])
watch(
    () => data.pendingData,
    (val) => {
        if (val) {
            shortResponseData.value = data.responseData.slice(
                0,
                6 - (data.pendingData.length || 0)
            )
        }
    }
)
const getTotalTimeSlot = (arr) => {
    return (
        arr[0].startTime.slice(0, 5) +
        '-' +
        arr[arr.length - 1].endTime.slice(0, 5)
    )
}
onMounted(() => {
    setChartData()
})
</script>

<style lang="less" scoped>
.year {
    width: 56px;
    text-align: left;
    padding-left: 2px;
}

.basicIncome {
    width: 146px;
    text-align: right;
    background: #f3f6f6;
    padding: 0 8px;
}

.aiIncome {
    width: 146px;
    text-align: right;
    background: #f3effe;
    padding: 4px 8px;
    color: #7d4af9;
}

.total {
    .basicIncome {
        background: none;
    }

    .aiIncome {
        background: none;
    }
}

.table-box {
    height: 550px;
    overflow-y: auto;
}

.income-table-box {
    :deep(.ant-table-body) {
        height: 552px;
    }

    :deep(.ant-table-tbody > tr > td) {
        padding-left: 4px;
        padding-right: 4px;
        color: rgba(34, 34, 34, 0.6);
    }

    :deep(.ant-table-thead > tr > th) {
        color: rgba(34, 34, 34, 0.6);
        padding: 12px 8px;
    }

    :deep(.ant-table-thead > tr > th:first-child) {
        background: #fff;
    }

    :deep(.ant-table-thead > tr > th) {
        padding: 12px 8px;
    }

    :deep(.ant-table-tbody > tr > td) {
        padding: 12px 8px;
    }

    :deep(.ant-table-thead tr) {
        &:first-child {
            background: #f6f6f6;

            th {
                padding: 0;
                border-right: 1px solid rgba(34, 34, 34, 0.08);
                border-bottom: 1px solid rgba(34, 34, 34, 0.08);
                border-top: 1px solid rgba(34, 34, 34, 0.08);

                &:first-child {
                    border-left: 1px solid rgba(34, 34, 34, 0.08);
                }
            }
        }

        &:nth-child(2) {
            th {
                &:first-child {
                    border-right: 1px solid rgba(34, 34, 34, 0.08);
                }

                &:nth-child(7) {
                    border-right: 1px solid rgba(34, 34, 34, 0.08);
                }

                &:last-child {
                    border-right: 1px solid rgba(34, 34, 34, 0.08);
                }
            }
        }
    }

    :deep(.ant-table-tbody tr) {
        td:first-child {
            border-right: 1px solid rgba(34, 34, 34, 0.08);
            border-left: 1px solid rgba(34, 34, 34, 0.08);
        }

        td:nth-child(2) {
            border-right: 1px solid rgba(34, 34, 34, 0.08);
        }

        td:nth-child(8) {
            border-right: 1px solid rgba(34, 34, 34, 0.08);
        }

        td:last-child {
            border-right: 1px solid rgba(34, 34, 34, 0.08);
        }
    }
}

.month-picker {
}

:deep(.ant-calendar-footer-btn) {
    width: 100%;
}

:deep(.ant-collapse-borderless) {
    background: #ffffff;
}
</style>

<style lang="less">
.ant-calendar-footer-btn {
    width: 100%;
}

.ant-calendar-footer-extra {
    width: 100%;
    padding: 4px 0;
}

.el-month-table td.end-date .cell,
.el-month-table td.start-date .cell {
    background: var(--themeColor);

    &:hover {
        color: #fff;
    }
}

.el-month-table td.today .cell {
    color: var(--themeColor);
}

.el-month-table td .cell:hover {
    color: var(--themeColor);
}

.el-range-editor.is-active {
    box-shadow: 0 0 0 1px var(--themeColor);
}

.el-range-editor.is-active:hover {
    box-shadow: 0 0 0 1px var(--themeColor);
}

.el-date-table td.end-date .el-date-table-cell__text,
.el-date-table td.start-date .el-date-table-cell__text {
    background-color: var(--themeColor);
}

.el-date-table td.today .el-date-table-cell__text {
    color: var(--themeColor) !important;
}
.el-date-table td.today.end-date .el-date-table-cell__text,
.el-date-table td.today.start-date .el-date-table-cell__text {
    color: #fff !important;
}
.el-date-table td.current:not(.disabled) .el-date-table-cell__text {
    color: #fff !important;
}

.el-date-table td.available:hover {
    color: var(--themeColor);
}

.ant-table-pagination.ant-pagination {
    margin: 0;
    margin-bottom: 12px;
}

.ant-pagination-item-active {
    border-color: var(--themeColor);

    a {
        color: var(--themeColor);
    }
}

.ant-pagination-item-active:focus a {
    color: var(--themeColor);
}

.ant-pagination-item-active:hover a {
    color: var(--themeColor);
}

.ant-pagination-item:focus a {
    color: var(--themeColor);
}

.ant-pagination-item:hover a {
    color: var(--themeColor);
}
</style>
