<template>
    <div class="charge-title flex justify-between">
        <div
            class="charge-title-l h-12 pl-3 py-2 leading-12 sysbtjbt rounded-l"
            :class="locale"
        >
            <span class="name">
                {{ chargeTitle }}
            </span>
            <span class="data">
                {{ unitConversion(chargeData, 1000, locale == 'en' ? 1 : 2) }}
            </span>
            <span class="unit">
                {{ chargeUnit }}
            </span>
        </div>
        <div
            class="charge-title-r h-12 pr-3 py-2 leading-12 sysbtjbt rounded-l justify-end"
            :class="locale"
        >
            <span class="name">
                {{ dischargeTitle }}
            </span>
            <span class="data">
                {{
                    unitConversion(dischargeData, 1000, locale == 'en' ? 1 : 2)
                }}
            </span>
            <span class="unit">
                {{ dischargeUnit }}
            </span>
        </div>
    </div>
</template>

<!-- 

，我只是一个他妈的彩笔，工资给的他妈的低的不得了，还他妈的想让我他妈的给他写贼他妈牛逼的傻逼代码。
刚开始写。这个组件是特么为了统一他妈的傻逼样式写的。
用来干啥呢，用来把那个傻逼的左右不对称的傻逼的充放电数据展示出来用的，
后续谁她妈知道改成什么傻逼样子。服了，操



-->
<script setup>
import { computed, ref } from 'vue'
import { unitConversion } from '../const'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const props = defineProps({
    cTitle: {
        type: String,
        default: '',
    },
    chargeData: {
        type: Number || String,
        default: 0,
    },
    chargeUnit: {
        type: String,
        default: '',
    },
    disTitle: {
        type: String,
        default: '',
    },
    dischargeData: {
        type: Number || String,
        default: 0,
    },
    dischargeUnit: {
        type: String,
        default: '',
    },
})
const chargeTitle = computed(() => {
    return props.cTitle || t('station_zuorichongdianliang')
})
const dischargeTitle = computed(() => {
    return props.disTitle || t('station_zuorifangdianliang')
})
</script>

<style lang="less" scoped>
.charge-title-l,
.charge-title-r {
    width: calc(~'50% - 24px');
    position: relative;
}
.charge-title-l {
    background: rgba(51, 190, 79, 0.1);

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        left: 100%;
        position: absolute;
        top: 0;
        border-top: 48px solid rgba(51, 190, 79, 0.1);
        border-right: 43px solid transparent;
    }
}

.charge-title-r {
    background: rgba(241, 246, 255, 1);

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        right: 100%;
        position: absolute;
        top: 0;
        border-bottom: 48px solid rgba(241, 246, 255, 1);
        border-left: 43px solid transparent;
    }
}
</style>
