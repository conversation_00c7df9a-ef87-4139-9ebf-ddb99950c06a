<template>
    <div class="chartbox">
        <div :id="idx" style="width: 100%; height: 100%"></div>
    </div>
</template>
<script>
import {
    updateEcharts,
    getChargeOption,
    someMax,
    roundNumFun,
} from './../const'
import {
    toRefs,
    computed,
    watch,
    onMounted,
    ref,
    nextTick,
    reactive,
} from 'vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import _cloneDeep from 'lodash/cloneDeep'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import useTheme from '@/common/useTheme'
export default {
    name: 'barCharts',
    props: {
        chartData: {
            type: Object,
            default: () => ({}),
        },
        chargeSelect: String,
    },
    setup(props) {
        const { t, locale } = useI18n()
        const { chartData, chargeSelect } = toRefs(props)
        const state = reactive({})
        const idx = computed(() => {
            return 'id' + parseInt(Math.random() * 100000000)
        })

        const setOption = () => {
            nextTick(() => {
                if (chartData.value && chartData.value.length) {
                    const dateList = chartData.value.map((item) => {
                        if (chargeSelect.value == 'hour') {
                            return String(item.hour).padStart(2, '0') + ':00'
                        } else if (chargeSelect.value == 'day') {
                            return dayjs(item.date).format('MM/DD')
                        } else if (chargeSelect.value == 'month') {
                            return dayjs(item.date).format('YYYY/MM')
                        }
                    })
                    const profit = []
                    const profitData = []

                    chartData.value.forEach((item) => {
                        profitData.push(item?.profit || 0)
                        profit.push({
                            value: item?.profit || 0,
                            isBooan: false,
                        })
                    })
                    const isBooan = someMax([...profitData], 10000)
                    if (isBooan && locale.value == 'zh') {
                        profit.forEach((item) => {
                            item.value = roundNumFun(item.value / 10000, 2)
                            item.isBooan = true
                        })
                    }
                    const options = _cloneDeep(getChargeOption())
                    options.legend.data = [t('station_shouyi')]
                    options.xAxis.data = dateList
                    options.yAxis.name =
                        locale.value == 'zh'
                            ? t('station_shouyi') + '/' + t('common_yuan')
                            : t('station_shouyi')
                    options.series[0].data = profit
                    options.series[0].type = 'bar'
                    options.series[0].name = t('station_shouyi')
                    options.series[0].symbol = 'none'
                    options.series[0].smooth = true
                    options.series[0].showSymbol = false
                    options.series[1] = undefined
                    // if(profit.length >= 12) {

                    // }
                    if (profit.length >= 13) {
                        options.series[0].barWidth = '8px'
                    } else if (profit.length >= 8) {
                        options.series[0].barWidth = '12px'
                    } else {
                        options.series[0].barWidth = '20px'
                    }
                    if (isBooan && locale.value == 'zh') {
                        options.yAxis.name =
                            locale.value == 'zh'
                                ? t('station_shouyi') +
                                  '/' +
                                  t('common_wanyuan')
                                : t('station_shouyi')

                        options.yAxis.nameTextStyle.align = 'center'
                        // options.yAxis.nameTextStyle.width = '60'
                    }
                    updateEcharts(idx.value, options)
                }
            })
        }
        watch(
            chartData,
            async (val) => {
                if (val) {
                    setOption()
                }
                // 处理数据

                // await initEcharts()
            }
            // { immediate: true }
        )
        const store = useStore()
        const { themeColors, themeChangeComplete } = useTheme()
        const isDark = computed(() => {
            return store.state.theme.isDark
        })
        watch([isDark, themeChangeComplete], ([newIsDark, isComplete]) => {
            // Only update chart when theme change is complete
            if (isComplete) {
                setOption()
            }
        })

        return {
            ...toRefs(state),
            idx,
        }
    },
}
</script>
<style lang="less" scoped>
.chartbox {
    width: 100%;
    height: 306px;
}
</style>
