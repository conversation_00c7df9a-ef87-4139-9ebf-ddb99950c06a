<template>
    <el-drawer
        :model-value="visible"
        :size="486"
        :show-close="false"
        @close="AiOptimizeClose"
        wrapClassName="drawerBox"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header text-primary-text dark:text-80-dark">
                    <span>{{ 'AI策略优化' }}</span>
                </div>
                <div class="flex gap-x-3 items-center">
                    <el-button plain round @click="AiOptimizeClose">{{
                        $t('common_guanbi')
                    }}</el-button>
                    <el-button plain type="primary" round @click="onSave"
                        >保存</el-button
                    >
                </div>
            </div>
        </template>
        <div class="space-y-4">
            <div class="w-full">
                <div class="mb-3">电站基础信息</div>
                <div class="p-4 bg-background rounded">
                    <div class="py-1 flex">
                        <span class="text-secondar-text dark:text-60-dark w-18"
                            >站点名称：</span
                        >
                        <div class="flex-1 w-0">
                            {{ data.stationName }}
                        </div>
                    </div>
                    <div class="py-1 flex alarm-stauts">
                        <span class="text-secondar-text dark:text-60-dark w-18"
                            >站点编号：</span
                        >
                        <div class="flex-1 w-0">{{ data.stationNo }}</div>
                    </div>
                    <div class="py-1 flex">
                        <span class="text-secondar-text dark:text-60-dark w-18"
                            >额定功率：</span
                        >
                        <div class="flex-1 w-0">{{ data.installedPower }}</div>
                    </div>
                    <div class="py-1 flex">
                        <span class="text-secondar-text dark:text-60-dark w-18"
                            >额定容量：</span
                        >
                        <div class="flex-1 w-0">
                            {{ data.installedCapacity }}
                        </div>
                    </div>
                    <div class="py-1 flex">
                        <span class="text-secondar-text dark:text-60-dark w-18"
                            >电压等级：</span
                        >
                        <div class="flex-1 w-0">{{ data.voltage }}</div>
                    </div>
                    <div class="py-1 flex">
                        <span class="text-secondar-text dark:text-60-dark w-18"
                            >倍率：</span
                        >
                        <div class="flex-1 w-0">{{ data.chargeRate }}</div>
                    </div>
                    <div class="py-1 flex">
                        <span class="text-secondar-text dark:text-60-dark w-18"
                            >PCS个数：</span
                        >
                        <div class="flex-1 w-0">{{ data.pcsCount }}</div>
                    </div>
                    <div class="py-1 flex">
                        <span class="text-secondar-text dark:text-60-dark w-18"
                            >电池簇数：</span
                        >
                        <div class="flex-1 w-0">{{ data.rackCount }}</div>
                    </div>
                    <div class="py-1 flex">
                        <span class="text-secondar-text dark:text-60-dark w-18"
                            >电池包数：</span
                        >
                        <div class="flex-1 w-0">{{ data.packCount }}</div>
                    </div>
                    <div class="py-1 flex">
                        <span class="text-secondar-text dark:text-60-dark w-18"
                            >电芯个数：</span
                        >
                        <div class="flex-1 w-0">{{ data.batteryCount }}</div>
                    </div>
                </div>
            </div>
            <div>
                <div class="mb-3">AI智能模式</div>
                <div class="p-4 bg-background rounded">
                    <div class="py-1 flex justify-between items-center">
                        <span class="text-secondar-text dark:text-60-dark"
                            >启用AI策略优化：</span
                        >
                        <span class="">
                            <a-switch
                                v-model:checked="openAiStrategy"
                                :checkedValue="1"
                                :unCheckedValue="0"
                            />
                        </span>
                    </div>
                    <div class="py-1 flex justify-between items-center">
                        <span class="text-secondar-text dark:text-60-dark"
                            >策略整体倾向：</span
                        >
                        <span class="">
                            <a-select
                                v-model:value="strategyDirection"
                                class="w-40"
                                placeholder="请选择"
                                disabled
                            >
                                <a-select-option
                                    v-for="item in strategyDirections"
                                    :key="item.value"
                                    :value="item.value"
                                    >{{ item.label }}</a-select-option
                                >
                            </a-select>
                        </span>
                    </div>
                    <div class="py-1 flex justify-between items-center">
                        <span class="text-secondar-text dark:text-60-dark"
                            >启用AI智能需求响应：</span
                        >
                        <span class="">
                            <a-switch
                                v-model:checked="openVppDemand"
                                :checkedValue="1"
                                :unCheckedValue="0"
                            />
                        </span>
                    </div>
                </div>
            </div>
            <div v-if="false">
                <div class="mb-3">运行约束（选填）</div>
                <div class="p-4 bg-background rounded">
                    <div class="py-1 flex justify-between items-center">
                        <span class="text-secondar-text dark:text-60-dark"
                            >SOC运行上限（%）：</span
                        >
                        <span class="">
                            <a-input-number
                                v-model:value="formState.xxxxxxxxx"
                                :min="0"
                                :max="100"
                                :step="1"
                                :precision="0"
                            ></a-input-number>
                        </span>
                    </div>
                    <div class="py-1 flex justify-between items-center">
                        <span class="text-secondar-text dark:text-60-dark"
                            >SOC运行下限（%）：</span
                        >
                        <span class="">
                            <a-input-number
                                v-model:value="formState.xxxxxxxxx"
                                :min="0"
                                :max="100"
                                :step="1"
                                :precision="0"
                            ></a-input-number>
                        </span>
                    </div>
                    <div class="py-1 flex justify-between items-center">
                        <span class="text-secondar-text dark:text-60-dark"
                            >日充放电次数上限：</span
                        >
                        <span class="">
                            <a-input-number
                                v-model:value="formState.xxxxxxxxx"
                                :min="0"
                                :max="10"
                                :step="1"
                                :precision="0"
                            ></a-input-number>
                        </span>
                    </div>
                    <div class="py-1 flex justify-between items-center">
                        <span class="text-secondar-text dark:text-60-dark"
                            >日充放电次数下限：</span
                        >
                        <span class="">
                            <a-input-number
                                v-model:value="formState.xxxxxxxxx"
                                :min="0"
                                :max="10"
                                :step="1"
                                :precision="0"
                            ></a-input-number>
                        </span>
                    </div>
                    <div class="py-1 flex justify-between items-center">
                        <span class="text-secondar-text dark:text-60-dark"
                            >最大充电功率（kW）：</span
                        >
                        <span class="">
                            <a-input-number
                                v-model:value="formState.xxxxxxxxx"
                                :min="0"
                                :max="maxPower"
                                :step="1"
                                :precision="0"
                            ></a-input-number>
                        </span>
                    </div>
                    <div class="py-1 flex justify-between items-center">
                        <span class="text-secondar-text dark:text-60-dark"
                            >最小充电功率（kW）：</span
                        >
                        <span class="">
                            <a-input-number
                                v-model:value="formState.xxxxxxxxx"
                                :min="0"
                                :max="maxPower"
                                :step="1"
                                :precision="0"
                            ></a-input-number>
                        </span>
                    </div>
                    <div class="py-1 flex justify-between items-center">
                        <span class="text-secondar-text dark:text-60-dark"
                            >最大放电功率（kW）：</span
                        >
                        <span class="">
                            <a-input-number
                                v-model:value="formState.xxxxxxxxx"
                                :min="0"
                                :max="maxPower"
                                :step="1"
                                :precision="0"
                            ></a-input-number>
                        </span>
                    </div>
                    <div class="py-1 flex justify-between items-center">
                        <span class="text-secondar-text dark:text-60-dark"
                            >最小放电功率（kW）：</span
                        >
                        <span class="">
                            <a-input-number
                                v-model:value="formState.xxxxxxxxx"
                                :min="0"
                                :max="maxPower"
                                :step="1"
                                :precision="0"
                            ></a-input-number>
                        </span>
                    </div>
                    <div class="py-1 flex justify-between items-center">
                        <span class="text-secondar-text dark:text-60-dark"
                            >最小套利价差（元/kWh）：</span
                        >
                        <span class="">
                            ¥
                            <a-input-number
                                v-model:value="formState.xxxxxxxxx2"
                                :min="0"
                                :max="10"
                                :step="0.01"
                                :precision="2"
                            ></a-input-number>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </el-drawer>

    <el-dialog v-model="modalVisible" title="提示：" width="500">
        <div class="text-sm">
            启用AI策略优化后，站点将会执行系统推荐的运行策略，是否确认？
        </div>
        <div class="text-xs">
            您可在「站点策略管理/能效管理/AI模式页」查看具体运行策略
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button plain round @click="onCancel">否</el-button>
                <el-button plain round type="primary" @click="onConfirm">
                    是
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, getCurrentInstance, h } from 'vue'
import apiVpp from '@/apiService/vpp'
const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    data: {
        type: Object,
        default: () => ({}),
    },
})
const emit = defineEmits(['close'])
const AiOptimizeClose = () => {
    emit('close')
}
const openAiStrategy = ref(false)
const strategyDirection = ref(1)
const strategyDirections = ref([
    {
        label: 'AI智能均衡',
        value: 1,
    },
    {
        label: 'AI智障均衡',
        value: 2,
    },
])

const openVppDemand = ref(false)
const formState = reactive({
    socMax: undefined,
    socMin: undefined,
    chargePowerMax: undefined,
    chargePowerMin: undefined,
    dischargePowerMax: undefined,
    dischargePowerMin: undefined,
})
const maxPower = ref(100)
const proxy = getCurrentInstance().proxy

const setData = async () => {
    if (!!props.data.openAiStrategy != !!openAiStrategy.value) {
        await apiVpp.openVppAiStrategy({
            stationId: props.data.stationId,
            openAiStrategy: openAiStrategy.value,
        })
    }
    if (!!props.data.openVppDemand != !!openVppDemand.value) {
        await apiVpp.openVppDemand({
            stationId: props.data.stationId,
            openVppDemand: openVppDemand.value,
        })
    }
    AiOptimizeClose()
    modalVisible.value = false
}
const modalVisible = ref(false)
const onSave = () => {
    if (openAiStrategy.value) {
        modalVisible.value = true
    } else {
        setData()
    }
}
const onCancel = () => {
    modalVisible.value = false
}
const onConfirm = async () => {
    setData()
}
watch(
    () => props.data,
    (newVal) => {
        if (newVal && Object.keys(newVal).length) {
            openAiStrategy.value = newVal.openAiStrategy ? 1 : 0
            openVppDemand.value = newVal.openVppDemand ? 1 : 0
        }
    }
)
</script>

<style lang="less" scoped>
:deep(.ant-switch) {
    min-width: 40px;
    height: 24px;
}

.ant-switch-loading-icon,
.ant-switch::after {
    width: 20px;
    height: 20px;
}

:deep(.ant-switch-checked) {
    background-color: #7d4af9;
}
</style>
