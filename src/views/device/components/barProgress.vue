<template>
    <!-- <em class="barProgress" :style="`width: calc(${fullWidth} * ${ratio});${style}`"></em> -->
    <div class="progress-box">
        <div class="barProgress" :style="{ width: boxWidth }"></div>
    </div>
</template>
<script>
import { reactive, toRefs, onMounted, computed } from 'vue'
export default {
    name: `BarProgress`,
    props: {
        style: String,
        fullWidth: {
            validator: (v) => typeof v === 'string' || typeof v === 'number',
            default: 90,
        },
        ratio: {
            type: Number,
            default: 1,
            validator: (v) => v <= 1 && v >= 0,
        }, // 0-1
    },
    setup(props) {
        const { fullWidth, ratio } = toRefs(props)
        const boxWidth = computed(() => {
            const num = fullWidth.value * ratio.value
            return `${num > 0 ? num : 1}%`
        })
        return {
            boxWidth,
        }
    },
}
</script>
<style lang="less" scoped>
.progress-box {
    width: 80px;
    background: #eee;
    height: 10px;
    // padding: 2px;
}

.barProgress {
    background-color: #faad14;
    height: 10px;
}
</style>
