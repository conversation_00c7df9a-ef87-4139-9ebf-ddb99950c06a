const target = 62
const data = {
  "6/8": "72.5",
  "6/9": "72.1",
  "6/10": "72.1",
  "6/11": "71.8",
  "6/12": "71.6",
  "6/13": "71.4",
  "6/14": "71",
  "6/15": "71",
  "6/16": "71",
  "6/17": "71",
  "6/18": "71",
  "6/19": "71",
  "6/20": "70.8",
  "6/21": "70.8",
  "6/22": "70.4",
  "6/23": "70.4",
  "6/24": "69.9",
  "6/25": "69.2",
  "6/26": "69",
  "6/27": "69.1",
  "6/28": "69.4",
  "6/29": "69.9",
  "6/30": "70.1",
  "7/1": "70.1",
  "7/2": "69.9",
  "7/3": "69.8",
  "7/4": "69.7",
  "7/5": "69.7",
  "7/6": "69.3",
  "7/7": "69",
  "7/8": "68.8",
  "7/9": "69",
  "7/10": "68.9",
  "7/11": "69.5",
  "7/12": "69.3",
  "7/13": "69.5",
  "7/14": "70",
  "7/15": "69.8",
  "7/16": "69.5",
  "7/17": "68.8",
  "7/18": "",
  "7/19": "",
  "7/20": "",
  "7/21": "",
  "7/22": "",
  "7/23": "",
  "7/24": "",
  "7/25": "",
  "7/26": "",
  "7/27": "",
  "7/28": "",
  "7/29": "",
  "7/30": "",
  "7/31": "",
  "8/1": "",
  "8/2": "",
  "8/3": "",
  "8/4": "",
  "8/5": "",
  "8/6": "",
  "8/7": "",
  "8/8": "",
  "8/9": "",
  "8/10": "",
  "8/11": "",
  "8/12": "",
  "8/13": "",
  "8/14": "",
  "8/15": "",
  "8/16": "",
  "8/17": "",
  "8/18": "",
  "8/19": "",
  "8/20": "",
  "8/21": "",
  "8/22": "",
  "8/23": "",
  "8/24": "",
  "8/25": "",
  "8/26": "",
  "8/27": "",
  "8/28": "",
  "8/29": "",
  "8/30": "",
  "8/31": "",
}
const xData = Object.keys(data)
const yData = Object.values(data)
const yData2 = new Array(yData.length).fill(62)

// 拟合直线计算
let sumX = 0, sumY = 0;
let n = yData.findIndex(i => i === ''); // n取有数据的点的个数

for (let i = 0; i < n; i++) {
    sumX += i;
    sumY += Number(yData[i]);
}

let meanX = sumX / n;
let meanY = sumY / n;

let numerator = 0, denominator = 0;

for (let i = 0; i < n; i++) {
    numerator += (i - meanX) * (Number(yData[i]) - meanY);
    denominator += (i - meanX) ** 2;
}

let a = numerator / denominator;
let b = meanY - a * meanX;

const predict = x => {
  const val = a * x + b
  return val
} 
const yData3 = xData.map((x, i) => predict(i))

const myChart = echarts.init(document.getElementById('main'));
const option = {
  xAxis: {
    type: 'category',
    data: xData
  },
  yAxis: {
    type: 'value',
    max: 75,
    min: 60
  },
  series: [
    {
      data: yData2,
      type: 'line',
      smooth: true,
      showSymbol: false,
    },
    {
      data: yData3,
      type: 'line',
      smooth: true,
      showSymbol: false,
      lineStyle: {
        color: '#aaa',
        type: 'dashed',
        width: 1
      }
    },
    {
      data: yData,
      type: 'line',
      smooth: true,
      showSymbol: false,
      lineStyle: {
        color: '#f27'
      }
    },
  ]
};
myChart.setOption(option);
window.addEventListener('resize', myChart.resize)