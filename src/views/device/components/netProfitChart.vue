<template>
    <div id="net-profit-chart"></div>
</template>

<script setup>
import * as echarts from 'echarts'
import { onMounted, watch, nextTick } from 'vue'
const props = defineProps({
    chartData: {
        type: Object,
        default: () => ({}),
    },
})
const option = {
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow',
        },
    },
    color: ['#6FBECE', '#FAC858', '#7D4AF9'],
    legend: {
        data: ['收入：峰谷套利+需求响应', '支出：设备新旧+运营成本', '净收益'],
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 20,
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
    },
    xAxis: [
        {
            type: 'value',
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                    color: ['#d9d9d9'],
                },
            },
        },
    ],
    yAxis: [
        {
            position: 'right',
            type: 'category',
            axisTick: {
                show: false,
            },
            data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
        },
    ],
    series: [
        {
            name: '收入：峰谷套利+需求响应',
            type: 'bar',
            stack: 'Total',
            label: {
                show: false,
            },
            barWidth: 12,
            // emphasis: {
            //     focus: 'series',
            // },
            data: [320, 302, 341, 374, 390, 450, 420],
        },
        {
            name: '支出：设备新旧+运营成本',
            type: 'bar',
            stack: 'Total',
            label: {
                show: false,
                position: 'left',
            },
            barWidth: 12,
            // emphasis: {
            //     focus: 'series',
            // },
            data: [-120, -132, -101, -134, -190, -230, -210],
        },
        {
            name: '净收益',
            type: 'bar',
            // stack: 'Total',
            label: {
                show: false,
                position: 'inside',
            },
            barWidth: 12,
            // emphasis: {
            //     focus: 'series',
            // },
            data: [200, 170, 240, 244, 200, 220, 210],
        },
    ],
}

watch(
    () => props.chartData,
    (val) => {
        if (val.length) {
            nextTick(() => {
                let newArr
                if (val[val.length - 1].date == '总计') {
                    newArr = val.slice(0, val.length - 1)
                } else {
                    newArr = val
                }
                const chart = echarts.init(
                    document.getElementById('net-profit-chart')
                )
                option.yAxis[0].data = newArr.map((item) => item.date)
                option.series[0].data = newArr.map((item) => item.profit)
                option.series[1].data = newArr.map((item) => -item.totalCost)
                option.series[2].data = newArr.map((item) => item.netProfit)
                chart.setOption(option)
            })
        }
    }
)
</script>

<style lang="less" scoped>
#net-profit-chart {
    width: 100%;
    height: 434px;
}
</style>
