<template>
    <div class="inline-flex items-baseline leading-none">
        <span :class="num >= 0 ? 'triangle-rotate' : 'triangle'"></span>
        <span
            class="text-base items-baseline inline-flex"
            :style="{
                color: isDark ? '#fff' : num >= 0 ? '#FF4D4F' : '#33BE4F',
                marginLeft: '4px',
                lineHeight: '18px',
            }"
        >
            <span class="font-medium">
                {{ num || num == 0 ? Math.abs(num) : '-' }}</span
            ><span class="text-xs font-medium">%</span>
        </span>
    </div>
</template>

<script>
import { toRefs, computed } from 'vue'
import { useStore } from 'vuex'
export default {
    props: {
        num: {
            type: Number,
            default: 0,
        },
    },
    setup(props) {
        const store = useStore()
        const { num } = toRefs(props)
        const isDark = computed(() => {
            return store.state.theme.isDark
        })
        return {
            isDark,
        }
    },
}
</script>

<style lang="less" scoped>
.triangle {
    display: inline-block;
    width: 0;
    height: 0;
    border-top: 10px solid #33be4f;
    border-right: 5px solid transparent;
    border-left: 5px solid transparent;
}

.triangle-rotate {
    transform-origin: 50% 50%;
    transform: rotateX(180deg);
    display: inline-block;
    width: 0;
    height: 0;
    border-top: 10px solid #ff4d4f;
    border-right: 5px solid transparent;
    border-left: 5px solid transparent;
}
</style>
