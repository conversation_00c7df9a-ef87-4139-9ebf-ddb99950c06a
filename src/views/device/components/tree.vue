<template>
    <div>
        <div
            class="px-3 tree-nodes space-y-3"
            v-for="item in treeData"
            :key="item.id"
        >
            <div
                class="tree-node"
                :class="item.children?.length ? 'isleaf' : ''"
            >
                {{ item.name }}
            </div>
            <div>
                <tree :tree-data="item.children" />
            </div>
        </div>
    </div>
</template>
<script>
import { ref, reactive, toRefs, watch } from 'vue'
import tree from './tree'
export default {
    components: {
        tree,
    },
    props: {
        treeData: {
            type: Array,
            default: () => [],
        },
    },
    setup(props, { emit }) {
        const { treeData } = toRefs(props)
        const state = reactive({})
        watch(treeData, async (val) => {})
        return {
            ...toRefs(state),
        }
    },
}
</script>

<style lang="less" scoped>
.tree-node {
    line-height: 44px;
}
</style>
