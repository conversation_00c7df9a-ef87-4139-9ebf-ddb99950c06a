<template>
    <div class="chartbox">
        <div :id="idx" style="width: 100%; height: 100%"></div>
    </div>
</template>
<script>
import {
    updateEcharts,
    getChargeOption,
    someMax,
    roundNumFun,
} from './../const'
import {
    toRefs,
    computed,
    watch,
    onMounted,
    ref,
    nextTick,
    reactive,
} from 'vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import _cloneDeep from 'lodash/cloneDeep'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import useTheme from '@/common/useTheme'
export default {
    name: 'barCharts',
    props: {
        chartData: {
            type: Object,
            default: () => ({}),
        },
        chargeSelect: String,
    },
    setup(props) {
        const { t, locale } = useI18n()
        const { chartData, chargeSelect } = toRefs(props)
        const state = reactive({})
        const idx = computed(() => {
            return 'id' + parseInt(Math.random() * 100000000)
        })

        const setOption = () => {
            nextTick(() => {
                if (chartData.value && chartData.value.length) {
                    const dateList = chartData.value.map((item) => {
                        if (chargeSelect.value == 'hour') {
                            return String(item.hour).padStart(2, '0') + ':00'
                        } else if (chargeSelect.value == 'day') {
                            return dayjs(item.date).format('MM/DD')
                        } else if (chargeSelect.value == 'month') {
                            return dayjs(item.date).format('YYYY/MM')
                        }
                        // return dayjs(item.day).format('YYYY/MM/DD')
                    })
                    const dataList = Object.values(chartData.value)
                    const charge = []
                    const chargeData = []
                    const discharge = []
                    const dischargeData = []
                    chartData.value.forEach((item) => {
                        chargeData.push(item?.charge || 0)
                        dischargeData.push(item?.discharge || 0)
                        charge.push({
                            value: item?.charge || 0,
                            isBooan: false,
                        })
                        discharge.push({
                            value: item?.discharge || 0,
                            isBooan: false,
                        })
                    })
                    const isBooan = someMax(
                        [...chargeData, ...dischargeData],
                        1000
                    )
                    if (isBooan) {
                        charge.forEach((item) => {
                            item.value = roundNumFun(item.value / 1000, 2)
                            item.isBooan = true
                        })
                        discharge.forEach((item) => {
                            item.value = roundNumFun(item.value / 1000, 2)
                            item.isBooan = true
                        })
                    }
                    const options = _cloneDeep(getChargeOption())
                    options.legend.data[0] = t('Charging amount')
                    options.legend.data[1] = t('Discharging amount')
                    // options.legend.textStyle.color = '#fff'
                    options.series[0].name = t('Charging amount')
                    options.series[1].name = t('Discharging amount')
                    options.xAxis.data = dateList
                    options.yAxis.name = 'KWh'
                    options.series[0].data = charge
                    options.series[1].data = discharge
                    options.series[0].type = 'bar'
                    options.series[1].type = 'bar'
                    options.series[0].symbol = 'none'
                    options.series[0].smooth = true
                    options.series[0].showSymbol = false
                    options.series[1].symbol = 'none'
                    options.series[1].smooth = true
                    options.series[1].showSymbol = false
                    if (dateList.length >= 15) {
                        options.series[0].stack = '1'
                        options.series[1].stack = '1'
                    } else {
                        options.series[0].stack = '0'
                        options.series[1].stack = '1'
                    }
                    if (dateList.length >= 15) {
                        options.series[0].barWidth = '8px'
                        options.series[1].barWidth = '8px'
                    } else if (dateList.length >= 8) {
                        options.series[0].barWidth = '10px'
                        options.series[1].barWidth = '10px'
                    } else {
                        options.series[0].barWidth = '16px'
                        options.series[1].barWidth = '16px'
                    }
                    if (isBooan) {
                        options.yAxis.name = 'MWh'
                    }
                    updateEcharts(idx.value, options)
                }
            })
        }
        watch(
            chartData,
            async (val) => {
                if (val) {
                    setOption()
                }
                // 处理数据

                // await initEcharts()
            }
            // { immediate: true }
        )
        const store = useStore()
        const { themeColors, themeChangeComplete } = useTheme()
        const isDark = computed(() => {
            return store.state.theme.isDark
        })
        watch([isDark, themeChangeComplete], ([newIsDark, isComplete]) => {
            // Only update chart when theme change is complete
            if (isComplete) {
                setOption()
            }
        })

        return {
            ...toRefs(state),
            idx,
        }
    },
}
</script>
<style lang="less" scoped>
.chartbox {
    width: 100%;
    height: 306px;
}
</style>
