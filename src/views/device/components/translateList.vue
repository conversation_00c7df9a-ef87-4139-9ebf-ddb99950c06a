<template>
    <div class="h-full flex flex-col translate-list pt-6">
        <div class="flex justify-center w-full">
            <div class="flex items-center mb-2">
                <el-button
                    size="small"
                    style="padding: 0 4px; border: none"
                    :disabled="clickNum == 1"
                    @click="leftClick"
                    class="btn-hover"
                >
                    <el-icon :size="20">
                        <CaretLeft />
                    </el-icon>
                </el-button>
                <div class="title">
                    {{ $t('dianchibao').replace('s%', clickNum) }}
                </div>
                <el-button
                    size="small"
                    style="padding: 0 4px; border: none"
                    :disabled="clickNum == batteriesLen"
                    @click="RigthClick"
                    class="btn-hover"
                >
                    <el-icon :size="20">
                        <CaretRight />
                    </el-icon>
                </el-button>
            </div>
        </div>
        <div class="flex-1 flex flex-wrap place-content-start">
            <div class="flex-item-box" v-for="item in list" :key="item.id">
                <div class="relative-box">
                    <div class="absolute-box">
                        <div class="item-content">
                            <div class="item-content-t">{{ item.soc }}%</div>
                            <div class="item-content-c">
                                {{ item.voltage }}v
                            </div>
                            <div class="item-content-b">
                                {{ item.temperature }}°C
                            </div>
                        </div>
                    </div>
                    <!-- backgroundColor:getColor() -->
                    <div
                        class="bg-box"
                        :style="{
                            top: computedNum(item.soc),
                            backgroundColor: getColor(item.soc),
                        }"
                    ></div>
                    <div class="box-hover flex justify-center items-center">
                        <span>#{{ item.sortNo }}</span>
                    </div>
                </div>
            </div>
            <div
                class="flex w-full h-full justify-center items-center"
                v-if="list.length == 0"
            >
                <empty-data :description="$t('car_tips01')" />
            </div>
        </div>
    </div>
</template>

<script>
import { ref, watch, computed, toRefs } from 'vue'
import { CaretLeft, CaretRight } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

export default {
    name: 'translateList',
    components: { CaretLeft, CaretRight },
    props: {
        data: {
            type: Array,
            default: () => {
                return []
            },
        },
        containerNum: {
            type: Number,
            default: () => {
                return 1
            },
        },
        productModel: {
            type: String,
            default: () => {
                return ''
            },
        },
    },
    setup(props, { emit }) {
        const { t, locale } = useI18n()
        const { productModel } = toRefs(props)
        const list = ref([])
        const allList = ref([])
        const num = computed(() => props.containerNum)
        const clickNum = ref(num.value)
        const computedNum = (num) => {
            const proPortion = 100 - num + '%'
            return proPortion
        }

        const getContent = (num, data) => {
            clickNum.value = +num
            if (!data || !data?.length) return []
            for (let i = 0; i < data.length; i++) {
                const box = data[i]
                if (box.sortNo == num) {
                    return box.batteries
                }
            }
            return []
        }

        const getColor = (soh) => {
            if (!soh) return '#fff'
            if (soh >= 30) return 'rgba(46, 212, 163, 0.6)'
            if (soh >= 10 && soh < 30) return 'rgba(253, 117, 11, 1)'
            return 'rgba(253, 11, 11, 1)'
        }

        const leftClick = () => {
            let val = 1
            clickNum.value = clickNum.value - 1
            val = clickNum.value + 3
            emit('svgTab', val)
            list.value = getContent(clickNum.value, allList.value)
        }

        const RigthClick = () => {
            let val = 1
            clickNum.value = clickNum.value + 1
            val = clickNum.value + 3
            emit('svgTab', val)
            list.value = getContent(clickNum.value, allList.value)
        }
        const batteriesLen = computed(() => {
            if (productModel.value == 'SE215') {
                return 5
            } else if (productModel.value == 'SE70') {
                return 10
            } else {
                return 5
            }
        })
        watch(
            () => props.data,
            (val) => {
                allList.value = val || []
                list.value = getContent(num.value, val)
            }
        )
        return {
            computedNum,
            clickNum,
            list,
            getColor,
            leftClick,
            RigthClick,
            batteriesLen,
            t,
            locale,
        }
    },
}
</script>

<style scoped lang="less">
.translate-list {
    padding: 12px;

    .title {
        // color: #6fbece;
        color: var(--text-100);
        // margin-bottom: 16px;
    }

    .flex-item-box {
        width: 8.333%;
        padding: 2px;
        color: var(--text-100);
        box-sizing: border-box;
        cursor: pointer;

        .relative-box {
            height: 100%;
            border-radius: 4px;
            position: relative;
            // background: #ccc;
            background: var(--bg-f5);
            overflow: hidden;
            height: 67px;

            .absolute-box {
                position: absolute;
                left: 0;
                top: 0;
                right: 0;
                bottom: 0;
                z-index: 5;
                background-color: transparent;

                .item-content {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    padding: 8px 0;

                    z-index: 10;

                    .item-content-t {
                        font-size: 16px;
                        margin-bottom: 6.4px;
                    }

                    .item-content-c,
                    .item-content-b {
                        font-size: 10px;
                        line-height: 12px;
                    }
                }
            }

            .bg-box {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                z-index: 2;
                background-color: rgba(46, 212, 163, 1);
            }

            .box-hover {
                border-radius: 0.25rem;
                position: absolute;
                left: 0;
                top: 0;
                right: 0;
                bottom: 0;
                z-index: 11;
                background: rgba(0, 0, 0, 0.6);
                transform: translateX(-102%);
                transition: all 0.5s;

                span {
                    font-size: 12px;
                    color: #ffffff;
                }
            }

            &:hover {
                .box-hover {
                    transform: translateX(0px);
                }
            }
        }
    }
}
</style>
