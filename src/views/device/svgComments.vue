<template>
    <div class="h-full flex svg-ing">
        <div class="relative svg-height my-mr-16 box-hover">
            <!-- <img src="@/assets/device/bms.jpg" class="mr-16"/> -->
            <template v-if="isDark">
                <img
                    src="@/assets/device/device-dark-1.png"
                    v-show="show"
                    class="img-box"
                />
            </template>
            <template v-else>
                <img src="@/assets/device/3.png" class="img-box" />
            </template>

            <a-tooltip placement="right">
                <template #title>
                    <span>{{ $t('station_donghuanguanlixitong') }}</span>
                </template>
                <div
                    :class="{
                        absolute: true,
                        'box-1': true,
                        'box-hover': true,
                        active: actvies == 1,
                    }"
                    @click="boxClick1"
                >
                    <span
                        class="absolute top-2/4 left-2/4 tranlate"
                        v-if="actvies == 1"
                    ></span>
                </div>
            </a-tooltip>

            <a-tooltip placement="top">
                <template #title>
                    <span>{{ $t('station_bianliuqi') }}</span>
                </template>
                <div
                    :class="{
                        absolute: true,
                        'box-2': true,
                        'box-hover': true,
                        active: actvies == 2,
                    }"
                    @click="boxClick2"
                >
                    <span
                        class="absolute top-2/4 left-2/4 tranlate"
                        style="width: max-content"
                        v-if="actvies == 2"
                    ></span>
                </div>
            </a-tooltip>

            <a-tooltip placement="top">
                <template #title>
                    <span>{{ $t('station_dianchiguanlixitong') }}</span>
                </template>
                <!--  @click="boxClick3All" -->
                <!-- 'box-hover': true, -->
                <div
                    :class="{
                        absolute: true,
                        'box-3': true,
                        'box-hover': true,
                        active: actvies == 3,
                        'box-dianchi': true,
                    }"
                    @click="boxClick3All"
                    :style="{ color: showColor }"
                >
                    <a-tooltip placement="right">
                        <template #title>
                            <span>{{ $t('dianchibao').replace('s%', 1) }}</span>
                        </template>
                        <div
                            :class="{
                                absolute: true,
                                'box-3-all': true,
                                ' box-1-absolute': true,
                                'box-hover': true,
                                active: actvies == 4,
                            }"
                            data-click="4"
                            data-containerNo="1"
                            @click.stop="dblclicks(4, 1)"
                        >
                            <span
                                class="absolute top-2/4 left-2/4 tranlate"
                                v-if="actvies == 4"
                                style="
                                    width: 100%;
                                    text-align: center;
                                    index: 0;
                                "
                                data-click="4"
                                data-containerNo="1"
                            ></span>
                        </div>
                    </a-tooltip>

                    <a-tooltip placement="right">
                        <template #title>
                            <span>{{ $t('dianchibao').replace('s%', 2) }}</span>
                        </template>
                        <div
                            :class="{
                                absolute: true,
                                'box-3-all': true,
                                'box-2-absolute': true,
                                'box-hover': true,
                                active: actvies == 5,
                            }"
                            data-click="5"
                            data-containerNo="2"
                            @click.stop="dblclicks(5, 2)"
                        >
                            <span
                                class="absolute top-2/4 left-2/4 tranlate"
                                v-if="actvies == 5"
                                style="width: 100%; text-align: center"
                                data-click="5"
                                data-containerNo="2"
                            ></span>
                        </div>
                    </a-tooltip>

                    <a-tooltip placement="right">
                        <template #title>
                            <span>{{ $t('dianchibao').replace('s%', 3) }}</span>
                        </template>
                        <div
                            :class="{
                                absolute: true,
                                'box-3-all': true,
                                'box-3-absolute': true,
                                'box-hover': true,
                                active: actvies == 6,
                            }"
                            data-click="6"
                            data-containerNo="3"
                            @click.stop="dblclicks(6, 3)"
                        >
                            <span
                                class="absolute top-2/4 left-2/4 tranlate"
                                v-if="actvies == 6"
                                style="
                                    width: 100%;
                                    text-align: center;
                                    index: 0;
                                "
                                data-click="6"
                                data-containerNo="3"
                            ></span>
                        </div>
                    </a-tooltip>

                    <a-tooltip placement="right">
                        <template #title>
                            <span>{{ $t('dianchibao').replace('s%', 4) }}</span>
                        </template>
                        <div
                            :class="{
                                absolute: true,
                                'box-3-all': true,
                                'box-hover': true,
                                'box-4-absolute': true,
                                active: actvies == 7,
                            }"
                            data-click="7"
                            data-containerNo="4"
                            @click.stop="dblclicks(7, 4)"
                        >
                            <span
                                class="absolute top-2/4 left-2/4 tranlate"
                                v-if="actvies == 7"
                                style="width: 100%; text-align: center"
                                data-click="7"
                                data-containerNo="4"
                            ></span>
                        </div>
                    </a-tooltip>

                    <a-tooltip placement="right">
                        <template #title>
                            <span>{{ $t('dianchibao').replace('s%', 5) }}</span>
                        </template>
                        <div
                            :class="{
                                absolute: true,
                                'box-3-all': true,
                                'box-hover': true,
                                'box-5-absolute': true,
                                active: actvies == 8,
                            }"
                            data-click="8"
                            data-containerNo="5"
                            @click.stop="dblclicks(8, 5)"
                        >
                            <span
                                class="absolute top-2/4 left-2/4 tranlate"
                                v-if="actvies == 8"
                                style="width: 100%; text-align: center"
                                data-click="8"
                                data-containerNo="5"
                            ></span>
                        </div>
                    </a-tooltip>

                    <span
                        class="absolute top-2/4 left-2/4 tranlate"
                        v-if="actvies == 3"
                        style="width: 100%; text-align: center"
                    ></span>
                    <!-- 电池管理系统 -->
                </div>
            </a-tooltip>

            <!--空白处返回-->
            <div
                :class="{
                    'box-back-1': true,
                    absolute: true,
                    active: actvies == 10,
                }"
                @click="goBack"
            ></div>

            <div
                :class="{
                    'box-back-2': true,
                    absolute: true,
                    active: actvies == 10,
                }"
                @click="goBack"
            ></div>

            <div
                :class="{
                    'box-back-3': true,
                    absolute: true,
                    active: actvies == 10,
                }"
                @click="goBack"
            ></div>

            <div
                :class="{
                    'box-back-4': true,
                    absolute: true,
                    active: actvies == 10,
                }"
                @click="goBack"
            ></div>

            <div
                :class="{
                    'box-back-5': true,
                    absolute: true,
                    active: actvies == 10,
                }"
                @click="goBack"
            ></div>
        </div>
        <div class="relative svg-height">
            <template v-if="isDark">
                <img
                    src="@/assets/device/device-dark-2.png"
                    v-show="show"
                    class="img-box"
                />
                <img
                    src="@/assets/device/device-dark-1.png"
                    v-show="!show"
                    class="img-box"
                />
            </template>
            <template v-else>
                <img
                    src="@/assets/device/4.png"
                    v-show="show"
                    class="img-box"
                />
                <img
                    src="@/assets/device/2.png"
                    v-show="!show"
                    class="img-box"
                />
            </template>
            <a-tooltip placement="left">
                <template #title>
                    {{ $t('device_type_chunengdianbiao') }}
                </template>
                <div
                    :class="{
                        absolute: true,
                        'box-4': true,
                        'box-hover': true,
                        active: actvies == 9,
                    }"
                    @click="boxClick4"
                >
                    <span
                        class="absolute top-2/4 left-2/4 tranlate"
                        v-if="actvies == 9"
                        style="width: 100%; text-align: center"
                    ></span>
                </div>
            </a-tooltip>

            <div
                :class="{
                    'box-back-6': true,
                    absolute: true,
                    active: actvies == 10,
                }"
                @click="goBack"
            ></div>
            <div
                :class="{
                    'box-back-7': true,
                    absolute: true,
                    active: actvies == 10,
                }"
                @click="goBack"
            ></div>
            <div
                :class="{
                    'box-back-8': true,
                    absolute: true,
                    active: actvies == 10,
                }"
                @click="goBack"
            ></div>
            <div
                :class="{
                    'box-back-9': true,
                    absolute: true,
                    active: actvies == 10,
                }"
                @click="goBack"
            ></div>
        </div>
    </div>
</template>

<script>
import { ref, onMounted, toRefs, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import store from '@/store'
export default {
    name: 'svgComment',
    props: {
        data: {
            type: Array,
            default: () => {
                return []
            },
        },
        propActive: {
            type: Number,
            default: 0,
        },
    },
    setup(props, { emit }) {
        const actvies = ref(10)
        const { t, locale } = useI18n()
        const { data, propActive } = toRefs(props)
        const show = ref(true)
        const isDark = computed(() => {
            return store.state.theme.isDark
        })
        // const timeFn = ref(null)
        const showColor = ref('transparent')
        onMounted(() => {
            // actvies.value = propActive
        })
        watch(
            propActive,
            (val) => {
                actvies.value = val
            }
            // { immediate: true }
        )
        const boxClick3All = () => {
            // clearTimeout(timeFn.value)
            // timeFn.value = setTimeout(() => {
            //     actvies.value = 3
            //     showColor.value = '#222222'
            //     emit('svgClick', { showDetailBox: 1 })
            // }, 250)
            actvies.value = 3
            showColor.value = '#222222'
            show.value = true
            emit('svgClick', { showDetailBox: 1 })
        }

        const boxClick1 = () => {
            actvies.value = 1
            showColor.value = 'transparent'
            show.value = true
            // nextTick(() => {
            //     emit('svgClick', { showDetailBox: 3 })
            // })
            emit('svgClick', { showDetailBox: 3 })
        }

        const boxClick2 = () => {
            actvies.value = 2
            showColor.value = 'transparent'
            show.value = true
            // nextTick(() => {
            //     emit('svgClick', {  showDetailBox: 2 })
            // })
            emit('svgClick', { showDetailBox: 2 })
        }

        const boxClick4 = () => {
            actvies.value = 9
            showColor.value = 'transparent'
            show.value = true
            // nextTick(() => {
            //     emit('svgClick', {  showDetailBox: 4 })
            // })
            emit('svgClick', { showDetailBox: 4 })
        }

        const dblclicks = (a, b) => {
            actvies.value = a
            showColor.value = 'transparent'
            show.value = true
            if (b) {
                emit('svgClick', {
                    showDetailBox: 5,
                    containerNo: b,
                })
            }
        }

        const goBack = () => {
            actvies.value = 10
            showColor.value = 'transparent'
            show.value = true
            emit('svgClick', { showDetailBox: 1 })
        }

        return {
            boxClick3All,
            boxClick1,
            boxClick2,
            boxClick4,
            actvies,
            dblclicks,
            showColor,
            goBack,
            show,
            t,
            isDark,
        }
    },
}
</script>

<style scoped lang="less">
.svg-ing {
    padding: 24px 0 32px 24px;
    font-size: 16px;
    .box-1 {
        width: 60px;
        height: 150px;
        top: 10px;
        left: 11px;
        width: 62px;
        height: 261px;
        cursor: pointer;
        user-select: none;
        border: 2px solid transparent;
    }

    .box-2 {
        width: 80px;
        top: 12px;
        height: 38px;
        left: 79px;
        cursor: pointer;
        user-select: none;
        border: 2px solid transparent;
    }

    .box-3 {
        top: 56px;
        right: 24px;
        height: 214px;
        width: 154px;

        cursor: pointer;
        border: 2px solid transparent;
        .box-3-all {
            height: 37px;
            width: 144px;
            border-radius: 2px;
            left: 3px;
            cursor: pointer;
            user-select: none;
            border: 2px solid transparent;
        }

        .box-1-absolute {
            top: 8px;
        }

        .box-2-absolute {
            top: 48px;
        }

        .box-3-absolute {
            top: 88px;
        }

        .box-4-absolute {
            top: 127px;
        }

        .box-5-absolute {
            top: 166px;
        }
    }

    .box-dianchi {
        display: flex;
        justify-content: center;
        align-items: center;
        user-select: none;
    }

    .box-4 {
        top: 170px;
        height: 60px;
        width: 101px;
        left: 73px;
        cursor: pointer;
        user-select: none;
        border: 2px solid transparent;
    }

    .active {
        background-color: rgba(111, 190, 206, 0.5);
    }

    .tranlate {
        transform: translate(-50%, -50%);
        color: #222222;
    }

    .box-hover {
        &:hover {
            border-color: #72bacb;
        }
    }

    .box-back-1 {
        height: 50px;
        width: 221px;
        bottom: 0;
        cursor: pointer;
        left: 10px;
    }

    .box-back-2 {
        height: 100%;
        width: 10px;
        left: 0;
        top: 0px;
        cursor: pointer;
    }

    .box-back-3 {
        height: 100%;
        width: 24px;
        top: 0;
        right: 0;
        cursor: pointer;
    }

    .box-back-4 {
        height: 10px;
        width: 221px;
        top: 0;
        cursor: pointer;
        left: 10px;
    }

    .box-back-5 {
        height: 41px;
        width: 71px;
        top: 10px;
        right: 24px;
        cursor: pointer;
    }

    .box-back-6 {
        left: 0;
        height: 320px;
        top: 0;
        width: 72px;
        cursor: pointer;
    }

    .box-back-7 {
        width: 11px;
        right: 0;
        top: 0;
        height: 320px;
        cursor: pointer;
    }

    .box-back-8 {
        left: 72px;
        width: 173px;
        height: 11px;
        top: 0;
        cursor: pointer;
    }

    .box-back-9 {
        bottom: 0;
        left: 72px;
        height: 50px;
        width: 173px;
        cursor: pointer;
    }

    .img-box {
        width: 256px;
        height: 321px;
    }

    .svg-height {
        width: 256px;
        height: 321px;
    }

    .my-mr-16 {
        margin-right: 52px;
    }
}
</style>
