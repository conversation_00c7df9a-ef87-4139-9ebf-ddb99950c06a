<template>
    <div class="operation">
        <div class="flex mb-4 justify-between">
            <div class="text-base leading-8 go-box flex items-center">
                <span
                    class="cursor-pointer mr-1 a text-primary-text dark:text-80-dark"
                    @click="goRouter"
                    >{{ $t('Home') }}</span
                >
                <iconSvg
                    name="rightIcon"
                    class="more-icon text-primary-text dark:text-80-dark"
                />
                <!-- 站点详情 -->
                <span class="ml-1">{{ $t('Management') }}</span>
            </div>
        </div>
        <div class="operation-content relative">
            <div class="absolute right-4 top-5 z-10">
                <div
                    class="flex items-center gap-x-4"
                    v-if="activeName == 'inspection'"
                ></div>
                <div
                    class="flex items-center gap-x-4"
                    v-if="activeName == 'inspection'"
                >
                    <el-tree-select
                        v-model="selectedSupplier"
                        :data="treeData"
                        check-strictly
                        :render-after-expand="false"
                        style="width: 240px"
                        default-expand-all
                        clearable
                        @change="onTreeChange"
                        placeholder="请选择所属企业"
                        :props="{
                            value: 'id',
                            label: 'name',
                            children: 'children',
                        }"
                    />
                    <el-select
                        v-model="operationStatus"
                        style="width: 144px"
                        @change="onChangeOperationStatus"
                        placeholder="投运状态"
                    >
                        <el-option
                            v-for="(item, index) in operationStatusOptions"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                    <div>
                        <el-select
                            v-model="searchType"
                            style="width: 144px"
                            @change="onChangeSearchType"
                        >
                            <el-option
                                v-for="item in searchTypes"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                        <el-select
                            v-model="searchValue"
                            filterable
                            placeholder="请选择站点"
                            style="width: 180px"
                            clearable
                            @change="onSearchTable"
                        >
                            <el-option
                                v-for="item in stations"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </div>
                </div>
            </div>
            <el-tabs v-model="activeName" @tab-change="handleTabChange">
                <template #default>
                    <el-tab-pane label="巡检管理" name="inspection">
                        <div class="table-tabs pt-4">
                            <inspection
                                v-if="activeName == 'inspection'"
                                v-model:tableData="inspectionData"
                                :tableLoading="tableLoading"
                                @update="update"
                            />
                            <div class="flex justify-center mt-4">
                                <el-pagination
                                    background
                                    :page-sizes="[10, 20, 30, 40]"
                                    layout="prev, pager, next, sizes"
                                    :total="pageTotal"
                                    v-model:current-page="pageInfo.current"
                                    :page-size="pageInfo.size"
                                    @change="pageChange"
                                    @size-change="handleSizeChange"
                                />
                            </div>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="告警管理" name="alarm">
                        <div class="table-tabs">
                            <alarm
                                v-if="activeName == 'alarm'"
                                :tableLoading="tableLoading"
                                @lookWorkOrder="lookWorkOrder"
                            />
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="工单管理" name="workOrder">
                        <div class="table-tabs">
                            <work-order
                                v-if="activeName == 'workOrder'"
                                ref="workOrderRef"
                                :getAlarmDataFlag="getAlarmDataFlag"
                            />
                        </div>
                    </el-tab-pane>
                </template>
            </el-tabs>
        </div>
    </div>
</template>

<script setup>
import { onMounted, ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import inspection from './components/inspection'
import alarm from './components/alarm'
import workOrder from './components/workOrder'
import { operationStatuss } from '@/views/device/const'
import apiService from '@/apiService/device'
import api from '@/apiService/strategy'
import powerApi from '@/apiService/power'

const router = useRouter()
const store = useStore()

const goRouter = () => {
    if (activeSystem.value == 'car') {
        router.push({
            path: '/vehicle',
        })
    } else {
        router.push({
            path: '/device',
        })
    }
}
const activeName = ref('inspection')
const handleTabChange = () => {
    getData()
}
const getDataHis = ref([])

// ⬇️巡检
const selectedSupplier = ref()
const treeData = ref([])
const getTreeData = async () => {
    const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])
    const {
        data: { data, code },
    } = await apiService.getDeviceTree({
        supplierId: getCompanyInfo?.value?.orgId,
        businessType: 'energy_storage_cabinet',
    })
    if (code === 0) {
        const tree = [
            {
                name: getCompanyInfo?.value?.orgName,
                id: getCompanyInfo?.value?.orgId,
                children: data || [],
            },
        ]
        treeData.value = tree
    }
}
const searchValue = ref()
const searchTypes = ref([
    // {
    //     value: 'id',
    //     label: '站点编号',
    // },
    {
        value: 'name',
        label: '站点名称',
    },
])
const searchType = ref('name')
const tableLoading = ref(false)
const pageInfo = ref({
    current: 1,
    size: 10,
})

const operationStatus = ref(1)
const onChangeOperationStatus = async (e) => {
    await onSearchTable()
}
const operationStatusOptions = ref(
    [].concat([{ value: -1, label: '全部投运状态' }], operationStatuss)
)
const pageTotal = ref(0)
const inspectionData = ref([])
const onSearchTable = async () => {
    let params = {
        orgId: selectedSupplier.value,
        stationNo: searchValue.value,
        // stationName: searchType.value == 'name' ? searchValue.value : undefined,
        ...pageInfo.value,
        operationStatus:
            operationStatus.value == -1 ? undefined : operationStatus.value,
    }
    let res = await apiService.getInspectionDevicePage(params)
    inspectionData.value = res.data.data.records
    pageTotal.value = res.data.data.total
}
const onChangeSearchType = () => {
    searchValue.value = ''
}
const onTreeChange = async () => {
    //
    await onSearchTable()
}

// ⬆️  巡检

// ⬇️  工单
const getAlarmDataFlag = ref(false)
// ⬆️ 工单
const getData = async () => {
    tableLoading.value = true
    if (activeName.value == 'inspection') {
        //
        if (getDataHis.value.includes('inspection')) {
            tableLoading.value = false
            return
        }
        await getTreeData()
        await onSearchTable()
        getDataHis.value.push('inspection')
    } else if (activeName.value == 'alarm') {
        //
        if (getDataHis.value.includes('alarm')) {
            tableLoading.value = false
            return
        }
        //
        getDataHis.value.push('alarm')
    } else if (activeName.value == 'workOrder') {
        getAlarmDataFlag.value = false
        setTimeout(() => {
            getAlarmDataFlag.value = true
        }, 100)
    }
    tableLoading.value = false
}
const pageChange = async () => {
    await onSearchTable()
}
const handleSizeChange = (e) => {
    pageInfo.value.size = e
}
const workOrderRef = ref(null)
const lookWorkOrder = (e) => {
    activeName.value = 'workOrder'
    nextTick(() => {
        workOrderRef.value.getSpecialWorkDetail(e)
    })
}
const update = async () => {
    await onSearchTable()
}
const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])
const orgId = ref(
    getCompanyInfo?.value?.orgId ? getCompanyInfo.value.orgId : void 0
)
const stations = ref()
const activeSystem = computed(() => localStorage.getItem('activeSystem'))
const getStations = async () => {
    const page = {
        orgId: orgId.value,
        stationType:
            activeSystem.value == 'car'
                ? 'vehicle_battery'
                : 'energy_storage_cabinet',
    }
    const res = await api.getOrgAndSubOrgStationNameList(page)
    stations.value = res.data.data.map((item) => ({
        label: item.stationName,
        value: item.stationNo,
    }))
}

onMounted(async () => {
    await getStations()
    await getData()
})
</script>

<style lang="less" scoped>
.go-box {
    color: var(--text-60);
}
.operation {
    padding-top: 88px;
}
.operation-content {
    background: var(--input-bg);
    border-radius: 8px;
    padding: 16px;
    min-height: calc(~'100vh - 180px');
}

:deep(.el-tabs__header) {
    // margin-bottom: 30px;
}

:deep(.el-tabs__nav-wrap:after) {
    display: none;
}
:deep(.more-icon) {
    width: 20px;
    height: 20px;
    color: var(--text-60);
}
:deep(.el-tabs__item) {
    color: var(--text-100) !important;
}
:deep(.el-tabs__item.is-active) {
    color: var(--themeColor) !important;
}
:deep(.el-tabs__item:hover) {
    color: var(--themeColor) !important;
}
</style>
