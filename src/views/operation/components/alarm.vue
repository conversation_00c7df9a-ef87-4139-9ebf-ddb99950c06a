<template>
    <div>
        <div class="flex justify-between items-center mb-3">
            <div class="leading-8 text-title dark:text-title-dark">
                {{ $t('gaojingjibie') }}
            </div>
            <div class="flex items-center gap-x-4"></div>
        </div>
        <div>
            <div
                class="echarts-all-box flex gap-x-6 text-title dark:text-title-dark"
            >
                <div class="flex-1 charts-box flex items-center">
                    <div id="echarts-1" class="ecahrts-dom"></div>
                    <div class="space-y-1">
                        <div
                            v-for="(item, index) in chartData.chart1Data"
                            :key="index"
                            class="flex items-center text-sm leading-5"
                        >
                            <div
                                class="w-2 h-2 rounded-full mr-1"
                                :style="{
                                    backgroundColor: item.itemStyle.color,
                                }"
                            ></div>
                            <div class="w-20">{{ item.legendName }}</div>
                            <div>{{ item.value }}{{ $t('common_tiao') }}</div>
                        </div>
                    </div>
                </div>
                <div class="flex-1 charts-box flex items-center">
                    <div id="echarts-2" class="ecahrts-dom"></div>
                    <div class="space-y-1">
                        <div
                            v-for="(item, index) in chartData.chart2Data"
                            :key="index"
                            class="flex items-center text-sm leading-5"
                        >
                            <div
                                class="w-2 h-2 rounded-full mr-1"
                                :style="{
                                    backgroundColor: item.itemStyle.color,
                                }"
                            ></div>
                            <div class="w-20">{{ item.legendName }}</div>
                            <div>{{ item.value }}{{ $t('common_tiao') }}</div>
                        </div>
                    </div>
                </div>
                <div class="flex-1 charts-box flex items-center">
                    <div id="echarts-3" class="ecahrts-dom"></div>
                    <div class="space-y-1">
                        <div
                            v-for="(item, index) in chartData.chart3Data"
                            :key="index"
                            class="flex items-center text-sm leading-5"
                        >
                            <div
                                class="w-2 h-2 rounded-full mr-1"
                                :style="{
                                    backgroundColor: item.itemStyle.color,
                                }"
                            ></div>
                            <div class="w-20">{{ item.legendName }}</div>
                            <div>{{ item.value }}{{ $t('common_tiao') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bg-ff dark:bg-ff-dark table rounded-lg w-full mt-4">
            <search
                title="告警列表"
                :searchData="searchData"
                v-model:filter="searchData.filterData.value"
                @search="refresh"
                :showRefresh="false"
                class="teble-search"
            >
            </search>
            <div class="table-box">
                <mw-table
                    :dataSource="tableData"
                    :columns="columns"
                    :hasPage="true"
                    :pageConfig="{ changePage, paginationProps }"
                    :customRow="Rowclick"
                    :rowKey="(record) => record.id"
                    @change="onTableChange"
                    :showRefresh="false"
                    class="alarm-table"
                >
                    <template #alarmDesc="{ record }">
                        <div
                            class="alarm-name font-medium text-primary-text dark:text-80-dark"
                        >
                            {{ record.alarmDesc || '-' }}
                        </div>
                        <div
                            class="alarm-sn text-secondar-text dark:text-60-dark"
                        >
                            设备编号：{{ record.deviceSn }}
                        </div>
                    </template>
                    <template #alarmStatus="{ record }">
                        <dictionary
                            :statusOptions="alarmStatusList"
                            :value="record.alarmStatus"
                            :color="'color'"
                        />
                    </template>
                    <template #deviceType="{ record }">
                        <dictionary
                            :showBadge="false"
                            :statusOptions="deviceTypeList"
                            :value="record.deviceType"
                        />
                    </template>
                    <template #alarmLevel="{ record }">
                        <dictionary
                            :showBadge="false"
                            :statusOptions="alarmLevelList"
                            :value="record.alarmLevel"
                        />
                    </template>
                    <template #alarmTime="{ record }">
                        {{ dayjs(record.alarmTime).format('YYYY-MM-DD HH:mm') }}
                    </template>
                </mw-table>
            </div>
        </div>
        <el-drawer
            v-model="detailVisible"
            :size="486"
            :show-close="false"
            :close-on-press-escape="false"
            :destroy-on-close="true"
            @close="onCloseDetail"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5 text-title dark:text-title-dark"
                >
                    <div><span>告警详情</span></div>
                    <div class="flex gap-x-3">
                        <el-button plain round @click="onCloseDetail">{{
                            $t('common_guanbi')
                        }}</el-button>
                        <confirm-button
                            v-if="detailInfo?.alarmStatus === 0 && isOperator"
                            :title="'是否忽略告警?'"
                            @confirm="ignoreAlarm"
                            placement="bottom-end"
                        >
                            <template #reference>
                                <el-button plain round>忽略告警</el-button>
                            </template>
                        </confirm-button>
                        <confirm-button
                            v-if="detailInfo?.alarmStatus === 0 && isOperator"
                            :title="'是否转为工单?'"
                            @confirm="ConvertToWorkOrder"
                            placement="bottom-end"
                        >
                            <template #reference>
                                <el-button plain round type="primary"
                                    >转为工单</el-button
                                >
                            </template>
                        </confirm-button>
                        <el-button
                            v-if="detailInfo?.workOrderId"
                            round
                            @click="onLookWorkOrder"
                        >
                            查看工单
                        </el-button>
                    </div>
                </div>
            </template>
            <div class="text-title dark:text-title-dark">
                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-18">
                        告警名称：
                    </div>
                    <div class="flex-1">{{ detailInfo?.alarmDesc }}</div>
                </div>
                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-18">
                        告警编号：
                    </div>
                    <div class="flex-1">{{ detailInfo?.tagNo }}</div>
                </div>

                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-18">
                        告警状态：
                    </div>
                    <div class="flex-1">
                        <dictionary
                            :statusOptions="alarmStatusList"
                            :value="detailInfo?.alarmStatus"
                            :color="'backGroundColor'"
                        />
                    </div>
                </div>
                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-18">
                        告警级别：
                    </div>
                    <div class="flex-1">
                        <dictionary
                            :statusOptions="alarmLevelList"
                            :value="detailInfo?.alarmLevel"
                            :color="'backGroundColor'"
                        />
                    </div>
                </div>

                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-18">
                        站点名称：
                    </div>
                    <div class="flex-1">{{ detailInfo?.stationName }}</div>
                </div>
                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-18">
                        站点编号：
                    </div>
                    <div class="flex-1">{{ detailInfo?.stationNo }}</div>
                </div>
                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-18">
                        机柜编号：
                    </div>
                    <div class="flex-1">{{ detailInfo?.containerNo }}</div>
                </div>
                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-18">
                        设备类型：
                    </div>
                    <div class="flex-1">
                        {{
                            getName(deviceTypeList, detailInfo?.deviceType)
                                ?.label
                        }}
                    </div>
                </div>
                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-18">
                        设备编号：
                    </div>
                    <div class="flex-1">{{ detailInfo?.deviceSn }}</div>
                </div>
                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-18">
                        告警时间：
                    </div>
                    <div class="flex-1">{{ detailInfo?.alarmTime }}</div>
                </div>
                <template
                    v-if="
                        detailInfo?.alarmStatus === 3 ||
                        detailInfo?.alarmStatus === 2
                    "
                >
                    <div class="list flex items-start">
                        <div class="text-secondar-text dark:text-60-dark w-18">
                            处理操作：
                        </div>
                        <div class="flex-1">
                            {{
                                disposeActions[detailInfo?.disposeAction] || '-'
                            }}
                        </div>
                    </div>
                    <div class="list flex items-start">
                        <div class="text-secondar-text dark:text-60-dark w-18">
                            处理人：
                        </div>
                        <div class="flex-1">
                            {{
                                detailInfo?.disposeStaffName
                                    ? detailInfo?.disposeStaffName
                                    : detailInfo?.disposeAction == 1
                                    ? '系统操作'
                                    : '-'
                            }}
                        </div>
                    </div>
                    <div class="list flex items-start">
                        <div class="text-secondar-text dark:text-60-dark w-18">
                            处理时间：
                        </div>
                        <div class="flex-1">
                            {{ detailInfo?.disposeTime || '-' }}
                        </div>
                    </div>
                </template>
            </div>
        </el-drawer>
    </div>
</template>

<script setup>
import {
    onMounted,
    ref,
    computed,
    watch,
    reactive,
    onBeforeMount,
    nextTick,
} from 'vue'
import {
    getPieOption,
    alarmStatusList,
    alarmLevelList,
    chartsColors,
    DateOptionsMap,
    processDurations,
    orderTypes,
    deviceTypeList,
    alarmStatus,
    disposeActions,
} from '@/views/device/const'
import Search from '@/components/search/index.vue'
import Dictionary from '@/components/table/dictionary.vue'
import apiService from '@/apiService/device'
import api from '@/apiService/strategy'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import { usePagenation } from '@/common/setup'
import store from '@/store'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const route = useRoute()
const token = store.getters['user/getNewToken']
const isOperator = computed(() => {
    return store.state.user.userInfoData.roles.includes('operation_staff')
})
const props = defineProps({
    stationType: {
        type: String,
        default: 'energy_storage_cabinet',
    },
})
const emits = defineEmits(['lookWorkOrder'])
const tableLoading = ref(false)
const formatData = (data, id, name) => {
    const chartDischargeDom = document.getElementById(id)
    chartDischargeDom && echarts.dispose(chartDischargeDom)
    echarts.init(chartDischargeDom).setOption(getPieOption(name, data))
}
const chartData = ref({})

const getName = (arr, val) => {
    return arr.find((item) => item.value == val)
}
const getAlarmChartData = async (params) => {
    const { data } = await apiService.PostDeviceAlarmStatisticalSummary(params)

    // 辅助函数，创建图表数据
    const createChartItem = (name, value, color) => ({
        name: `${name}`,
        legendName: name,
        value,
        itemStyle: {
            color: color,
            borderColor: '#fff',
            borderWidth: 2,
        },
    })

    const alarmStatusQuantity = data.data.alarmStatusQuantity
    chartData.value.chart1Data = [
        createChartItem(
            '待处理',
            alarmStatusQuantity?.unDisposedQuantity || 0,
            alarmStatusList[0].color
        ),
        createChartItem(
            '已恢复',
            alarmStatusQuantity?.recoverQuantity || 0,
            alarmStatusList[1].color
        ),
        createChartItem(
            '已忽略',
            alarmStatusQuantity?.clearQuantity || 0,
            alarmStatusList[2].color
        ),
        createChartItem(
            '已报障',
            alarmStatusQuantity?.reportedQuantity || 0,
            alarmStatusList[3].color
        ),
    ]

    const alarmLevelQuantity = data.data.alarmLevelQuantity
    chartData.value.chart2Data = [
        createChartItem(
            '紧急',
            alarmLevelQuantity?.urgencyQuantity || 0,
            alarmLevelList[2].color
        ),
        createChartItem(
            '重要',
            alarmLevelQuantity?.importantQuantity || 0,
            alarmLevelList[1].color
        ),
        createChartItem(
            '次要',
            alarmLevelQuantity?.minorQuantity || 0,
            alarmLevelList[0].color
        ),
    ]
    // 是大大
    // 处理 chart3Data
    const deviceTypeQuantity = data.data.deviceTypeQuantity
    chartData.value.chart3Data =
        deviceTypeQuantity?.length &&
        deviceTypeQuantity.map((item, index) =>
            createChartItem(
                getName(deviceTypeList, item.deviceType).label,
                item.alarmQuantity,
                chartsColors[index].color
            )
        )
    // 异步渲染图表
    nextTick(() => {
        formatData(chartData.value.chart1Data, 'echarts-1', '告警状态')
        formatData(chartData.value.chart2Data, 'echarts-2', '告警级别')
        formatData(chartData.value.chart3Data, 'echarts-3', '设备类型')
    })
}
const options = deviceTypeList.slice(0, deviceTypeList.length - 1)
const defaultRangeDate = [
    dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD'),
]
const options1 = computed(() => {
    return {
        stationNo: {
            name: '站点名称',
            ELType: 'el-select-v2',
            placeholder: '站点名称',
            options: computed(() => {
                return stations.value
            }),
            optionFilterProp: 'label',
        },
    }
})

const searchData = reactive({
    fields: {
        rangeDate: {
            ELType: 'el-date-picker',
            value: route.path == '/operation' ? defaultRangeDate : [],
            valueFormat: 'YYYY-MM-DD',
            width: '240px',
            // separator: '—',
            icon: 'iconfont icon-a-1',
            allowClear: true,
        },
        alarmStatus: {
            ELType: 'el-select',
            options: [{ value: '', label: '全部状态' }, ...alarmStatusList],
            placeholder: '告警状态',
            value: 0,
        },
        deviceType: {
            ELType: 'el-select',
            options: [{ value: '', label: '全部类型' }, ...options],
            placeholder: '设备类型',
            // allowClear: true,
        },
        alarmLevel: {
            ELType: 'el-select',
            placeholder: '告警级别',
            options: [{ value: '', label: '全部级别' }, ...alarmLevelList],
            // allowClear: true,
        },
    },
    filterData: {
        value: {
            // filterValue: route?.query?.stationName
            //     ? route.query.stationName
            //     : '',
            filterBy: 'stationNo',
        },
        options: options1.value,
        fnChangeFilterBy: async (val) => {},
        config: {
            filterByWidth: '120px',
        },
    },
})

const tableData = ref([])
const columns = [
    {
        title: '告警名称',
        dataIndex: 'alarmDesc',
        key: 'alarmDesc',
        slots: {
            customRender: 'alarmDesc',
        },
        width: 330,
    },
    {
        title: '站点名称',
        dataIndex: 'stationName',
        key: 'stationName',
        width: 240,
    },
    {
        title: '机柜编号',
        dataIndex: 'containerNo',
        key: 'containerNo',
        width: 160,
        align: 'center',
    },
    {
        title: '告警状态',
        dataIndex: 'alarmStatus',
        key: 'alarmStatus',
        slots: {
            customRender: 'alarmStatus',
        },
        width: 160,
        align: 'center',
    },
    {
        title: '设备类型',
        dataIndex: 'deviceType',
        key: 'deviceType',
        slots: {
            customRender: 'deviceType',
        },
        width: 160,
        align: 'center',
    },
    {
        title: '告警级别',
        dataIndex: 'alarmLevel',
        key: 'alarmLevel',
        slots: {
            customRender: 'alarmLevel',
        },
        align: 'center',
    },
    {
        title: '告警时间',
        dataIndex: 'alarmTime',
        key: 'alarmTime',
        slots: {
            customRender: 'alarmTime',
        },
        align: 'right',
    },
]

const getAlarmTableData = async (params) => {
    //
    let res = await apiService.PostDeviceAlarmPage({
        ...params,
        ...pageParam.value,
        stationType: props.stationType,
    })
    tableData.value = res.data.data.records
    paginationProps.value.total = res.data.data.total
}

const getParams = () => {
    let params = {}
    for (const key in searchData.fields) {
        params[key] = searchData.fields[key].value
    }
    if (searchData.fields.rangeDate.value) {
        params.startDate = searchData.fields.rangeDate.value[0]
        params.endDate = searchData.fields.rangeDate.value[1]
    }
    params.rangeDate = undefined
    // //单个搜索信息
    params[searchData.filterData.value.filterBy] =
        searchData.filterData.value.filterValue
    params.stationType = props.stationType
    return params
}
const { paginationProps, changePage, onTableChange, pageParam } = usePagenation(
    () => {
        getTableData()
    }
)
const { refresh } = usePagenation(() => {
    paginationProps.value.current = 1
    getData()
})
const getTableData = async () => {
    tableLoading.value = true
    let params = getParams()
    await getAlarmTableData(params)
    tableLoading.value = false
}
const getData = async () => {
    tableLoading.value = true
    let params = getParams()
    await getAlarmChartData(params)
    await getAlarmTableData(params)
    tableLoading.value = false
}

const disposeStaffs = ref([])
const getUserNames = async () => {
    let res = await apiService.getStaffByRole({ roleId: 3 })
    let resultData = res.data.data.map((item) => ({
        label: item.name || '',
        value: item.id,
    }))
    disposeStaffs.value = resultData
}
// ⬇️ 告警详情
const detailVisible = ref(false)
const detailInfo = ref({})
const onCloseDetail = () => {
    detailVisible.value = false
}
const getDetail = async (id) => {
    //
    detailVisible.value = true
    let { data } = await apiService.GetDeviceAlarmDetail({
        alarmId: id,
    })
    detailInfo.value = data.data
}
const Rowclick = (record) => {
    return {
        onClick: async (event) => {
            // 点击行
            await getDetail(record.id)
        },
    }
}

const ignoreAlarm = async () => {
    // 忽略告警
    let { data } = await apiService.PostDeviceAlarmClearAlarm({
        alarmId: detailInfo.value.id,
    })
    if (data.data) {
        ElMessage.success('忽略成功')
        // onCloseDetail()
        await refresh()
    }
}
const ConvertToWorkOrder = async () => {
    // 转为工单
    let { data } = await apiService.alarmTransfer({
        alarmId: detailInfo.value.id,
    })
    if (data.data) {
        ElMessage.success('转换成功')
        onCloseDetail()
        await refresh()
    } else {
        ElMessage.error('转换失败')
    }
}
const onLookWorkOrder = () => {
    console.log('[ 1 ] >', 1)
    emits('lookWorkOrder', detailInfo.value.workOrderId)
}

// ⬆️  新建工单
// ⬇️ 工单详情

// ⬆️ 工单详情

const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])
const orgId = ref(
    getCompanyInfo?.value?.orgId ? getCompanyInfo.value.orgId : void 0
)
const stations = ref([])
const activeSystem = computed(() => localStorage.getItem('activeSystem'))
const getStations = async () => {
    const page = {
        orgId: orgId.value,
        stationType:
            activeSystem.value == 'car'
                ? 'vehicle_battery'
                : 'energy_storage_cabinet',
    }
    const res = await api.getOrgAndSubOrgStationNameList(page)
    stations.value = res.data.data.map((item) => ({
        label: item.stationName,
        value: item.stationNo,
    }))
}
onBeforeMount(async () => {
    await getUserNames()
    await getStations()
})
onMounted(async () => {
    await getData()
})
</script>

<style lang="less" scoped>
.charts-box {
    padding: 32px 30px;
    // padding-right: 80px;
    border-radius: 8px;
    background: var(--bg-f5);
    border: 2px solid var(--border);
}

@media screen and (max-width: 1560px) {
    .charts-box {
        padding-right: 32px;
    }
}

.ecahrts-dom {
    width: 200px;
    height: 200px;
    margin-right: 32px;

    // width: 33.33%;
    &:last-child {
        margin-right: 0;
    }
}

:deep(.ant-table-row) {
    cursor: pointer;
}

.list + .list {
    margin-top: 8px;
}

.item + .item {
    margin-top: 8px;
}

:deep(.el-upload--picture-card) {
    --el-upload-picture-card-size: 100px;
}

:deep(.el-upload-list--picture-card) {
    --el-upload-list-picture-card-size: 100px;
}

.hide /deep/ .el-upload--picture-card {
    display: none;
}

:deep(.el-form-item__label) {
    padding-right: 0;
}

:deep(.el-form-item) {
    // margin-bottom: 16px;
}
</style>
