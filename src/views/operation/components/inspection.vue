<template>
    <div
        class="overflow-x-auto overflow-y-auto relative tables"
        :class="{ leftFixed: leftFixed }"
        ref="tableRef"
        v-loading="tableLoading"
    >
        <div class="inspection-header text-secondar-text dark:text-60-dark">
            <div class="data info">设备基本信息</div>
            <div class="data status">巡检状态</div>
            <div class="data data1">上次巡检时间</div>
            <div class="data data2">充放电状态</div>
            <div class="data data3">SOC</div>
            <div class="data data4">SOH</div>
            <div class="data data5">单体最高SOC</div>
            <div class="data data6">单体最低SOC</div>
            <div class="data data7">单体最高电压</div>
            <div class="data data8">单体最低电压</div>
            <div class="data data9">单体最高温度</div>
            <div class="data data10">单体最低温度</div>
            <div class="data data11">A相电压</div>
            <div class="data data12">B相电压</div>
            <div class="data data13">C相电压</div>
            <div class="data data14">IGBT温度</div>
            <div class="data data15">除湿机环境湿度</div>
            <div class="data data16">液冷机进水温度</div>
            <div class="data data17">液冷机出水温度</div>
            <div class="data data18">液冷机环境温度</div>
            <div class="data data19">液冷机进水压力</div>
            <div class="data data20">液冷机出水压力</div>
        </div>
        <div v-if="tableData.length">
            <div
                v-for="(item, index) in tableData"
                :key="index"
                class="inspection-box"
            >
                <div class="inspection-table">
                    <div>
                        <div
                            class="inspection-title text-title dark:text-title-dark"
                        >
                            <span> {{ item?.stationName }}</span>
                            <span>
                                站点编号：
                                <span
                                    @click="goDevice(item)"
                                    class="cursor-pointer a"
                                    >{{ item?.stationNo }}</span
                                ></span
                            >
                            <span
                                >投运日期：{{
                                    item?.operationDate
                                        ? moment(item?.operationDate).format(
                                              'YYYY/MM/DD'
                                          )
                                        : '-'
                                }}</span
                            >
                        </div>
                    </div>

                    <div class="inspection-table-content">
                        <div
                            class="inspection-content cursor-pointer text-primary-text dark:text-80-dark"
                            v-for="(ite, ind) in item?.inspectionDevices"
                            :key="ind"
                            @click="goDetail(ite)"
                        >
                            <div class="data info">
                                <div
                                    class="text-primary-text dark:text-80-dark font-medium"
                                >
                                    {{ item?.stationName }}-{{
                                        Number(
                                            ite?.containerNo?.split('CON')[1]
                                        )
                                    }}号机柜
                                </div>
                                <div
                                    class="text-secondar-text dark:text-60-dark"
                                >
                                    ({{ ite.deviceSn }})
                                </div>
                            </div>
                            <div class="data status">
                                <dictionary
                                    :statusOptions="statusOptions"
                                    :value="ite.inspectionStatus"
                                    :color="'color'"
                                />
                            </div>
                            <div class="data data1">
                                {{
                                    ite.lastInspectionTime
                                        ? moment(ite.lastInspectionTime).format(
                                              'YYYY/MM/DD'
                                          )
                                        : '-'
                                }}
                            </div>
                            <div class="data data2">
                                {{
                                    chargeStatus[
                                        ite.emsRunDataOverview.rack
                                            ?.chargeStatus
                                    ]
                                }}
                            </div>
                            <div class="data data3">
                                {{
                                    ite.emsRunDataOverview.rack?.soc
                                        ? ite.emsRunDataOverview.rack?.soc + '%'
                                        : '-'
                                }}
                            </div>
                            <div class="data data4">
                                {{
                                    ite.emsRunDataOverview.rack?.soh
                                        ? ite.emsRunDataOverview.rack?.soh + '%'
                                        : '-'
                                }}
                            </div>
                            <div class="data data5">
                                {{
                                    ite.emsRunDataOverview.rack?.maxSoc
                                        ? ite.emsRunDataOverview.rack?.maxSoc +
                                          '%'
                                        : '-'
                                }}
                            </div>
                            <div class="data data6">
                                {{
                                    ite.emsRunDataOverview.rack?.minSoc
                                        ? ite.emsRunDataOverview.rack?.minSoc +
                                          '%'
                                        : '-'
                                }}
                            </div>
                            <div class="data data7">
                                {{
                                    ite.emsRunDataOverview.rack?.maxVoltage
                                        ? ite.emsRunDataOverview.rack
                                              ?.maxVoltage + 'V'
                                        : '-'
                                }}
                            </div>
                            <div class="data data8">
                                {{
                                    ite.emsRunDataOverview.rack?.minVoltage
                                        ? ite.emsRunDataOverview.rack
                                              ?.minVoltage + 'V'
                                        : '-'
                                }}
                            </div>
                            <div class="data data9">
                                {{
                                    ite.emsRunDataOverview.rack?.maxTemperature
                                        ? ite.emsRunDataOverview.rack
                                              ?.maxTemperature + '°C'
                                        : '-'
                                }}
                            </div>
                            <div class="data data10">
                                {{
                                    ite.emsRunDataOverview.rack?.minTemperature
                                        ? ite.emsRunDataOverview.rack
                                              ?.minTemperature + '°C'
                                        : '-'
                                }}
                            </div>
                            <div class="data data11">
                                {{
                                    ite.emsRunDataOverview.pcs?.ua
                                        ? ite.emsRunDataOverview.pcs?.ua + 'V'
                                        : '-'
                                }}
                            </div>
                            <div class="data data12">
                                {{
                                    ite.emsRunDataOverview.pcs?.ub
                                        ? ite.emsRunDataOverview.pcs?.ub + 'V'
                                        : '-'
                                }}
                            </div>
                            <div class="data data13">
                                {{
                                    ite.emsRunDataOverview.pcs?.uc
                                        ? ite.emsRunDataOverview.pcs?.uc + 'V'
                                        : '-'
                                }}
                            </div>
                            <div class="data data14">
                                {{
                                    ite.emsRunDataOverview.pcs?.igbtTemp
                                        ? ite.emsRunDataOverview.pcs?.igbtTemp +
                                          '°C'
                                        : '-'
                                }}
                            </div>
                            <div class="data data15">
                                {{
                                    ite.emsRunDataOverview.dehumidifier
                                        ?.environmentHumidity
                                        ? ite.emsRunDataOverview.dehumidifier
                                              ?.environmentHumidity + '%'
                                        : '-'
                                }}
                            </div>
                            <div class="data data16">
                                {{
                                    ite.emsRunDataOverview.refrigerator
                                        ?.waterInletTemp
                                        ? ite.emsRunDataOverview.refrigerator
                                              ?.waterInletTemp + '°C'
                                        : '-'
                                }}
                            </div>
                            <div class="data data17">
                                {{
                                    ite.emsRunDataOverview.refrigerator
                                        ?.waterOutletTemp
                                        ? ite.emsRunDataOverview.refrigerator
                                              ?.waterOutletTemp + '°C'
                                        : '-'
                                }}
                            </div>
                            <div class="data data18">
                                {{
                                    ite.emsRunDataOverview.refrigerator
                                        ?.environmentTemp
                                        ? ite.emsRunDataOverview.refrigerator
                                              ?.environmentTemp + '°C'
                                        : '-'
                                }}
                            </div>
                            <div class="data data19">
                                {{
                                    ite.emsRunDataOverview.refrigerator
                                        ?.waterInletPressure
                                        ? ite.emsRunDataOverview.refrigerator
                                              ?.waterInletPressure + 'bar'
                                        : '-'
                                }}
                            </div>
                            <div class="data data20">
                                {{
                                    ite.emsRunDataOverview.refrigerator
                                        ?.waterOutletPressure
                                        ? ite.emsRunDataOverview.refrigerator
                                              ?.waterOutletPressure + 'bar'
                                        : '-'
                                }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-20" v-else>
            <empty-data :description="'暂无巡检记录'">
                <slot name="empty"></slot>
            </empty-data>
        </div>

        <el-drawer
            v-model="historyVisible"
            :size="486"
            :show-close="false"
            :close-on-press-escape="false"
            :destroy-on-close="true"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div><span>历史巡检记录</span></div>
                    <div class="flex items-center gap-x-3">
                        <el-button plain round @click="closeDrawer">{{
                            $t('common_guanbi')
                        }}</el-button>
                        <el-button
                            plain
                            round
                            type="primary"
                            @click="openAddNew"
                            v-if="selectedIte?.inspectionStatus === 0"
                            >新增</el-button
                        >
                    </div>
                </div>
            </template>
            <div>
                <el-collapse
                    v-model="activeNames"
                    @change="handleChange"
                    accordion
                    v-if="historyRecords.length"
                >
                    <el-collapse-item
                        :name="index"
                        v-for="(item, index) in historyRecords"
                        :key="index"
                    >
                        <template #title>
                            <div
                                class="flex flex-1 items-center justify-between text-title dark:text-title-dark"
                            >
                                <div>{{ item.stationName }}</div>
                                <div>{{ item.createTime }}</div>
                            </div>
                        </template>
                        <div class="bg-f5 px-3 py-2.5 rounded-lg">
                            <div class="list">
                                <span
                                    class="text-secondar-text dark:text-60-dark"
                                    >巡检人员：</span
                                >
                                <span>{{ item.disposeStaffName }}</span>
                            </div>
                            <div class="list">
                                <span
                                    class="text-secondar-text dark:text-60-dark"
                                    >巡检结果：</span
                                >
                                <span>{{
                                    getErrorText(item.inspectionItems)
                                }}</span>
                            </div>
                            <div class="list">
                                <div
                                    class="text-secondar-text dark:text-60-dark"
                                >
                                    巡检项目：
                                </div>
                                <div
                                    class="item"
                                    v-for="(ite, ind) in item.inspectionItems"
                                    :key="ind"
                                >
                                    <div
                                        class="flex items-center justify-between leading-5.5"
                                    >
                                        <div>{{ getName(ite.item) }}</div>
                                        <div>
                                            <dictionary
                                                :statusOptions="errorOptions"
                                                :value="ite.status"
                                                :color="'color'"
                                            />
                                        </div>
                                    </div>
                                    <div
                                        v-if="ite.status == 2"
                                        class="p-3"
                                        style="
                                            background: rgba(
                                                111,
                                                190,
                                                206,
                                                0.1
                                            );
                                            border: 1px solid
                                                rgba(151, 151, 151, 0.1);
                                            color: rgba(111, 190, 206, 1);
                                        "
                                    >
                                        {{ ite.description }}
                                    </div>
                                </div>
                            </div>
                            <div class="list">
                                <div
                                    class="text-secondar-text dark:text-60-dark"
                                >
                                    补充说明
                                </div>
                                <div
                                    class="p-4 bg-ff dark:bg-ff-dark rounded-lg text-secondar-text dark:text-60-dark mt-2"
                                >
                                    {{ item.description || '-' }}
                                </div>
                            </div>
                            <div class="list flex" v-if="item.files">
                                <div
                                    class="text-secondar-text dark:text-60-dark w-20"
                                >
                                    图片说明:
                                </div>
                                <div
                                    class="flex mt-2 gap-2 flex-wrap flex-1 w-0"
                                >
                                    <div
                                        v-for="it in item.files"
                                        :key="it.fileId"
                                        class="w-25 h-25 bg-ff dark:bg-ff-dark rounded-lg"
                                    >
                                        <el-image
                                            style="width: 100%; height: 100%"
                                            :src="it.fileVisitUrl"
                                            :zoom-rate="1.2"
                                            :max-scale="7"
                                            :min-scale="0.2"
                                            :preview-src-list="[
                                                it.fileVisitUrl,
                                            ]"
                                            :initial-index="4"
                                            fit="contain"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-collapse-item>
                </el-collapse>
                <div class="h-40" v-else>
                    <empty-data description="当前暂无巡检记录"> </empty-data>
                </div>
            </div>
            <el-drawer
                v-model="addVisible"
                :size="486"
                :show-close="false"
                :destroy-on-close="true"
                :close-on-press-escape="false"
            >
                <template #header>
                    <div
                        class="drawer-header flex items-center justify-between leading-5.5"
                    >
                        <div><span>新增巡检记录</span></div>
                        <div>
                            <el-button
                                plain
                                round
                                class="mr-4"
                                @click="cancelAdd(formRef)"
                                >{{ $t('common_guanbi') }}</el-button
                            >
                            <el-button plain round type="primary" @click="onAdd"
                                >新增</el-button
                            >
                        </div>
                    </div>
                </template>
                <div>
                    <div class="text-secondar-text dark:text-60-dark mb-2">
                        巡检项目
                    </div>
                    <div class="px-3 py-2.5 rounded-lg bg-background">
                        <el-form
                            ref="formRef"
                            :model="formState"
                            label-width="auto"
                            style="max-width: 600px"
                            label-position="left"
                        >
                            <div
                                v-for="(
                                    item, index
                                ) in formState.inspectionItems"
                                :key="index"
                            >
                                <el-form-item :label="item.label">
                                    <el-checkbox-group v-model="item.status">
                                        <el-checkbox
                                            label="正常"
                                            :value="1"
                                            @change="
                                                changeStatus1($event, item)
                                            "
                                        />
                                        <el-checkbox
                                            label="异常"
                                            :value="2"
                                            @change="
                                                changeStatus2($event, item)
                                            "
                                        />
                                    </el-checkbox-group>
                                </el-form-item>
                                <div v-if="item.status == 2">
                                    <el-input
                                        type="textarea"
                                        v-model="item.description"
                                        placeholder="可以对异常情况进行说明"
                                        class="bg-ff dark:bg-ff-dark text-primary-text dark:text-80-dark rounded"
                                        style="border: 1px solid #ff3141"
                                        show-word-limit
                                        maxlength="100"
                                    ></el-input>
                                </div>
                            </div>
                        </el-form>
                    </div>
                    <div class="text-secondar-text dark:text-60-dark mt-3 mb-2">
                        补充说明：
                    </div>
                    <el-input
                        type="textarea"
                        v-model="formState.description"
                        placeholder="请输入补充说明，如果没有，可不填"
                        class="bg-ff dark:bg-ff-dark text-primary-text dark:text-80-dark rounded"
                        show-word-limit
                        maxlength="200"
                    ></el-input>
                    <div class="flex mt-3">
                        <div class="text-secondar-text dark:text-60-dark">
                            上传图片：
                        </div>
                        <div class="flex-1">
                            <el-upload
                                class="upload-demo"
                                :class="{ hide: hideUpload }"
                                :action="url + '/file/uploadFile'"
                                list-type="picture-card"
                                :on-preview="handlePreview"
                                :on-remove="handleRemove"
                                :multiple="true"
                                :limit="9"
                                :data="getUploadData"
                                :file-list="fileList"
                                :before-upload="beforeUpload"
                                @change="updateFileList"
                                :headers="{
                                    Authorization: `Bearer ${token}`,
                                }"
                            >
                                <template v-if="true">
                                    <el-icon>
                                        <Plus />
                                    </el-icon>
                                </template>
                            </el-upload>
                        </div>
                    </div>
                </div>
            </el-drawer>
        </el-drawer>
        <el-dialog v-model="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="" />
        </el-dialog>
    </div>
</template>

<script setup>
import { reactive, ref, onMounted, nextTick, computed } from 'vue'
import Dictionary from '@/components/table/dictionary.vue'
import { Plus } from '@element-plus/icons-vue'
import apiService from '@/apiService/device'
import { ElMessage } from 'element-plus'
import moment from 'moment'
import { chargeStatus } from '@/views/device/const.js'
// import Upload from '@/views/role/components/UploadM.vue'
import store from '@/store'
import { useRouter } from 'vue-router'
const router = useRouter()
const token = store.getters['user/getNewToken']

const defaultInspectionItems = [
    {
        item: 'overallSummary',
        label: '总体概况',
        status: [1],
        description: undefined,
    },
    {
        item: 'batteryCompartment',
        label: '电池舱',
        status: [1],
        description: undefined,
    },
    {
        item: 'highVoltageCabinet',
        label: '高压箱',
        status: [1],
        description: undefined,
    },
    {
        item: 'combiner',
        label: '汇流柜',
        status: [1],
        description: undefined,
    },
    {
        item: 'ems',
        label: '能量管理系统',
        status: [1],
        description: undefined,
    },

    {
        item: 'pcs',
        label: '逆变器',
        status: [1],
        description: undefined,
    },
    {
        item: 'bms',
        label: '电池系统',
        status: [1],
        description: undefined,
    },
    {
        item: 'fireProtection',
        label: '消防系统',
        status: [1],
        description: undefined,
    },
    {
        item: 'refrigerator',
        label: '液冷机',
        status: [1],
        description: undefined,
    },
]
const props = defineProps({
    tableData: {
        type: Array,
        default: () => [],
    },
    tableLoading: {
        type: Boolean,
        default: false,
    },
})
const emits = defineEmits(['update'])
const hideUpload = ref(false)
const historyVisible = ref(false)
const statusOptions = [
    {
        value: 0,
        label: '未巡检',
        color: '#FF8487',
        colorOffset: '#FF4D4F',
        backGroundColor: '#EA0C28',
    },
    {
        value: 1,
        label: '已巡检',
        color: '#93EED2',
        colorOffset: '#5AD8A6',
        backGroundColor: '#33BE4F',
    },
]
const errorOptions = [
    {
        value: 2,
        label: '异常',
        color: '#EA0C28',
        colorOffset: '#FF4D4F',
        backGroundColor: '#EA0C28',
    },
    {
        value: 1,
        label: '正常',
        color: '#33BE4F',
        colorOffset: '#5AD8A6',
        backGroundColor: '#33BE4F',
    },
]
const historyRecords = ref([])
const selectedIte = ref()
const goDetail = async (ite) => {
    selectedIte.value = ite
    let res = await apiService.getInspectionLogPage({
        current: 1,
        size: 100,
        deviceId: selectedIte.value.deviceId,
    })
    historyRecords.value = res.data.data.records
    if (historyRecords.value.length > 0) {
        activeNames.value = [0]
    }
    historyVisible.value = true
}
const goDevice = (item) => {
    console.log('[ item ] >', item)
    router.push({
        path: '/device/deviceDetail',
        query: {
            stationNo: item.stationNo,
            stationId: item.stationId,
        },
    })
}
const closeDrawer = () => {
    //
    activeNames.value = []
    historyVisible.value = false
}
const activeNames = ref()
const handleChange = () => {}
const addVisible = ref()
const formRef = ref()
const cancelAdd = () => {
    addVisible.value = false
    // formRef.value.resetFields()
    nextTick(() => {
        formState.inspectionItems = defaultInspectionItems.map((i) => {
            return {
                status: [1],
                description: undefined,
                item: i.item,
                label: i.label,
            }
        })
        formState.description = ''
        fileList.value = []
    })
}
const onAdd = async () => {
    const inspectionItems = formState.inspectionItems.map((i) => {
        return {
            item: i.item,
            status: i.status[0],
            description: i.description,
        }
    })
    let params = {
        inspectionItems,
        fileIds: fileList.value.map((item) => item.response.data.fileId),
        deviceId: selectedIte.value.deviceId,
        description: formState.description,
    }
    let res = await apiService.addInspectionLog(params)
    if (res.data.data) {
        ElMessage.success('添加成功')
        cancelAdd()
        closeDrawer()
        emits('update')
    }
}

const openAddNew = () => {
    //
    addVisible.value = true
}

const formState = reactive({
    inspectionItems: defaultInspectionItems,
    description: undefined,
})
const changeStatus1 = (e, item) => {
    item.status = [1]
}

const changeStatus2 = (e, item) => {
    item.status = [2]
}
const files = ref([])
const delImgFile = () => {}
const success = (info) => {}

const url =
    process.env.NODE_ENV == 'production'
        ? 'https://ems-api.ssnj.com'
        : 'https://ems-api-beta.ssnj.com'

const fileList = ref([])
const dialogImageUrl = ref('')
const dialogVisible = ref(false)

const handlePreview = (file) => {
    dialogImageUrl.value = file.url
    dialogVisible.value = true
}

const handleRemove = (file, flist) => {
    fileList.value = flist
    hideUpload.value = flist.length >= 9
}

const beforeUpload = (file) => {
    let isJpgOrPng = ''
    const isLt1M = file.size / 1024 / 1024 <= 1
    if (!isLt1M) {
        // message.error('图片大小不能超过1M')
    }
    return isJpgOrPng && isLt1M
}

const getUploadData = () => {
    return {
        scene: 'inspectionLog',
        // 其他需要传递的数据
    }
}

const updateFileList = (newFileList, a) => {
    hideUpload.value = a.length >= 9
    fileList.value = a
}
// mj
const options = {
    overallSummary: '总体概览',
    batteryCompartment: '电池仓',
    highVoltageCabinet: '高压箱',
    combiner: '汇流柜',
    ems: '能量管理系统',
    pcs: '逆变器',
    bms: '电池系统',
    fireProtection: '消防系统',
    refrigerator: '液冷机',
}
const getName = (val) => {
    return options[val]
}
const getErrorText = (inspectionItems) => {
    if (inspectionItems.every((i) => i.status == 2)) {
        return '全部异常'
    }
    if (inspectionItems.some((i) => i.status == 2)) {
        return '部分异常'
    }
    return '正常'
}

const tableRef = ref(null)
const leftFixed = ref(false)
onMounted(() => {
    tableRef.value.addEventListener('scroll', (e) => {
        if (e.target.scrollLeft > 20) {
            leftFixed.value = true
        } else {
            leftFixed.value = false
        }
    })
})
</script>

<style lang="less" scoped>
.tables {
    height: calc(~'100vh - 317px');

    &.leftFixed {
        .info {
            box-shadow: 10px 0 10px -10px var(--border);
        }
    }
}

/* 隐藏滚动条 */
// .tables::-webkit-scrollbar {
// width: 0;
// height: 8px; /* 横向滚动条的高度 */
// }
.inspection-header {
    padding-top: 12px;
    padding-bottom: 12px;
    padding-right: 24px;
    padding-left: 12px;
    white-space: nowrap;
    background: var(--car-pie-border);
    zoom: 1;
    width: max-content;
    position: -webkit-sticky;
    position: sticky;
    top: 0px;
    z-index: 10;
    border-bottom: 1px solid var(--border);
}

.inspection-box {
    & + .inspection-box {
        margin-top: 16px;
    }
}

.inspection-table {
    background: var(--bg-f5);
    padding: 16px 12px;
    border: 1px solid var(--border);
    width: max-content;

    &:first-child {
        border-top: none;
    }
}

.inspection-title {
    display: inline-flex;
    column-gap: 16px;
    margin-bottom: 12px;
    padding: 0 12px;
    position: sticky;
    left: 0;
}

.inspection-table-content {
    padding-right: 12px;
    background: var(--input-bg);
    white-space: nowrap;
    width: max-content;
}

.inspection-content {
    padding: 12px 0;

    & + .inspection-content {
        border-top: 1px solid var(--border);
    }
}

.data {
    min-width: 100px;
    display: inline-block;
    text-align: center;
    vertical-align: middle;
}

.info {
    width: 280px;
    position: sticky;
    left: 0;
    padding-left: 12px;
    text-align: left;
    // backdrop-filter: blur(2px);
    background: #fff;
}

.status {
    width: 80px;
    text-align: left;
}

.data1 {
    width: 160px;
}

.data3 {
    min-width: 80px;
}

.data4 {
    min-width: 80px;
}

.data5,
.data6,
.data7,
.data8,
.data9,
.data10 {
    width: 120px;
}

.data15,
.data16,
.data17,
.data18,
.data19,
.data20 {
    width: 120px;
}

.list + .list {
    margin-top: 8px;
}

.item {
    margin-top: 8px;
}

:deep(.el-collapse-item__content) {
    padding-bottom: 1px;
}

:deep(.el-collapse-item__wrap) {
    border-bottom: 1px solid transparent;
}

:deep(.el-collapse) {
    border-top: 1px solid transparent;
}

:deep(.el-form-item) {
    justify-content: space-between;
    margin-bottom: 0;

    .el-form-item__content {
        flex: initial;
        margin-left: auto;
    }

    .el-radio {
        margin-right: 12px;
    }

    .el-radio__label {
        padding-left: 4px;
    }
}

:deep(.el-upload--picture-card) {
    --el-upload-picture-card-size: 100px;
}

:deep(.el-upload-list--picture-card) {
    --el-upload-list-picture-card-size: 100px;
}

.hide /deep/ .el-upload--picture-card {
    display: none;
}
.a {
    color: var(--themeColor);
}
:deep(.el-drawer__header) {
    background: var(--bg-f5);
    // backdrop-filter: blur(10px);
}
:deep(.el-drawer__body) {
    background: var(--bg-f5);
    // backdrop-filter: blur(10px);
}
.dark {
    :deep(.el-drawer__header) {
        background: #5d626c;
        // backdrop-filter: blur(10px);
    }
    :deep(.el-drawer__body) {
        background: #5d626c;
        // backdrop-filter: blur(10px);
    }
}
</style>
