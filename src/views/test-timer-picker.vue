<template>
    <div style="padding: 20px">
        <h2>日期时间选择器测试</h2>

        <div style="margin-bottom: 20px">
            <h3>普通日期选择器 (isTimerPicker: false)</h3>
            <dateSearch
                :info="{
                    periodOptions: ['day', 'hour'],
                    defaultDayRangeLen: 7,
                    dayRangeLen: 30,
                }"
                :isTimerPicker="false"
                @onChange="onDateChange1"
            />
            <p>选择的日期: {{ JSON.stringify(dateResult1) }}</p>
        </div>

        <div style="margin-bottom: 20px">
            <h3>日期时间选择器 (isTimerPicker: true)</h3>
            <dateSearch
                :info="{
                    periodOptions: ['day', 'hour'],
                    defaultDayRangeLen: 7,
                    dayRangeLen: 30,
                }"
                :isTimerPicker="true"
                @onChange="onDateChange2"
            />
            <p>选择的日期时间: {{ JSON.stringify(dateResult2) }}</p>
        </div>

        <div style="margin-bottom: 20px">
            <h3>对比说明</h3>
            <ul>
                <li>普通模式: type="date" / "daterange", 格式 YYYY-MM-DD</li>
                <li>
                    时间模式: type="datetime" / "datetimerange", 格式 YYYY-MM-DD
                    HH:mm:ss
                </li>
            </ul>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import dateSearch from '@/components/dateSearch.vue'

const dateResult1 = ref({})
const dateResult2 = ref({})

const onDateChange1 = (params) => {
    dateResult1.value = params
    console.log('普通日期选择器结果:', params)
}

const onDateChange2 = (params) => {
    dateResult2.value = params
    console.log('日期时间选择器结果:', params)
}
</script>

<style scoped>
h2,
h3 {
    color: #333;
}
p {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
}
ul {
    background: #e8f4fd;
    padding: 15px;
    border-radius: 4px;
}
</style>
