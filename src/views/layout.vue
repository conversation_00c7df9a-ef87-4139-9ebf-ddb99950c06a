<template>
    <div class="w-full fixed left-0 top-0 z-50" style="z-index: 99">
        <!--  v-if="route.meta.isFullPage" -->
        <map-header
            :isFullPage="route.meta.isFullPage"
            @onChangeTheme="onChangeTheme"
        />
    </div>
    <div class="relative layout">
        <div
            class="w-full h-12 absolute"
            style="background: rgba(253, 117, 11, 0.1); top: 78px; z-index: 90"
            v-if="showTips"
        >
            <div class="content-area">
                <div class="leading-12 relative">
                    <div
                        class="flex items-center justify-center leading-12 pl-32"
                    >
                        <iconSvg name="warnning" class="w-5 h-5 mr-3" />
                        <div class="mr-2" style="color: #fd750b">
                            {{ $t('changePwd01') }}
                        </div>
                        <div class="mr-2 text-secondar-text dark:text-60-dark">
                            {{ $t('changePwd02') }}
                        </div>
                        <el-button
                            plain
                            type="primary"
                            round
                            @click="onEditPassword"
                            >{{ $t('lijiqianwang') }}</el-button
                        >
                    </div>
                    <div
                        class="h-12 flex items-center absolute right-0 top-0 cursor-pointer"
                        @click="closeTips"
                    >
                        <iconSvg name="close1" class="w-5 h-5 mr-3 closeIcon" />
                    </div>
                </div>
            </div>
        </div>

        <a-layout :class="getContentClass">
            <a-layout-content
                :style="{
                    margin: 0,
                    'margin-bottom': '0',
                }"
            >
                <el-config-provider :locale="configLang">
                    <router-view v-slot="{ Component }">
                        <!-- 没有下面这行代码，就会导致页面切换后不能点击，很奇怪？？？？？ -->
                        <!-- <div style="height: 0; font-size: 0">
                            {{ keepAliveList }}
                            {{ route.meta.keepAlive }}
                        </div> -->
                        <keep-alive>
                            <component
                                :is="Component"
                                :key="route.name"
                                v-if="route.meta.keepAlive"
                            />
                        </keep-alive>
                        <component
                            :is="Component"
                            v-if="!route.meta.keepAlive"
                        />
                    </router-view>
                </el-config-provider>
            </a-layout-content>
        </a-layout>
    </div>
</template>
<script>
import {
    defineComponent,
    reactive,
    ref,
    watch,
    toRefs,
    onMounted,
    onBeforeMount,
    computed,
} from 'vue'
import { routes } from '@/router'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import service from '@/apiService'
import dayjs from 'dayjs'
import MapHeader from '@/components/homeHeader.vue'
import initWebSocket from '@/common/websocket'
import zhCN from 'element-plus/dist/locale/zh-cn.mjs'
import enUS from 'element-plus/dist/locale/en.mjs'
import Cookies from 'js-cookie'
import { useI18n } from 'vue-i18n'
export default defineComponent({
    components: { MapHeader },
    setup() {
        const { locale } = useI18n()
        const store = useStore()
        const route = useRoute(),
            router = useRouter(),
            selectedKeys = ref(['/'])
        const goto = ({ item, key, keyPath }) => {
            router.push(`/${key}`)
        }
        const state = reactive({
            path: '',
            collapsed: false,
            openKeys: ['order', 'goods', 'setting', 'customer'],
            preOpenKeys: ['order', 'goods', 'setting', 'customer'],
        })
        const toggleCollapsed = () => {
            state.collapsed = !state.collapsed
        }
        watch(
            () => route.path,
            (val) => {
                selectedKeys.value = [val.replace('/', '')]
                state.path =
                    selectedKeys.value[0].indexOf('/') > 0
                        ? selectedKeys.value[0].slice(
                              0,
                              selectedKeys.value[0].indexOf('/')
                          )
                        : selectedKeys.value[0]
            },
            { immediate: true }
        )
        watch(
            () => state.openKeys,
            (_val, oldVal) => {
                state.preOpenKeys = oldVal
            }
        )
        const getConfigLang = (lang) => {
            console.log('[ lang ] >', lang)
            if (lang == 'zh') {
                return zhCN
            } else if (lang == 'en') {
                return enUS
            } else {
                return zhCN
            }
        }
        const configLang = ref(zhCN)
        onBeforeMount(async () => {
            locale.value = localStorage.getItem('language') || 'zh'
            configLang.value = getConfigLang(locale.value)
        })
        const keepAliveList = computed(() => {
            return ['device']
        })

        const staffRoleCodesLength = computed(() => {
            return store.state.user.staffRoleCodes?.length
        })
        const getContentClass = computed(() => {
            // 判断当前路由是否是全屏宽度的页面，根据需要进行修改
            const isFullPage = route.meta.isFullPage
            return {
                'content-area': !isFullPage, // 1200px 宽度的样式
                'w-full': isFullPage, // 全屏宽度的样式
            }
        })

        const showTips = computed(() => {
            return (
                !(Cookies.get('modifyPasswordFlag') != 0) &&
                !store.state.user.hidetip &&
                !Cookies.get('hideOnce')
            )
        })
        const onEditPassword = () => {
            //
            store.commit('user/setHidetip', true)
            Cookies.set('hideOnce', 1, {
                expires: 7,
                path: '/',
                domain: 'ssnj.com',
            })
            router.push({
                name: 'user',
                params: { openPws: 1 },
            })
        }
        const closeTips = () => {
            //
            store.commit('user/setHidetip', true)
            Cookies.set('hideOnce', 1, {
                expires: 7,
                path: '/',
                domain: 'ssnj.com',
            })
        }
        const onChangeTheme = () => {
            // 切换地图样式
            console.log('[ 切换地图样式 ] >')
            console.log(localStorage.getItem('cn-sys-theme'))
        }
        return {
            selectedKeys,
            routes,
            goto,
            ...toRefs(state),
            toggleCollapsed,
            keepAliveList,
            route,
            staffRoleCodesLength,
            getContentClass,
            zhCN,
            enUS,
            showTips,
            onEditPassword,
            closeTips,
            configLang,
            onChangeTheme,
        }
    },
})
</script>
<style lang="less" scoped>
.parent-menu {
    font-size: 14px;
}

.menu {
    &.iconfont {
        font-size: 24px;
        margin-right: 6px;
    }
}

:deep(.ant-menu-inline .ant-menu-item::after) {
    border: none;
}

:deep(.ant-menu-item-icon) {
    display: inline-block;
    height: 40px;
    line-height: 40px;
    vertical-align: top;
}

:deep(.ant-menu-title-content) {
    display: inline-block;
    height: 40px;
    vertical-align: top;
    line-height: 40px;
    font-size: 14px;
}

:deep(.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-active) {
    background-color: #fff;
}

:deep(
        .ant-menu-item .ant-menu-item-icon + span,
        .ant-menu-submenu-title .ant-menu-item-icon + span,
        .ant-menu-item .anticon + span,
        .ant-menu-submenu-title .anticon + span
    ) {
    margin-left: -2px;
}

:deep(.ant-menu-submenu-title .ant-menu-item-icon + span) {
    margin-left: -2px;
}

.menubox {
    position: relative;

    .toggleCollapsedBtn {
        position: absolute;
        right: 12px;
        top: 9px;
        z-index: 3;

        span {
            font-size: 20px;
        }
    }
}

.ant-menu.ant-menu-inline-collapsed {
    width: 80px !important;
}

:deep(.ant-menu.ant-menu-inline-collapsed > .ant-menu-item) {
    padding: 0 12px;
}

:deep(
        .ant-menu.ant-menu-inline-collapsed
            > .ant-menu-submenu
            > .ant-menu-submenu-title
    ) {
    padding: 0 12px;
}

:deep(.ant-menu-item .ant-menu-item-icon + span) {
    transition: opacity 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), color 0.3s;
}

:deep(.ant-menu-inline.ant-menu-root .ant-menu-item) {
    display: block;
}

:deep(.ant-menu-inline.ant-menu-root .ant-menu-submenu-title) {
    display: block;
}

:deep(.ant-layout-sider) {
    z-index: 9;
}

:deep(.ant-menu-sub.ant-menu-inline) {
    background: #f5f5f5;
}

:deep(.ant-menu-sub.ant-menu-inline > .ant-menu-item, ) {
    padding-left: 40px !important;
}
.layout {
    // background: var(--main-bg);
    padding-bottom: 24px;
    min-height: 100vh;
}
.closeIcon {
    color: var(--text-80);
}
</style>
