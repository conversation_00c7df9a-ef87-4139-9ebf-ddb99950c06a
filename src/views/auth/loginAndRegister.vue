<template>
    <div
        class="video-box"
        :style="{ 'background-image': 'url(' + bgImg.loginBanner + ')' }"
    >
        <div class="w-full h-full flex items-center justify-center content-box">
            <div class="flex-1 pt-15" style="height: 540px">
                <div>
                    <img
                        class="logo"
                        :src="loginImg?.orgLogo"
                        alt=""
                        @click="goHome"
                    />
                </div>
                <div class="name-1" :class="locale">
                    {{ $t('Battery Health Management Platform') }}
                </div>
                <div class="text-1" :class="locale">
                    {{
                        $t(
                            'Empower the full lifecycle health management of batteries with AI to ensure operational safety and extend system lifespan '
                        )
                    }}
                </div>
            </div>
            <div
                class="flex flex-col justify-between items-center box p-8 pl-0"
            >
                <!-- w-full login-box flex justify-end -->
                <div class="loginIn">
                    <div>
                        <div class="bg-ff dark:bg-ff-dark login-content">
                            <!-- <div class="login-title">登录后台</div> -->
                            <router-view></router-view>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { reactive, toRefs, computed } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
export default {
    setup() {
        // const state = reactive({})
        // return { ...toRefs(state) }
        const hostname = window.location.hostname
        const { t, locale } = useI18n()
        const state = useStore()
        const bgImg = computed(() => {
            if (
                state.getters['user/getConfigData'] &&
                state.getters['user/getConfigData'].loginBanner
            ) {
                console.log(hostname)
                //
                if (
                    hostname == 'ems.ssnj.com' ||
                    hostname == 'ems-beta.ssnj.com' ||
                    hostname == 'localhost' ||
                    hostname == 'faw.ssnj.com'
                ) {
                    return {
                        loginBanner: require('@/assets/login/logobg_simple.jpg'),
                    }
                } else {
                    return state.getters['user/getConfigData']
                }
            } else {
                return {
                    loginBanner: require('@/assets/login/logobg_simple.jpg'),
                }
            }
        })

        const loginImg = computed(() => {
            return state.getters['user/getConfigData']
        })
        return {
            bgImg,
            loginImg,
            locale,
        }
    },
}
</script>
<style lang="less" scoped>
.video-box {
    position: relative;
    height: 100vh;
    /*进行视频裁剪*/
    overflow: hidden;
    min-width: 1400px;
    min-height: 760px;
    // background-image: url('../../assets/login/login-wanjiale.jpg');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    z-index: 2;
}
.content-box {
    width: 1440px;
    max-width: calc(100% - 120px);
    min-width: 1200px;
    margin: 0 auto;
    font-weight: normal;
    column-gap: 76px;
    .logo {
        width: 234px;
        margin-bottom: 12px;
    }
    .name-1 {
        font-size: 64px;
        color: #595758;
        line-height: 100px;
        text-align: left;
        font-style: normal;
        &.en {
            font-size: 49px;
            line-height: 100px;
        }
    }
    .text-1 {
        font-size: 28px;
        color: #595758;
        line-height: 40px;
        text-align: left;
        font-style: normal;
        &.en {
            font-size: 24px;
        }
    }
}
.loginIn {
    // top: 17%;
    // right: 12%;
    background: #ffffff;
    box-shadow: 0px 0px 20px 0px rgba(131, 176, 193, 0.4);
    z-index: 10;
    .login-content {
        width: 480px;
        height: 540px;
        padding: 38px 42px;
    }
}

.login-title {
    height: 22px;
    font-size: 16px;

    color: rgba(34, 34, 34, 0.65);
    line-height: 22px;
}

.video-box .video-background {
    position: absolute;
    left: 50%;
    top: 50%;
    /*保证视频内容始终居中*/
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    /*保证视频充满屏幕*/
    object-fit: cover;
    min-height: 800px;
}

// .box {
//   background: url("../../assets/login/ssnj.jpg") no-repeat center / cover;
// }
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 88px;
    line-height: 88px;

    // width: 100%;
    .menu {
        // width: 1200px;
        position: relative;
        height: 100%;
    }
}

.login-box {
    width: 1440px;
    // height: calc(~"100vh - 160px");
    // height: 540px;
    margin: auto;
    // overflow: hidden;
    border-radius: 8px;
}
@media screen and (max-width: 1560px) {
    .content-box {
        column-gap: 40px;
    }
}
@media screen and (max-width: 1440px) {
    .content-box {
        .name-1 {
            font-size: 56px;
            line-height: 80px;
            &.en {
                font-size: 42px;
                line-height: 80px;
            }
        }
        .text-1 {
            font-size: 24px;
            line-height: 40px;
            &.en {
                font-size: 22px;
            }
        }
    }
}
</style>
