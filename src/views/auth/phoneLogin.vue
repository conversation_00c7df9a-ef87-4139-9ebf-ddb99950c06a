<template>
    <div class="tabs relative">
        <div class="absolute right-0 top-0 leading-10 flex">
            <div
                class="lang select-none leading-10 h-10 px-2 cursor-pointer z-1000"
                :class="locale === 'zh' ? 'langActive' : ''"
                @click="changeLanguage('zh')"
            >
                CN
            </div>
            <div>/</div>
            <div
                class="lang select-none leading-10 h-10 px-2 cursor-pointer z-1000"
                :class="locale === 'en' ? 'langActive' : ''"
                @click="changeLanguage('en')"
            >
                EN
            </div>
        </div>
        <el-tabs v-model="activeName" @tab-change="handleTabChange">
            <template #default>
                <el-tab-pane
                    :label="$t('Verification code login')"
                    name="smscode"
                >
                    <div class="table-tabs">
                        <a-form
                            :model="formState"
                            ref="formRef"
                            :rules="rules"
                            class="mw-form"
                        >
                            <a-form-item name="phone" :validateFirst="true">
                                <a-input
                                    size="large"
                                    autocomplete="off"
                                    v-model:value="formState.phone"
                                    :placeholder="$t('phone')"
                                    @keyup.enter="onSubmit"
                                    class="input-color"
                                >
                                    <template #prefix
                                        ><span
                                            class="text-third-text inline-block px-2 rounded-full pre-tag"
                                            >+86</span
                                        >
                                    </template>
                                    <!-- <template #prefix> -->
                                    <!-- <i class="iconfont icon-ej-10"></i> -->
                                    <!-- </template> -->
                                </a-input>
                            </a-form-item>
                            <a-form-item name="smsCode" :validateFirst="true">
                                <sms-code
                                    :phone="formState.phone"
                                    v-model:value="formState.smsCode"
                                    @onChangeCode="onChangeCode"
                                    @keyup.enter="onSubmit"
                                    smsVerifyType="login"
                                    size="large"
                                >
                                    <!-- <i class="iconfont icon-ej-11"></i> -->
                                </sms-code>
                            </a-form-item>
                        </a-form>
                    </div>
                </el-tab-pane>
                <el-tab-pane :label="$t('login_mimadenglu')" name="password">
                    <div class="table-tabs">
                        <a-form
                            :model="formStateTwo"
                            ref="formRefTwo"
                            :rules="rulesTwo"
                            class="mw-form"
                        >
                            <a-form-item name="phone" :validateFirst="true">
                                <a-input
                                    size="large"
                                    autocomplete="off"
                                    v-model:value="formStateTwo.phone"
                                    :placeholder="$t('Phone/Mail')"
                                    @keyup.enter="onSubmit"
                                    class="input-color"
                                >
                                </a-input>
                            </a-form-item>
                            <a-form-item name="password" :validateFirst="true">
                                <a-input-password
                                    size="large"
                                    v-model:value="formStateTwo.password"
                                    :placeholder="
                                        $t('placeholder_qingshurumima')
                                    "
                                    @keyup.enter="onSubmit"
                                    class="input-color"
                                >
                                </a-input-password>
                            </a-form-item>
                        </a-form>
                    </div>
                </el-tab-pane>
            </template>
        </el-tabs>
    </div>
    <p class="checkbox-box" v-show="agreePolicyTip">{{ $t('notAccepted') }}</p>
    <div class="login-bar">
        <el-button
            plain
            round
            type="primary"
            @click="onSubmit"
            :loading="loading"
            class="login-bt"
            size="large"
            :disabled="!isValided"
            :class="!isValided ? 'btn-disabled' : ''"
            >{{ $t('login') }}</el-button
        >
        <p class="demo-title" v-if="hasDemo">
            <span
                @click="demonstrateLogin"
                class="cursor-pointer select-none"
                >{{ $t('demoLogin') }}</span
            >
        </p>
        <div class="login-policy">
            <a-checkbox
                @change="handleAgreePolicyChange"
                v-model:checked="agreePolicy"
                size="small"
                class="my-checkbox"
            ></a-checkbox>
            <span class="policy-text"
                >{{ $t('UnderstandAndAgree') }} 《
                <span>
                    <router-link :to="{ path: '/agreement' }" target="_blank">{{
                        $t('UserAgreement')
                    }}</router-link>
                </span>
                》{{ $t('toLogin') }}</span
            >
        </div>
    </div>
</template>
<script>
import {
    defineComponent,
    reactive,
    toRaw,
    ref,
    toRefs,
    computed,
    getCurrentInstance,
} from 'vue'
import smsCode from '@/components/smsCode'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { checkPhone, checkPhoneOrEmail } from '@/common/validate'
import { env } from 'dingtalk-jsapi'
import { useI18n } from 'vue-i18n'
import { mobile, email } from '@/common/reg'
export default defineComponent({
    components: {
        // graphCode
        smsCode,
    },
    props: {
        thirdUserIdentify: String,
    },
    setup(props) {
        const { proxy } = getCurrentInstance()
        // const { thirdUserIdentify } = toRefs(props);
        const router = useRouter()
        const route = useRoute()
        const store = useStore()
        const formRef = ref()
        const { t, locale } = useI18n()
        const isInDingtalk = computed(() => {
            return env.platform != 'notInDingTalk'
        })
        const state = reactive({
            agreePolicyTip: false,
            loading: false,
            agreePolicy: false,
        })
        const formState = reactive({
            deviceType: 'PC',
            phone: undefined,
            email: undefined,
            smsCode: undefined,
            loginType: 'sms',
            //   isSave: true,
            //   saveTime: 15,
        })
        const graphCodeRef = ref(null)
        const isValided = computed(() => {
            if (activeName.value === 'smscode') {
                // 验证码登录时的验证
                return (
                    !!formState.phone &&
                    !!formState.smsCode &&
                    state.agreePolicy
                )
            } else {
                // 密码登录时的验证
                return (
                    !!formStateTwo.phone &&
                    !!formStateTwo.password &&
                    state.agreePolicy
                )
            }
        })
        const onSubmit = (type) => {
            if (activeName.value === 'smscode' || type === 'demo') {
                formRef.value
                    .validate()
                    .then(async () => {
                        if (!state.agreePolicy) {
                            state.agreePolicyTip = true
                        } else {
                            state.loading = true
                            await store.dispatch('user/newLoginInt', {
                                ...formState,
                            })
                            state.loading = false
                        }
                    })
                    .catch((error) => {
                        state.loading = false
                        state.agreePolicyTip = false
                    })
            } else {
                formRefTwo.value
                    .validate()
                    .then(async () => {
                        if (!state.agreePolicy) {
                            state.agreePolicyTip = true
                        } else {
                            state.loading = true
                            // 根据输入内容判断是手机号还是邮箱
                            const inputValue = formStateTwo.phone
                            const isPhone = mobile.test(inputValue)
                            const isEmail = email.test(inputValue)

                            // 构建登录参数
                            const loginParams = {
                                deviceType: formStateTwo.deviceType,
                                password: formStateTwo.password,
                                loginType: formStateTwo.loginType,
                            }

                            // 根据输入类型设置相应的参数
                            if (isPhone) {
                                loginParams.phone = inputValue
                            } else if (isEmail) {
                                loginParams.email = inputValue
                            }

                            await store.dispatch(
                                'user/newLoginInt',
                                loginParams
                            )
                            state.loading = false
                        }
                    })
                    .catch((error) => {
                        state.loading = false
                        state.agreePolicyTip = false
                    })
            }
        }
        const resetForm = () => {
            formRef.value.resetFields()
        }

        const rules = {
            phone: [
                {
                    required: true,
                    message: t('placeholder_phone'),
                    trigger: 'blur',
                },
                {
                    validator: checkPhone,
                    message: t('Please enter a valid phone number'),
                    trigger: 'blur',
                },
            ],
            smsCode: [
                {
                    required: true,
                    message: t('login_tips02'),
                    trigger: 'blur',
                },
            ],
            // password: [
            //   {
            //     validator: checkPassword,
            //     trigger: "blur",
            //   },
            // ],
            // graphCode: [
            //   {
            //     required: true,
            //     message: "请填写验证码",
            //     trigger: "blur",
            //   },
            // ],
        }
        const onChangeCode = async (callback) => {
            await formRef.value.validate('phone')
            callback()
        }
        const handleAgreePolicyChange = (e) => {
            if (e.target.checked) {
                state.agreePolicyTip = false
            }
        }

        const demonstrateLogin = () => {
            formState.phone = '13200000000'
            formState.smsCode = '1234'
            state.agreePolicy = true
            onSubmit('demo')
        }

        const goBacks = () => {
            // window.open('https://devops.mingwork.com/contract.pdf', '_blank')
            router.push('/agreement')
        }
        const hasDemoHosts = [
            'ems.ssnj.com',
            'ems-beta.ssnj.com',
            'localhost:8080',
            'faw.ssnj.com:8080',
        ]
        const hasDemo = computed(() => {
            // 获取域名
            const host = window.location.host
            return hasDemoHosts.includes(host)
        })
        const activeName = ref('smscode')

        // 添加密码登录表单状态
        const formStateTwo = reactive({
            deviceType: 'PC',
            phone: undefined,
            password: undefined,
            loginType: 'password',
        })

        const formRefTwo = ref()

        // 添加密码验证规则
        const rulesTwo = {
            phone: [
                {
                    required: true,
                    message: t('placeholder_phone'),
                    trigger: 'blur',
                },
                {
                    validator: checkPhoneOrEmail,
                    message: t('Please enter a valid phone number or email'),
                    trigger: 'blur',
                },
            ],
            password: [
                {
                    required: true,
                    message: t('Password_tips03'),
                    trigger: 'blur',
                },
                {
                    min: 6,
                    message: t('Password_tips10'),
                    trigger: 'blur',
                },
            ],
        }

        // 处理tab切换
        const handleTabChange = (tab) => {
            // 切换tab时重置表单
            if (tab === 'smscode') {
                formRef.value?.resetFields()
            } else {
                formRefTwo.value?.resetFields()
            }
        }
        const changeLanguage = async (val) => {
            // window.location.reload()
            locale.value = val
            await store.dispatch('lang/changeLanguage', val)
        }
        return {
            ...toRefs(state),
            formRef,
            formState,
            onSubmit,
            resetForm,
            rules,
            onChangeCode,
            graphCodeRef,
            handleAgreePolicyChange,
            demonstrateLogin,
            goBacks,
            isValided,
            hasDemo,
            activeName,
            formStateTwo,
            formRefTwo,
            rulesTwo,
            handleTabChange,
            changeLanguage,
            locale,
        }
    },
})
</script>
<style lang="less" scoped>
.mw-form {
    margin-top: 30px;

    .input-color {
        border-top-width: 0;
        border-left-width: 0;
        border-right-width: 0 !important;
        // border-bottom-color:rgba(34,34,34,0.08);
        box-shadow: none;
        border-radius: 0;
        color: #222222 !important;

        &:focus {
            //   border-color:#fff !important;
            border-bottom-color: var(--themeColor);
        }

        // &:hover{
        // border-color:#fff ;
        // border-bottom-color:#47BABC;
        // border-right-width:0;
        // }
        font-size: 16px;

        &:hover {
            border-color: var(--themeColor);
        }
    }

    :deep(.ant-input) {
        border-radius: 0 !important;
    }

    :deep(.ant-input-affix-wrapper-focused) {
        border-color: var(--themeColor);
    }
}

.demo-title {
    line-height: 22px;
    height: 22px;
    font-size: 14px;

    color: var(--themeColor);
    text-align: center;
    text-decoration: underline;
    // cursor: pointer;
    margin-bottom: 30px;
}

.policy-text {
    height: 17px;
    font-size: 12px;
    color: #222222;
    line-height: 17px;

    span {
        cursor: pointer;
        text-decoration: underline;

        &:hover {
            color: var(--themeColor);
        }
    }
}

.login-bt {
    width: 100%;
    margin-bottom: 12px;
    font-size: 16px;
    box-sizing: border-box;
    padding: 4px 15px;
    height: 48px;
    line-height: 16px;
    // background-color: var(--themeColor);
    // border-color: var(--themeColor);
    border-radius: 999px;
    color: var(--themeColor);
}

.checkbox-box {
    color: #ff4d4f;
    margin-top: 0;
    font-size: 12px;
    margin-left: 12px;
}

.login-policy {
    display: flex;
    align-items: center;

    .my-checkbox {
        margin-right: 4px;

        :deep(.ant-checkbox-input) {
            width: 16px;
            height: 16px;

            &:focus + .ant-checkbox-inner {
                border-color: var(--themeColor);
            }
        }

        :deep(.ant-checkbox-inner) {
            width: 16px;
            height: 16px;

            &:after {
                width: 5.71428571px;
                height: 9.14285714px;
            }

            &:hover {
                border-color: var(--themeColor);
            }
        }

        :deep(.ant-checkbox-checked) {
            .ant-checkbox-inner {
                background-color: var(--themeColor);
                border-color: var(--themeColor);
            }
        }

        :deep(.ant-checkbox) {
            &:hover {
                .ant-checkbox-inner {
                    border-color: var(--themeColor);
                }
            }
        }

        :deep(.ant-checkbox-checked) {
            &:after {
                border-color: var(--themeColor);
            }
        }
    }
}

:deep(.el-tabs__item) {
    padding: 0 16px;
}

:deep(.el-tabs__nav-wrap:after) {
    display: none;
}

:deep(.el-tabs__active-bar) {
    height: 2px;
    margin-bottom: 3px;
}
.langActive {
    color: var(--themeColor);
}
.dark {
}
:deep(.el-button--primary.is-link.is-disabled) {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(111, 190, 206, 0.2);
    color: var(--themeColor);
}
:deep(.el-button--primary.is-link.is-disabled:active) {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(111, 190, 206, 0.2);
    color: var(--themeColor);
}
:deep(.el-button--primary.is-link.is-disabled:focus) {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(111, 190, 206, 0.2);
    color: var(--themeColor);
}
:deep(.el-button--primary.is-link.is-disabled:hover) {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(111, 190, 206, 0.2);
    color: var(--themeColor);
}
:deep(.el-button--primary.is-plain.is-disabled) {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(111, 190, 206, 0.2);
    color: var(--themeColor);
}
:deep(.el-button--primary.is-plain.is-disabled:active) {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(111, 190, 206, 0.2);
    color: var(--themeColor);
}
:deep(.el-button--primary.is-plain.is-disabled:hover) {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(111, 190, 206, 0.2);
    color: var(--themeColor);
}
:deep(.el-button--primary.is-plain.is-disabled:focus) {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(111, 190, 206, 0.2);
    color: var(--themeColor);
}
:deep(.el-button--primary.is-text.is-disabled) {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(111, 190, 206, 0.2);
    color: var(--themeColor);
}
:deep(.el-button--primary.is-text.is-disabled:active) {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(111, 190, 206, 0.2);
    color: var(--themeColor);
}
:deep(.el-button--primary.is-text.is-disabled:focus) {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(111, 190, 206, 0.2);
    color: var(--themeColor);
}
:deep(.el-button--primary.is-text.is-disabled:hover) {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(111, 190, 206, 0.2);
    color: var(--themeColor) !important;
}
:deep(.el-button--primary.is-plain.is-disabled:hover) {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(111, 190, 206, 0.2);
    color: var(--themeColor) !important;
}
.login-bt.el-button.is-round {
    border-color: rgba(111, 190, 206, 0.2);
}

:deep(.el-tabs__active-bar) {
    background-color: var(--themeColor) !important;
}
.pre-tag {
    background: rgba(34, 34, 34, 0.08);
}
</style>
