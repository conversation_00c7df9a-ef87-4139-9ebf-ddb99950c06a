<template>
    <div class="body-inner w-full h-full relative">
        <!-- <span v-if="thirdUserIdentify"> 绑定手机号</span>
    <a-radio-group v-else v-model:value="loginType">
      <a-radio-button value="phone">账号登录</a-radio-button>
      <a-radio-button value="qrcode">二维码登录</a-radio-button>
    </a-radio-group> -->
        <phone-login :thirdUserIdentify="thirdUserIdentify" />
    </div>
</template>
<script>
import PhoneLogin from './phoneLogin.vue'
// import wxlogin from "vue-wxlogin";
import { domain } from '@/config/env'
import {
    reactive,
    toRefs,
    computed,
    watch,
    onMounted,
    getCurrentInstance,
} from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { env } from 'dingtalk-jsapi'
import { useStore } from 'vuex'

export default {
    components: { PhoneLogin },
    setup() {
        const { proxy } = getCurrentInstance()
        const store = useStore()
        const route = useRoute()
        const router = useRouter()
        const isInDingtalk = computed(() => {
            return env.platform != 'notInDingTalk'
        })
        const state = reactive({
            loginType: isInDingtalk.value ? 'qrcode' : 'phone',
            dingTalkIdentify: '',
        })
        const thirdUserIdentify = computed(() => {
            return route.query.thirdUserIdentify || state.dingTalkIdentify
        })
        onMounted(() => {
            store.commit('user/setLoading', false)
        })
        watch(
            () => route.query.thirdUserIdentify,
            (val) => {
                if (val) {
                    state.loginType = 'phone'
                }
            },
            { immediate: true }
        )
        return { ...toRefs(state), domain, thirdUserIdentify, isInDingtalk }
    },
}
</script>
<style lang="less" scoped>
.header {
    // margin-bottom: 30px;
    div {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    span {
        color: #595959;
    }
}
.wxlogin-container {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
}
#dingTalkLogin {
    width: 300px;
    height: 300px;
}
</style>
