export const fullTimePeriod = [
  '00:15', '00:30', '00:45',
  '01:00', '01:15', '01:30', '01:45',
  '02:00', '02:15', '02:30', '02:45',
  '03:00', '03:15', '03:30', '03:45',
  '04:00', '04:15', '04:30', '04:45',
  '05:00', '05:15', '05:30', '05:45',
  '06:00', '06:15', '06:30', '06:45',
  '07:00', '07:15', '07:30', '07:45',
  '08:00', '08:15', '08:30', '08:45',
  '09:00', '09:15', '09:30', '09:45',
  '10:00', '10:15', '10:30', '10:45',
  '11:00', '11:15', '11:30', '11:45',
  '12:00', '12:15', '12:30', '12:45',
  '13:00', '13:15', '13:30', '13:45',
  '14:00', '14:15', '14:30', '14:45',
  '15:00', '15:15', '15:30', '15:45',
  '16:00', '16:15', '16:30', '16:45',
  '17:00', '17:15', '17:30', '17:45',
  '18:00', '18:15', '18:30', '18:45',
  '19:00', '19:15', '19:30', '19:45',
  '20:00', '20:15', '20:30', '20:45',
  '21:00', '21:15', '21:30', '21:45',
  '22:00', '22:15', '22:30', '22:45',
  '23:00', '23:15', '23:30', '23:45', '24:00']

export const priceFullTimePeriod = [
  '01:00', '02:00', '03:00',
  '04:00','05:00', '06:00',
  '07:00','08:00', '09:00',
  '10:00', '11:00', '12:00',
  '13:00','14:00', '15:00',
  '16:00', '17:00', '18:00',
  '19:00','20:00', '21:00',
  '22:00','23:00', '24:00']

export const runModes = [
  {
    name: '手动模式',
    value: 1,
    color: '#1677ff',
    backGroundColor: 'rgba(22,119,255,0.1)'
  },
  {
    name: '计划模式',
    value: 2,
    color: '#33be4f',
    backGroundColor: 'rgba(51,190,79,0.1)'
  },
  // {
  //   name: 'AI模式',
  //   value: 3,
  //   color: '#7d4af9',
  //   backGroundColor: 'rgba(125,74,249,.0.1)'
  // }
]
export const runStatuss = [
  {
    name: '停机',
    value: 0
  },
  {
    name: '开机',
    value: 1
  }
]
export const gridStatuss = [
  {
    name: '离网',
    value: 0
  },
  {
    name: '并网',
    value: 1
  }
]

export const prioritys = [
  {
    name: '高',
    value: 1
  }, {
    name: '较高',
    value: 2
  }, {
    name: '中',
    value: 3
  }, {
    name: '较低',
    value: 4
  }, {
    name: '低',
    value: 5
  }
]

export const weeklys = [
  {
    value: '1',
    label: '周一',
  },
  {
    value: '2',
    label: '周二',
  },
  {
    value: '3',
    label: '周三',
  },
  {
    value: '4',
    label: '周四',
  },
  {
    value: '5',
    label: '周五',
  },
  {
    value: '6',
    label: '周六',
  },
  {
    value: '7',
    label: '周日',
  },
]


export const cycles = [
  {
    name: '每天重复',
    value: 1,
  },
  {
    name: '每周重复',
    value: 2,
  },
]
export const strategyTypes = [
  {
    name: '削峰填谷',
    value: 0,
  },
  {
    name: '动态增容',
    value: 1,
  },
]

export const transformerCapProtectOptions = [
  {
    label: '禁用',
    value: 0,
  },
  {
    label: '启用',
    value: 1,
  },
]
export const backFlowPreventionOptions = [
  {
    label: '禁用',
    value: 0,
  },
  {
    label: '启用',
    value: 1,
  },
]


export const formList = [
  {
    name: 'socMax',
    label: 'SOC上限（%）',
    type: 'number',
    min: 0,
    max: 100,
    step: 1,
    auth:[0,1],
    options: null
  },
  {
    name: 'socMin',
    label: 'SOC下限（%）',
    type: 'number',
    min: 0,
    max: 100,
    step: 1,
    auth:[0,1],
    options: null
  },
  {
    name: 'backFlowPrevention',
    label: '防逆流',
    type: 'select',
    min: 0,
    max: 999,
    step: 1,
    auth:[0,],
    options: backFlowPreventionOptions
  },
  {
    name: 'gridPowerLimit',
    label: '电网供电功率下限（kW）',
    type: 'number',
    min: 0,
    max: 100,
    step: 1,
    auth:[0,],
    options: null
  },
  {
    name: 'gridPowerLimitTolerance',
    label: '电网供电功率下限误差（kW）',
    type: 'number',
    min: 0,
    max: 10,
    step: 1,
    auth:[0,],
    options: null
  },
  {
    name: 'batVMax',
    label: '最高电压单体门限（kW）',
    type: 'number',
    min: 2.8,
    max: 3.6,
    step: 0.1,
    auth:[0,],
    options: null
  },

  {
    name: 'batVMin',
    label: '最低电压单体门限（kW）',
    type: 'number',
    min: 2.8,
    max: 3.6,
    step: 0.1,
    auth:[0,],
    options: null
  },
  {
    name: 'transformerCapProtect',
    label: '变压器容量保护',
    type: 'select',
    min: 0,
    max: 999,
    step: 1,
    auth:[0,],
    options: transformerCapProtectOptions
  },

  {
    name: 'transformerCapMax',
    label: '变压器最大容量（kW）',
    type: 'number',
    min: 0,
    max: 9999999,
    step: 1,
    auth:[0,],
    options: null
  },
  {
    name: 'transformerCapRatio',
    label: '容量上限系数（0-100）',
    type: 'number',
    min: 0,
    max: 100,
    step: 1,
    auth:[0,],
    options: null
  },
  {
    name: 'tempMax',
    label: '温度上限（℃）',
    type: 'number',
    min: 0,
    max: 9999,
    step: 1,
    auth:[0,1],
    options: null
  },
  {
    name: 'startChargePower',
    label: '开始充电功率（kW）',
    type: 'number',
    min: 0,
    max: 100,
    step: 1,
    auth:[1],
    options: null
  },
  {
    name: 'endChargePower',
    label: '结束充电功率（kW）：',
    type: 'number',
    min: 0,
    max: 100,
    step: 1,
    auth:[1],
    options: null
  },

  {
    name: 'thresholdDischargePower',
    label: '放电阈值（kW）',
    type: 'number',
    min: 0,
    max: 100,
    step: 1,
    auth:[1],
    options: null
  },
  {
    name: 'endThresholdDischargePower',
    label: '放电结束阈值（kW）',
    type: 'number',
    min: 0,
    max: 100,
    step: 1,
    auth:[1],
    options: null
  },
  {
    name: 'thresholdDischargePowerSupplement',
    label: '放电结束补放值（kW）',
    type: 'number',
    min: 0,
    max: 100,
    step: 1,
    auth:[1],
    options: null
  },
  {
    name: 'chargePowerMax',
    label: '最大充电功率（kW）',
    type: 'number',
    min: 0,
    max: 100,
    step: 1,
    auth:[0,1],
    options: null
  },

  {
    name: 'dischargePowerMax',
    label: '最大放电功率（kW）',
    type: 'number',
    min: 0,
    max: 100,
    step: 1,
    auth:[0,1],
    options: null
  },
  {
    name: 'beforePowerOnTime',
    label: '空调提前开启时间（min）',
    type: 'number',
    min: 0,
    max: 1440,
    step: 1,
    auth:[0,1],
    options: null
  },
  {
    name: 'afterPowerOffTime',
    label: '空调延后关闭时间（min）',
    type: 'number',
    min: 0,
    max: 1440,
    step: 1,
    auth:[0,1],
    options: null
  },

]


export const modelStatusOptions = [
  {
      label: '使用中',
      value: true,
      color: '#4B82FF',
  },
  {
      label: '未使用',
      value: false,
      color: '#D9D9D9',
  },
]

export const segmentTypes = [
  {
    label: '尖',
    value: 1,
    text:'尖峰时段',
  },
  {
    label: '峰',
    value: 2,
    text:'高峰时段',
  },
  {
    label: '平',
    value: 3,
    text:'平时时段',
  },
  {
    label: '谷',
    value: 4,
    text:'低谷时段',
  },
  {
    label: '深谷',
    value: 5,
    text:'深谷时段',
  },
]