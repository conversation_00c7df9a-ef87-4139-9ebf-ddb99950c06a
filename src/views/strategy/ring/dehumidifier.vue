<template>
    <div
        class="items-center border-0 border-l border-r border-b border-border dark:border-border-dark rounded-lg inline-block"
    >
        <a-form :model="formState" ref="formRef" labelAlign="left">
            <div class="parameter">
                <div class="parameter-title">基础设定</div>
                <div class="flex flex-wrap">
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">温度手动开关：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.humiditySwitch"
                                param="humiditySwitch"
                                type="select"
                                :options="state.humiditySwitchs"
                                v-model:isEdit="isEdit"
                                @update="update"
                            />
                        </div>
                    </div>
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label"></div>
                        <div class="parameter-data"></div>
                    </div>
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">控温开启值：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.tempStart"
                                param="tempStart"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                unit="℃"
                                :min="-50"
                                :max="100"
                            />
                        </div>
                    </div>
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">控温停止值：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.tempStop"
                                param="tempStop"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                unit="℃"
                                :min="-50"
                                :max="100"
                            />
                        </div>
                    </div>

                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">控湿开启值：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.humidityStart"
                                param="humidityStart"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                unit="℃"
                                :min="0"
                                :max="100"
                            />
                        </div>
                    </div>
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">控湿停止值：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.humidityStop"
                                param="humidityStop"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                unit="℃"
                                :min="0"
                                :max="100"
                            />
                        </div>
                    </div>
                    <div class="w-1/2 parameter-list flex">
                        <div class="parameter-label">温度报警上限值：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.highTempAlarm"
                                param="highTempAlarm"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                unit="℃"
                                :min="-50"
                                :max="100"
                            />
                        </div>
                    </div>
                    <div class="w-1/2 parameter-list flex">
                        <div class="parameter-label">温度报警下限值：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.lowTempAlarm"
                                param="lowTempAlarm"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                unit="℃"
                                :min="-50"
                                :max="100"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </a-form>
    </div>
    <div class="text-center mt-4">
        <el-button plain round @click="onEdit">{{
            isEdit ? '取消' : '编辑'
        }}</el-button>
    </div>
</template>

<script setup>
import { reactive, ref, watch, provide, nextTick } from 'vue'
import settings from './components/settings.vue'

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    tabActiveKey: {
        type: String,
        default: '',
    },
})
const emit = defineEmits(['update'])

const formState = reactive({})
const isEdit = ref(false)
const state = reactive({
    humiditySwitchs: [
        {
            id: 0,
            name: '关闭',
        },
        {
            id: 1,
            name: '开启',
        },
    ],
})
const deviceType = ref()
provide('deviceType', deviceType)
watch(
    () => props.tabActiveKey,
    (val) => {
        isEdit.value = false
    }
)
watch(
    () => props.data,
    (data) => {
        if (data && Object.keys(data).length) {
            nextTick(() => {
                Object.keys(data).forEach((key) => {
                    formState[key] = data[key] || undefined
                })
                deviceType.value = data.deviceType
            })
        } else {
            nextTick(() => {
                Object.keys(formState).forEach((key) => {
                    formState[key] = undefined
                })
            })
        }
    },
    {
        immediate: true,
    }
)

const onEdit = () => {
    isEdit.value = !isEdit.value
}
const update = () => {
    //
    emit('update')
}
</script>

<style lang="less" scoped>
.parameter {
    width: 742px;
}

.parameter-title {
    text-align: center;
    line-height: 36px;
    background: #eee;
    border-radius: 8px;
}

.parameter-list {
    line-height: 44px;
    width: 370px;

    .parameter-label {
        width: 160px;
        text-align: left;
        padding-left: 16px;
        border-right: 1px solid #eee;
        color: var(--text-60);
    }

    .parameter-data {
        width: 210px;
        text-align: left;
        padding-left: 16px;
        color: var(--text-100);
    }

    &:nth-child(2n) {
        .parameter-label {
            border-left: 1px solid #eee;
        }
    }
}
</style>
