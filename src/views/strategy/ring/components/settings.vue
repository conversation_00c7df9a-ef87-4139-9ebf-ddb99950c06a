<template>
    <div v-if="!isEdit">
        {{
            type == 'select'
                ? data !== null && data !== undefined
                    ? getName(data, options)
                    : '-'
                : data !== null && data !== undefined
                ? data + unit
                : '-'
        }}
    </div>
    <div v-else class="flex items-center h-11 gap-x-3 pr-4">
        <a-select
            class="flex-1"
            v-model:value="newData"
            placeholder="请选择"
            v-if="type == 'select'"
        >
            <a-select-option
                v-for="item in options"
                :key="item.id"
                :value="item.id"
            >
                <p>
                    <span>{{ item.name }}</span>
                </p>
            </a-select-option>
        </a-select>
        <a-input-number
            v-if="type == 'inputNumber'"
            :min="min"
            :max="max"
            class="flex-1"
            v-model:value="newData"
            placeholder="请输入"
            :step="step"
        />
        <div v-if="unit">
            <span>{{ unit }}</span>
        </div>
        <el-button
            plain
            type="primary"
            round
            @click="onSubmit"
            style="padding: 0 12px"
            >下发</el-button
        >
    </div>
</template>

<script setup>
import { watch, ref, getCurrentInstance, inject } from 'vue'
import { useRoute } from 'vue-router'
import api from '@/apiService/strategy'
const route = useRoute()
const { proxy } = getCurrentInstance()
const props = defineProps({
    data: {
        type: Number,
        default: 1,
    },
    options: {
        type: Array,
        default: () => [],
    },
    isEdit: {
        type: Boolean,
        default: false,
    },

    param: {
        type: String,
        default: '',
    },
    type: {
        type: String,
        default: 'input',
    },
    unit: {
        type: String,
        default: '',
    },
    min: {
        type: Number,
        default: 0,
    },
    max: {
        type: Number,
        default: 0,
    },
    step: {
        type: Number,
        default: 1,
    },
})

const emit = defineEmits(['update'])
const getName = (value, array) => {
    const item = array.find((item) => item.id === value)
    return item?.name || '-'
}
const newData = ref(1)
watch(
    () => props.data,
    (val) => {
        newData.value = val
    },
    {
        immediate: true,
    }
)
const deviceType = inject('deviceType')
const containerNo = inject('containerNo')

const onSubmit = async () => {
    // handleReset()
    let params = {
        deviceType: deviceType.value || undefined,
        stationId: route.query.stationId,
        data: {
            [props.param]: newData.value,
        },
        containerNo: containerNo.value,
    }
    let res = await api.setDynamicEnvironmentSystem(params)
    if (res.data.data) {
        proxy.$message.success('下发成功')
        emit('update')
    }
}
</script>

<style lang="less" scoped></style>
