<template>
    <div class="px-4">
        <div
            class="flex mb-5 items-end justify-between pb-4 border-b border-border"
        >
            <div class="flex models">
                <div
                    class="model-item"
                    :class="{ active: tabActiveKey == item.key }"
                    @click="changeTabs(item)"
                    v-for="item in models"
                    :key="item.key"
                >
                    {{ item.title }}
                </div>
            </div>
            <div class="flex items-center gap-x-3">
                <el-select
                    v-model="containerNo"
                    placeholder="请选择"
                    style="width: 120px; margin-right: 16px"
                    @change="onContainerChange"
                >
                    <el-option
                        v-for="item in containerList"
                        :key="item.containerNo"
                        :label="'机柜' + item.containerNo.substring(3)"
                        :value="item.containerNo"
                    />
                </el-select>
            </div>
        </div>
        <div class="content">
            <a-spin :spinning="loading">
                <div class="text-center" v-show="tabActiveKey == 'cooler'">
                    <cooler
                        :data="{
                            ...detail.refrigeratorSetting,
                        }"
                        v-model:tabActiveKey="tabActiveKey"
                        @update="update"
                    />
                </div>
                <div
                    class="text-center"
                    v-show="tabActiveKey == 'dehumidifier'"
                >
                    <dehumidifier
                        :data="{
                            ...detail.dehumidifierSetting,
                        }"
                        v-model:tabActiveKey="tabActiveKey"
                        @update="update"
                    />
                </div>
            </a-spin>
        </div>
    </div>
</template>

<script setup>
import { onMounted, reactive, ref, provide } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import api from '@/apiService/strategy'
import apiService from '@/apiService/device'
import cooler from './cooler.vue'
import dehumidifier from './dehumidifier.vue'

const router = useRouter(),
    route = useRoute()

const goRouter = () => {
    router.go(-1)
}
const tabActiveKey = ref('cooler')
const models = ref([
    {
        title: '液冷机运行参数',
        key: 'cooler',
    },
    {
        title: '除湿机运行参数',
        key: 'dehumidifier',
    },
])
const referenceSite = ref('1')
const referenceSites = ref([
    {
        title: '站点1',
        key: '1',
    },
    {
        title: '站点2',
        key: '2',
    },
    {
        title: '站点3',
        key: '3',
    },
])
const changeTabs = (item) => {
    tabActiveKey.value = item.key
}
// 手动

const createTemp = () => {}
const detail = ref({})
const loading = ref(false)
const getData = async (no) => {
    loading.value = true
    //
    let res = await api.getEmsDynamicEnvironmentSystem({
        stationId: route.query.stationId,
        containerNo: no || containerNo.value,
    })
    detail.value = res.data.data
    loading.value = false
}

const update = async () => {
    await getData()
}
const containerNo = ref(null)
const containerList = ref([])
const getContainerList = async () => {
    let res = await apiService.getContainerList({
        stationNo: route.query.stationNo,
    })
    containerList.value = res.data.data
    containerNo.value = res.data.data[0].containerNo
}

const onContainerChange = async () => {
    await getData(containerNo.value)
}
provide('containerNo', containerNo)
onMounted(async () => {
    await getContainerList()
    await getData()
})
</script>

<style lang="less" scoped>
.models {
    // background: rgba(34, 34, 34, 0.04);
    border: 1px solid var(--border);
    border-radius: 4px;
    padding: 8px;
    position: relative;
    &::before {
        display: block;
        content: '';
        border-bottom: 4px solid var(--border);
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        position: absolute;
        left: 200px;
        bottom: 100%;
    }
    .model-item {
        font-size: 14px;
        line-height: 24px;
        text-align: center;
        padding: 8px 16px;
        background: transparent;
        cursor: pointer;
        transition: all 0.2s;
        &.active {
            background: #fff;
            color: var(--themeColor);
        }
    }
}
.dark {
    .models .model-item {
        color: var(--text-100);
    }
    .models .model-item.active {
        background-color: var(--bg-f5);
        color: var(--themeColor);
    }
}
.tactics-tip {
    background: var(--tag-blue-bg);
    color: #1677ff;
    border-radius: 2px;
    font-size: 12px;
}
:deep(.ant-switch-checked) {
    background-color: var(--themeColor);
}
:deep(.ant-form-item) {
    margin-bottom: 12px;
}
:deep(.ant-form-item-label) {
    width: 72px !important;
}
:deep(.ant-form-item-label > label) {
    color: var(--text-60);
}
</style>
