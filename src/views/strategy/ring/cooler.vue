<template>
    <div
        class="items-center border-0 border-l border-r border-b border-border dark:border-border-dark rounded-lg inline-block"
        style=""
    >
        <a-form :model="formState" ref="formRef" labelAlign="left">
            <div class="parameter">
                <div class="parameter-title">基础参数</div>
                <div class="inline-flex flex-wrap">
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">启动模式设定：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.bootMode"
                                param="bootMode"
                                type="select"
                                :options="state.bootModes"
                                v-model:isEdit="isEdit"
                                @update="update"
                            />
                        </div>
                    </div>
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">运行模式设定：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.setMode"
                                param="setMode"
                                type="select"
                                :options="state.setModes"
                                v-model:isEdit="isEdit"
                                @update="update"
                            />
                        </div>
                    </div>
                    <div class="w-1/2 parameter-list flex">
                        <div class="parameter-label">温控制设定方式：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.tempControlMode"
                                param="tempControlMode"
                                type="select"
                                :options="state.tempControlModes"
                                v-model:isEdit="isEdit"
                                @update="update"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="parameter">
                <div class="parameter-title">温度参数</div>
                <div class="inline-flex flex-wrap">
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">制冷设置温度：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.refrigerationTemp"
                                param="refrigerationTemp"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                unit="℃"
                                :min="15"
                                :max="30"
                            />
                        </div>
                    </div>
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">制冷回差温度：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.refrigerationReturnTemp"
                                param="refrigerationReturnTemp"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                unit="℃"
                                :min="1"
                                :max="10"
                            />
                        </div>
                    </div>
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">制热设置温度：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.heatTemp"
                                param="heatTemp"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                unit="℃"
                                :min="-15"
                                :max="25"
                            />
                        </div>
                    </div>
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">制热回差温度：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.heatReturnTemp"
                                param="heatReturnTemp"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                unit="℃"
                                :min="1"
                                :max="10"
                            />
                        </div>
                    </div>

                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">高温报警温度：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.highTempAlarm"
                                param="highTempAlarm"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                unit="℃"
                                :min="0"
                                :max="55"
                            />
                        </div>
                    </div>
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">低温报警温度：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.lowTempAlarm"
                                param="lowTempAlarm"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                unit="℃"
                                :min="-30"
                                :max="55"
                            />
                        </div>
                    </div>
                    <div class="w-1/2 parameter-list flex">
                        <div class="parameter-label">水温温差过高报警值：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.tempDifferenceAlarm"
                                param="tempDifferenceAlarm"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                unit="℃"
                                :min="0"
                                :max="30"
                            />
                        </div>
                    </div>
                    <div class="w-1/2 parameter-list flex">
                        <div class="parameter-label"></div>
                        <div class="parameter-data"></div>
                    </div>
                </div>
            </div>
            <div class="parameter">
                <div class="parameter-title">差额设定</div>
                <div class="inline-flex flex-wrap">
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">水泵压差设定值：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.pumpPressDiff"
                                param="pumpPressDiff"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                :min="0"
                                :max="500"
                            />
                        </div>
                    </div>
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">水泵压差回差值：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.pumpPressDiffReturn"
                                param="pumpPressDiffReturn"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                :min="0"
                                :max="10"
                            />
                        </div>
                    </div>
                    <div class="w-1/2 parameter-list flex">
                        <div class="parameter-label">水温温差设定值：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.waterTempDiff"
                                param="waterTempDiff"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                :min="0"
                                :max="10"
                            />
                        </div>
                    </div>
                    <div class="w-1/2 parameter-list flex">
                        <div class="parameter-label">水温温差回差值：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.waterTempDiffReturn"
                                param="waterTempDiffReturn"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                :min="0"
                                :max="10"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="parameter">
                <div class="parameter-title">压力参数</div>
                <div class="inline-flex flex-wrap">
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">回水压力过低点：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.pressureInLow"
                                param="pressureInLow"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                :min="0.0"
                                :max="2.0"
                                unit="bar"
                                :step="0.1"
                            />
                        </div>
                    </div>
                    <div
                        class="w-1/2 parameter-list flex border-border dark:border-border-dark border-b"
                    >
                        <div class="parameter-label">供水压力过高点：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.pressureOutHigh"
                                param="pressureOutHigh"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                :min="0.0"
                                :max="20.0"
                                unit="bar"
                                :step="0.1"
                            />
                        </div>
                    </div>
                    <div class="w-1/2 parameter-list flex">
                        <div class="parameter-label">供水压力预警点：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.pressureOutAlarm"
                                param="pressureOutAlarm"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                :min="0.0"
                                :max="20.0"
                                unit="bar"
                                :step="0.1"
                            />
                        </div>
                    </div>
                    <div class="w-1/2 parameter-list flex">
                        <div class="parameter-label">供水压力预警回差：</div>
                        <div class="parameter-data">
                            <settings
                                v-model:data="formState.pressureOutAlarmReturn"
                                param="pressureOutAlarmReturn"
                                type="inputNumber"
                                v-model:isEdit="isEdit"
                                @update="update"
                                :min="-9.0"
                                :max="9.0"
                                unit="bar"
                                :step="0.1"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </a-form>
    </div>
    <div class="text-center mt-4">
        <el-button plain round @click="onEdit">{{
            isEdit ? '取消' : '编辑'
        }}</el-button>
    </div>
</template>

<script setup>
import { reactive, ref, watch, provide, nextTick } from 'vue'
import settings from './components/settings.vue'

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    tabActiveKey: {
        type: String,
        default: '',
    },
})
const emit = defineEmits(['update'])
const state = reactive({
    bootModes: [
        {
            id: 0,
            name: '手动',
        },
        {
            id: 1,
            name: '自动',
        },
    ],
    setModes: [
        {
            id: 0,
            name: '关机',
        },
        {
            id: 1,
            name: '自动',
        },
        {
            id: 2,
            name: '水泵循环',
        },
        {
            id: 3,
            name: '制冷',
        },
        {
            id: 4,
            name: '制热',
        },
    ],
    tempControlModes: [
        {
            id: 0,
            name: '出水温度',
        },
        {
            id: 1,
            name: '进水温度',
        },
    ],
})
const formRef = ref()
const formState = reactive({
    deviceType: undefined,
})
const deviceType = ref(null)
const newFormState = reactive({})
const isEdit = ref(false)
watch(
    () => props.tabActiveKey,
    (val) => {
        isEdit.value = false
    }
)
watch(
    () => props.data,
    (data) => {
        if (data && Object.keys(data).length) {
            nextTick(() => {
                Object.keys(data).forEach((key) => {
                    formState[key] = data[key] || undefined
                })
                deviceType.value = data.deviceType
            })
        } else {
            nextTick(() => {
                Object.keys(formState).forEach((key) => {
                    formState[key] = undefined
                })
            })
        }
    },
    {
        immediate: true,
        deep: true,
    }
)
provide('deviceType', deviceType)
const onEdit = () => {
    isEdit.value = !isEdit.value
}

const update = async () => {
    //
    emit('update')
}
</script>

<style lang="less" scoped>
.parameter {
    width: 742px;
}

.parameter-title {
    text-align: center;
    line-height: 36px;
    background: #eee;
    border-radius: 8px;
}

.parameter-list {
    line-height: 44px;
    width: 370px;

    .parameter-label {
        width: 160px;
        text-align: left;
        padding-left: 16px;
        border-right: 1px solid #eee;
        color: var(--text-60);
    }

    .parameter-data {
        width: 210px;
        text-align: left;
        padding-left: 16px;
        color: var(--text-100);
    }

    &:nth-child(2n) {
        .parameter-label {
            border-left: 1px solid #eee;
        }
    }
}
</style>
