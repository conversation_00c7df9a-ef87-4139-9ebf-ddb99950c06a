<template>
    <a-spin :spinning="loading">
        <div class="relative setting">
            <div
                class="leading-5 space-x-6 flex justify-end absolute -top-20 right-0"
            >
                <el-button plain round type="primary" @click="onDistribute"
                    >分发</el-button
                >
                <span class="text-title dark:text-title-dark"
                    >选择参考站点：
                    <a-select
                        v-model:value="referenceSite"
                        placeholder="请选择参考站点"
                        class="w-40 text-left"
                        @change="changeReferenceSite"
                    >
                        <a-select-option
                            v-for="item in referenceSites"
                            :key="item.id"
                            :value="item.id"
                            >{{ item.stationName }}</a-select-option
                        >
                    </a-select>
                </span>
            </div>
            <div
                class="flex flex-wrap justify-center items-center gap-x-8 gap-y-7 months mx-auto"
            >
                <template v-for="(item, index) in settings" :key="index">
                    <!-- 未选择运行模式时 -->
                    <div
                        style="width: 275px; height: 200px"
                        class="p-8 bg-background dark:bg-ff-dark rounded-lg text-primary-text dark:text-80-dark text-left cursor-pointer"
                        v-if="
                            !item.priceTemplateId &&
                            !item.specialDayElectricPrice
                        "
                    >
                        <div class="text-2xl mb-4">
                            {{ item.effectiveMonth }}月
                        </div>
                        <div class="h-10 leading-5">
                            <a-select
                                v-model:value="item.priceTemplateId"
                                placeholder="请选择运行模式"
                                class="w-full"
                                @change="changedRunMode($event, index)"
                            >
                                <a-select-option
                                    v-for="ite in allTempList"
                                    :key="ite.id"
                                    :value="ite.id"
                                    >{{ ite.name }}</a-select-option
                                >
                            </a-select>
                        </div>
                        <!-- 选择后Z -->
                    </div>

                    <a-popover
                        title=""
                        trigger="click"
                        v-else
                        :destroyTooltipOnHide="true"
                        v-model:visible="item.visible"
                    >
                        <template #title> </template>
                        <template #content>
                            <div
                                class="flex justify-between items-center"
                                style="width: 480px"
                            >
                                <div
                                    class="text-2xl font-medium text-title dark:text-title-dark"
                                >
                                    {{ item.effectiveMonth }}月
                                </div>
                                <div class="flex gap-x-3.5">
                                    <el-button plain round @click="onAdd(item)"
                                        >添加特殊时段</el-button
                                    >
                                    <a-select
                                        v-model:value="item.priceTemplateId"
                                        placeholder="请选择运行模式"
                                        class="w-40"
                                        @change="changeTemp"
                                    >
                                        <a-select-option
                                            v-for="item in allTempList"
                                            :key="item.id"
                                            :value="item.id"
                                            >{{ item.name }}</a-select-option
                                        >
                                    </a-select>
                                </div>
                            </div>
                            <div
                                class=""
                                style="
                                    width: 480px;
                                    height: 246px;
                                    padding: 10px;
                                "
                            >
                                <canvas
                                    style="
                                        width: 460px;
                                        height: 226px;
                                        z-index: 1;
                                    "
                                    width="440"
                                    height="240"
                                    canvas-id="priceChart"
                                    id="priceChart"
                                    ref="priceChart"
                                    :key="new Date().getTime()"
                                ></canvas>
                            </div>
                            <!-- <a-spin :spinning="popLoading">
                            </a-spin> -->
                        </template>
                        <div
                            style="width: 275px; height: 200px"
                            class="p-8 bg-background dark:bg-ff-dark rounded-lg text-primary-text dark:text-80-dark text-left cursor-pointer"
                            @click="selectItem(item)"
                        >
                            <div class="text-2xl mb-4">
                                {{ item.effectiveMonth }}月
                            </div>
                            <div class="leading-8 run-mode">
                                <div style="max-height: 93px; overflow: hidden">
                                    <a-select
                                        :value="item.priceTemplateId"
                                        placeholder="请选择运行模式"
                                        class="w-full"
                                        disabled
                                    >
                                        <a-select-option
                                            v-for="ite in allTempList"
                                            :key="ite.id"
                                            :value="ite.id"
                                            >{{ ite.name }}</a-select-option
                                        >
                                    </a-select>
                                    <div
                                        class="text-sm"
                                        v-if="
                                            item.specialDayElectricPrice
                                                ?.templateName
                                        "
                                    >
                                        <span
                                            class="inline-block w-1.5 h-1.5 rounded bg-wait align-middle"
                                        ></span>
                                        {{
                                            item.specialDayElectricPrice
                                                ?.templateName
                                        }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a-popover>
                </template>
            </div>
            <div class="flex justify-center items-center gap-x-3 mt-10">
                <el-button plain round @click="onRefresh">刷新</el-button>
                <el-button plain type="primary" round @click="onSave(false)"
                    >保存</el-button
                >
            </div>

            <el-drawer
                v-model="onDistributeVisible"
                :size="486"
                :show-close="false"
                @close="onDistributeClose"
                wrapClassName="drawerBox"
            >
                <template #header>
                    <div
                        class="drawer-header flex items-center justify-between leading-5.5"
                    >
                        <div
                            class="drawer-header text-primary-text dark:text-80-dark"
                        >
                            <span>{{ '电价管理策略分发' }}</span>
                        </div>
                        <div class="flex gap-x-3 items-center">
                            <el-button plain round @click="onDistributeClose">{{
                                $t('common_guanbi')
                            }}</el-button>
                            <confirm-button
                                :title="'如果目标站点已配有价格，将会覆盖！'"
                                @confirm="onConfirm"
                                placement="bottom-end"
                            >
                                <template #reference>
                                    <el-button
                                        plain
                                        round
                                        type="primary"
                                        class="btn-hover"
                                    >
                                        <span>分发</span>
                                    </el-button>
                                </template>
                            </confirm-button>
                        </div>
                    </div>
                </template>
                <div class="">
                    <el-form
                        ref="formRef"
                        :model="formState"
                        label-width="auto"
                        style="max-width: 600px"
                        :rules="rules"
                        label-position="left"
                    >
                        <el-form-item
                            label="分发站点选择"
                            prop="targetStationIds"
                        >
                            <el-select
                                v-model="formState.targetStationIds"
                                multiple
                                placeholder="请选择分发站点"
                                filterable
                                no-match-text="无匹配数据"
                            >
                                <el-option
                                    v-for="item in referenceSites"
                                    :key="item.id"
                                    :label="item.stationName"
                                    :value="item.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>
            </el-drawer>
            <el-drawer
                v-model="specialVisible"
                :size="486"
                :show-close="false"
                @close="onSpecialClose"
                wrapClassName="drawerBox"
            >
                <template #header>
                    <div
                        class="drawer-header flex items-center justify-between leading-5.5"
                    >
                        <div
                            class="drawer-header text-primary-text dark:text-80-dark"
                        >
                            <span>{{
                                selectedItem?.effectiveMonth + '月特殊时段配置'
                            }}</span>
                        </div>
                        <div class="flex gap-x-3 items-center">
                            <el-button plain round @click="onSpecialClose">{{
                                $t('common_guanbi')
                            }}</el-button>
                            <el-button
                                plain
                                round
                                type="primary"
                                @click="onSetSpecial"
                                >保存</el-button
                            >
                        </div>
                    </div>
                </template>
                <div class="add-title">通用时段策略</div>
                <div
                    v-if="specialSetting.priceTemplateId"
                    class="mt-4 text-left"
                >
                    <el-config-provider :locale="zhCn">
                        <div class="flex items-center mb-4">
                            <div class="w-17 text-title dark:text-title-dark">
                                策略模版:
                            </div>
                            <div class="flex-1">
                                <el-select
                                    v-model="specialSetting.priceTemplateId"
                                    placeholder="请选择运行模式"
                                    filterable
                                    no-match-text="无匹配数据"
                                >
                                    <el-option
                                        v-for="item in allTempList"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"
                                    />
                                </el-select>
                            </div>
                        </div>
                        <div
                            class="w-full px-3 py-4 bg-background dark:bg-ff-dark rounded-lg form"
                        >
                            <div class="add-title">特殊时段策略</div>
                            <div
                                v-for="(ite, index) in specialList"
                                :key="index"
                                class="mt-4"
                            >
                                <div class="flex items-center mb-3">
                                    <div
                                        class="w-17 text-title dark:text-title-dark"
                                    >
                                        策略模版:
                                    </div>
                                    <div class="flex-1">
                                        <el-select
                                            v-model="ite.specialTemplateId"
                                            placeholder="请选择运行模式"
                                            no-match-text="无匹配数据"
                                        >
                                            <el-option
                                                v-for="item in allTempList"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.id"
                                            />
                                        </el-select>
                                    </div>
                                </div>
                                <div class="flex items-center text-left">
                                    <div
                                        class="w-17 text-title dark:text-title-dark"
                                    >
                                        日期区间:
                                    </div>
                                    <!--   -->
                                    <el-date-picker
                                        v-model="ite.rangeDate"
                                        type="daterange"
                                        :start-placeholder="
                                            $t('common_kaishiriqi')
                                        "
                                        :end-placeholder="
                                            $t('common_jieshuriqi')
                                        "
                                        value-format="YYYY-MM-DD"
                                        :default-value="defaultMonth"
                                        :disabled-date="disabledDate"
                                    />
                                </div>
                                <div></div>
                                <div
                                    class="flex items-center justify-center gap-x-3 mt-4"
                                >
                                    <el-button
                                        plain
                                        round
                                        size="small"
                                        @click="onDeleteItem(ite)"
                                        >删除</el-button
                                    >
                                </div>
                            </div>
                            <div class="text-center">
                                <el-button
                                    plain
                                    round
                                    type="primary"
                                    v-if="specialList.length <= 0"
                                    size="small"
                                    @click="onAddItem"
                                    >添加策略</el-button
                                >
                            </div>
                        </div>
                    </el-config-provider>
                </div>
            </el-drawer>
        </div>
    </a-spin>
</template>

<script setup>
import {
    onMounted,
    reactive,
    ref,
    getCurrentInstance,
    nextTick,
    computed,
    toRaw,
    onBeforeUnmount,
} from 'vue'
import _cloneDeep from 'lodash/cloneDeep'
import api from '@/apiService/strategy'
import { useRoute, onBeforeRouteLeave } from 'vue-router'
import { DrawCanvas, formatData } from '@/common/drawPriceChart'
import moment from 'moment'
import { ElMessageBox } from 'element-plus'
import { useStore } from 'vuex'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

const route = useRoute()

const props = defineProps({
    hasChanged: Boolean,
})

const emit = defineEmits(['updateChanged'])

const specialSetting = ref({
    priceTemplateId: undefined,
    specialTemplateId: undefined,
    rangeDate: [],
})
// const tooltipVisible = ref(false)
const selectedItem = ref(null)
const selectItem = async (item) => {
    selectedItem.value = item
    if (item.priceTemplateId) {
        settings.value[item.effectiveMonth - 1]['visible'] = true
        setTimeout(async () => {
            await getDetail(item.priceTemplateId)
        }, 300)
    }
}
const { proxy } = getCurrentInstance()
const settings = ref(
    Array.from({ length: 12 }, (_, i) => ({
        id: undefined,
        stationNo: undefined,
        priceTemplateId: undefined,
        templateName: undefined,
        effectiveMonth: i + 1,
    }))
)
const referenceSite = ref(undefined)
const referenceSites = ref()
const loading = ref(false)
const onDistributeVisible = ref(false)
const onDistribute = async () => {
    //
    await getStations()
    onDistributeVisible.value = true
}
const formState = reactive({
    targetStationIds: [],
})

const rules = {
    targetStationIds: [
        {
            required: true,
            message: '请选择分发站点',
        },
    ],
}

const formRef = ref()
const onConfirm = async () => {
    formRef.value.validate().then(async (valid) => {
        if (valid) {
            let res = await api.authorizationStationElectricPrice({
                sourceStationId: route.query.stationId,
                ...toRaw(formState),
            })
            if (res.data.data) {
                proxy.$message.success('操作成功')
                onDistributeClose()
            }
        }
    })
}
// 刷新
const onRefresh = async () => {
    //
    loading.value = true
    await getSettings()
    loading.value = false
}

// 保存
const onSave = async (hasYear) => {
    //
    if (JSON.stringify(oldSettings.value) == JSON.stringify(settings.value)) {
        proxy.$message.success('保存成功')
        return
    }

    loading.value = true
    let monthElectricPriceList = settings.value.filter((item) => {
        return item.priceTemplateId
    })
    let params = {
        stationId: route.query.stationId,
        monthElectricPriceList: monthElectricPriceList.map((item) => {
            if (item.specialDayElectricPrice) {
                return {
                    templateId: item.priceTemplateId,
                    effectiveMonth: item.effectiveMonth,
                    specialDayElectricPrice: Object.keys(
                        item.specialDayElectricPrice
                    ).length
                        ? {
                              templateId:
                                  item.specialDayElectricPrice.templateId ||
                                  item.specialDayElectricPrice.priceTemplateId,
                              startDate:
                                  item.specialDayElectricPrice.startDate
                                      .length > 6
                                      ? item.specialDayElectricPrice.startDate
                                      : moment().year() +
                                        '-' +
                                        item.specialDayElectricPrice.startDate,

                              endDate:
                                  item.specialDayElectricPrice.endDate.length >
                                  6
                                      ? item.specialDayElectricPrice.endDate
                                      : moment().year() +
                                        '-' +
                                        item.specialDayElectricPrice.endDate,
                          }
                        : undefined,
                }
            } else {
                return {
                    templateId: item.priceTemplateId,
                    effectiveMonth: item.effectiveMonth,
                    specialDayElectricPrice: undefined,
                }
            }
        }),
    }
    try {
        let res = await api.settingStationElectricPrice(params)
        proxy.$message.success('保存成功')
        await getSettings()
        emit('updateChanged', false)
        loading.value = false
    } catch (error) {
        loading.value = false
    }
}

const formatMonthData = (data) => {
    // 创建一个包含12个月的模板数组
    let completeData = Array.from({ length: 12 }, (_, i) => ({
        id: undefined,
        stationNo: undefined,
        priceTemplateId: undefined,
        templateName: undefined,
        effectiveMonth: i + 1,
        specialDayElectricPrice: undefined,
    }))
    // 根据有效月份替换模板数组中的数据
    for (const key in data) {
        let normalDay = data[key].filter((item) => {
            return item.specialDay == 0
        })
        let specialDay = data[key].filter((item) => {
            return item.specialDay == 1
        })
        completeData[key - 1] = (normalDay && normalDay[0]) || {}
        completeData[key - 1].specialDayElectricPrice =
            (specialDay && specialDay[0]) || {}
    }
    // data.forEach((item) => {
    //     // const monthIndex = item.effectiveMonth - 1 // 将月份转换为数组索引
    //     //
    // })
    return completeData
}

const changedSetting = () => {
    emit('updateChanged', true)
}

const changedRunMode = (e, i) => {
    changedSetting()
}

const oldSettings = ref([])
const getSettings = async () => {
    let res = await api.getElecPriceStationList({
        stationId: route.query.stationId,
    })
    settings.value = formatMonthData(res.data.data)
    oldSettings.value = _cloneDeep(settings.value)
}

const allTempList = ref([])
const getTemps = async () => {
    let allRes = await api.getElecPriceTemplateList({
        current: 1,
        size: 9999,
        stationId: route.query.stationId,
    })
    allTempList.value = allRes.data.data.records
}
const store = useStore()
const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])
const orgId = ref(
    getCompanyInfo?.value?.orgId ? getCompanyInfo.value.orgId : void 0
)
const activeSystem = computed(() => localStorage.getItem('activeSystem'))
const getStations = async () => {
    const page = {
        orgId: orgId.value,
        stationType:
            activeSystem.value == 'car'
                ? 'vehicle_battery'
                : 'energy_storage_cabinet',
    }
    const res = await api.getOrgAndSubOrgStationNameList(page)
    referenceSites.value = res.data.data.filter(
        (item) => item.id != route.query.stationId
    )
}
const changeReferenceSite = async (e) => {
    let res = await api.getElecPriceStationList({
        stationId: e,
    })
    settings.value = formatMonthData(res.data.data)
    changedSetting()
}
const getData = async () => {
    loading.value = true
    // 获取模版
    await getTemps()
    // 获取配置
    await getSettings()
    await getStations()
    loading.value = false
}
const getReferenceList = async () => {
    // let res = await api.getStationList({
    //     current: 1,
    //     size: 9999,
    // })
    // referenceSite.value = res.data.data.records
}
onMounted(async () => {
    await getReferenceList()
    await getData()
})

const changeTemp = async (e) => {
    await getDetail(e)
    changedSetting()
}

const drawPrice = (workProcessGroup, prices) => {
    let data = formatData(workProcessGroup, prices)
    var context = document.getElementById('priceChart').getContext('2d')
    var painter = new DrawCanvas(context)
    nextTick(() => {
        painter.update(data)
        painter.clear()
        painter.paint()
    })
}
const popLoading = ref(false)
const getDetail = async (id) => {
    popLoading.value = true
    //
    let res = await api.getElecPriceTemplateDetail({
        id: id,
        stationId: route.query.stationId,
    })
    let detail = res.data.data
    let workProcessList = detail.electricSegments.map((item) => {
        return {
            ...item,
            startHour: parseInt(item.startTime.substring(0, 2), 10),
            endHour: parseInt(item.endTime.substring(0, 2), 10),
            segmentType: item.segmentType,
            batteryWorkStatus: 0,
            price: item.price,
            salePrice: item.salePrice,
        }
    })
    let workProcessGroup = {
        season: detail.name,
        workProcessId: null,
        workProcessList: workProcessList,
    }
    nextTick(() => {
        setTimeout(() => {
            drawPrice(workProcessGroup, { ...detail })
        }, 200)
    })
    popLoading.value = false
}

const specialVisible = ref(false)

const onAdd = (item) => {
    settings.value[item.effectiveMonth - 1]['visible'] = false
    specialVisible.value = true
    specialSetting.value = _cloneDeep(item)
    if (Object.keys(item.specialDayElectricPrice).length) {
        let startDate =
            item.specialDayElectricPrice.startDate.length > 6
                ? item.specialDayElectricPrice.startDate
                : moment().year() + '-' + item.specialDayElectricPrice.startDate
        let endDate =
            item.specialDayElectricPrice.endDate.length > 6
                ? item.specialDayElectricPrice.endDate
                : moment().year() + '-' + item.specialDayElectricPrice.endDate
        specialList.value[0] = {
            specialTemplateId: item.specialDayElectricPrice.priceTemplateId,
            rangeDate: [startDate, endDate],
        }
    }
}

const specialList = ref([
    {
        specialTemplateId: undefined,
        rangeDate: [],
    },
])
const onSetSpecial = async () => {
    if (specialList.value.length > 0) {
        // 判断是否有空值
        const hasEmpty = specialList.value.some((item) => {
            return !item.specialTemplateId || !item.rangeDate
        })
        if (hasEmpty) {
            proxy.$message.warning('请配置好特殊时段模版和日期')
            return
        }
        let universalId = specialSetting.value.priceTemplateId
        if (universalId == specialList.value[0].specialTemplateId) {
            proxy.$message.warning('特殊时段模版不能和通用时段模版相同')
            return
        }
        // 判断如果特殊时段天数是整个月的话，提示报错
        if (
            moment(specialList.value[0].rangeDate[1]).diff(
                moment(specialList.value[0].rangeDate[0]),
                'days'
            ) +
                1 >
            10
        ) {
            proxy.$message.warning('特殊时段天数不能超过10天')
            return
        }
        const month = selectedItem.value.effectiveMonth
        const list = specialList.value.map((item) => {
            return {
                templateId: item.specialTemplateId,
                startDate: item.rangeDate[0],
                endDate: item.rangeDate[1],
            }
        })
        //    将本月的特殊时段设置到模版列表对应的月份中
        settings.value[month - 1]['specialDayElectricPrice'] = list[0]
        settings.value[month - 1].priceTemplateId =
            specialSetting.value.priceTemplateId
    } else {
        const month = selectedItem.value.effectiveMonth
        settings.value[month - 1]['specialDayElectricPrice'] = {}
        settings.value[month - 1].priceTemplateId =
            specialSetting.value.priceTemplateId
    }
    onSpecialClose()

    await onSave(true)
}
const choiceDate = ref()
const onChange = ([startTime]) => {
    choiceDate.value = startTime
}
const calendarChange = ([startTime]) => {
    choiceDate.value = startTime
}

// 设置默认打开月份
const defaultMonth = computed(() => {
    let day = moment().year() + '/' + selectedItem.value.effectiveMonth + '/01'
    return [moment(new Date(day)).format('YYYY-MM-DD')]
})
// 设置日期范围
const disabledDate = (current) => {
    const year = moment().year()
    const month = selectedItem.value.effectiveMonth
    const firstDayOfMonth = new Date(year, month - 1, 1)
    const lastDayOfMonth = new Date(year, month, 0)
    return (
        current.valueOf() < firstDayOfMonth.getTime() ||
        current.valueOf() > lastDayOfMonth.getTime()
    )
}

const onDeleteItem = (item) => {
    specialList.value = specialList.value.filter((ite) => ite !== item)
}
const onAddItem = () => {
    specialList.value.push({
        specialTemplateId: undefined,
        rangeDate: [],
    })
}
const onSpecialClose = () => {
    specialList.value = [
        {
            specialTemplateId: undefined,
            rangeDate: [],
        },
    ]
    specialVisible.value = false
}

const onDistributeClose = () => {
    onDistributeVisible.value = false
    formRef.value.resetFields()
}
// 路由跳出时
onBeforeRouteLeave((to, from, next) => {
    if (props.hasChanged) {
        ElMessageBox.confirm('当前页面有内容尚未保存，是否确认离开?', '提示', {
            cancelButtonText: '否',
            confirmButtonText: '是',
            roundButton: true,
            confirmButtonClass: 'confirm-btn1',
        })
            .then(() => {
                next()
                emit('updateChanged', false)
            })
            .catch(() => {
                // catch error
            })
    } else {
        next()
    }
})
</script>

<style lang="less" scoped>
.months {
    max-width: 1440px;
}

.run-mode {
    :deep(
            .ant-select-disabled.ant-select:not(.ant-select-customize-input)
                .ant-select-selector
        ) {
        cursor: pointer;
        background: transparent;
        color: theme('colors.title');
        border: none;
    }

    :deep(.ant-select-disabled .ant-select-arrow) {
        display: none;
    }

    :deep(
            .ant-select-single:not(.ant-select-customize-input)
                .ant-select-selector
        ) {
        padding: 0;
    }
}

.add-title {
    margin-bottom: 4px;
    color: var(--themeColor);
    text-align: left;
}

:deep(.ant-calendar-range-picker-input) {
    text-align: left;
    width: 84px;
}

:deep(.ant-popover-inner-content) {
    width: 240px;
}

.form {
    :deep(.el-date-editor .el-range-input) {
        width: 90px;
    }

    :deep(.el-date-editor .el-range-separator) {
        flex: initial;
    }

    :deep(.el-date-editor .el-range__close-icon) {
        margin-left: auto;
    }
}
:deep(.ant-select-selection-item) {
    color: var(--text-100);
}
</style>

<style lang="less">
.ant-select-dropdown {
    z-index: 9999 !important;
}
.confirm-btn1 {
    background-color: #fff;
    color: var(--themeColor);
}
</style>
