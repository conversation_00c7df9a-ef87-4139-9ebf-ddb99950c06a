<template>
    <div>
        <el-dialog :model-value="visible" title="" width="1200">
            <template #title>
                <div class="flex justify-between items-center">
                    <div>{{ isEdit ? '编辑电价模版' : '新建电价模版' }}</div>
                    <div>
                        <a-select
                            v-if="!isEdit"
                            v-model:value="selectedModel"
                            placeholder="请选择参照模版"
                            class="w-40"
                            @change="handleChange"
                        >
                            <a-select-option
                                v-for="item in tempList"
                                :key="item.id"
                                :value="item.id"
                                >{{ item.name }}</a-select-option
                            >
                        </a-select>
                    </div>
                </div>
            </template>
            <div class="addTemp pt-6 pb-6">
                <a-form
                    :validateOnRuleChange="false"
                    :model="formState"
                    ref="formRef"
                    labelAlign="left"
                    :rules="rules"
                    hideRequiredMark
                >
                    <div class="flex justify-center items-center gap-x-5">
                        <a-form-item label="模版名称" name="name" class="mb-0">
                            <a-input
                                v-model:value="formState.name"
                                class="w-40"
                                :maxlength="20"
                            ></a-input>
                        </a-form-item>
                    </div>
                    <a-divider></a-divider>
                    <div class="mx-auto" style="width: 628px">
                        <div class="text-center">新建电价模版</div>
                        <div class="text-secondar-text dark:text-60-dark mt-4">
                            <div class="flex flex-box leading-6">
                                <div
                                    class="flex-items-time flex justify-between items-center"
                                >
                                    <div class="w-30 text-center">开始时间</div>
                                    <div>{{ ' ' }}</div>
                                    <div class="w-30 text-center">结束时间</div>
                                </div>
                                <div class="flex-items-operation"></div>
                                <div class="flex-items-power">
                                    <div class="text-center flex-1">
                                        时段类型
                                    </div>
                                </div>
                                <div class="flex-items-action"></div>
                            </div>
                            <div
                                class="flex flex-box mt-3"
                                v-for="(item, index) in electricSegments"
                                :key="index"
                            >
                                <div
                                    class="flex-items-time flex justify-between items-center"
                                >
                                    <div class="w-30 text-center">
                                        <a-select
                                            v-model:value="item.startTime"
                                            disabled
                                            class="w-full text-left"
                                            placeholder="请选择"
                                        >
                                            <a-select-option
                                                v-for="item in item.startTimeList"
                                                :key="item"
                                                :value="item"
                                                >{{ item }}</a-select-option
                                            >
                                        </a-select>
                                    </div>
                                    <div>--</div>
                                    <div class="w-30 text-center">
                                        <a-select
                                            v-model:value="item.endTime"
                                            :disabled="
                                                electricSegments.length - 1 >
                                                index
                                            "
                                            class="w-full text-left"
                                            placeholder="请选择"
                                        >
                                            <a-select-option
                                                v-for="item in item.endTimeList"
                                                :key="item"
                                                :value="item"
                                                >{{ item }}</a-select-option
                                            >
                                        </a-select>
                                    </div>
                                </div>
                                <div class="flex-items-operation">
                                    <div>执行</div>
                                </div>
                                <div class="flex-items-strategy">
                                    <a-select
                                        v-model:value="item.segmentType"
                                        class="w-40"
                                        placeholder="请选择"
                                    >
                                        <a-select-option
                                            v-for="item in segmentTypes"
                                            :key="item.value"
                                            :value="item.value"
                                            >{{ item.label }}</a-select-option
                                        >
                                    </a-select>
                                </div>
                                <div>
                                    <div v-if="item.endTime == '24:00'">
                                        <div
                                            class="flex-items-action opacity-50 cursor-not-allowed"
                                        >
                                            +
                                        </div>
                                    </div>
                                    <div v-else>
                                        <div
                                            class="flex-items-action cursor-pointer"
                                            v-if="
                                                index ==
                                                electricSegments.length - 1
                                            "
                                            @click="onAdd(item, index)"
                                        >
                                            <div class="select-none">+</div>
                                        </div>
                                        <div
                                            class="flex-items-action cursor-pointer"
                                            v-else-if="
                                                index ==
                                                electricSegments.length - 2
                                            "
                                            @click="onDelete(item, index)"
                                        >
                                            <div class="select-none">x</div>
                                        </div>
                                        <div
                                            v-else
                                            class="flex-items-action cursor-pointer opacity-50"
                                        >
                                            <div
                                                class="select-none cursor-not-allowed"
                                            >
                                                x
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <a-divider></a-divider>
                    <div class="mx-auto" style="width: 735px">
                        <div class="text-center">电价配置</div>
                        <div>
                            <div class="flex setting text-center mb-4">
                                <div
                                    class="setting-title text-secondar-text dark:text-60-dark text-left"
                                >
                                    时间段类型
                                </div>
                                <div class="data">尖</div>
                                <div class="data">峰</div>
                                <div class="data">平</div>
                                <div class="data">谷</div>
                                <div class="data">深谷</div>
                            </div>

                            <div class="flex setting">
                                <div
                                    class="setting-title text-secondar-text dark:text-60-dark pb-6"
                                >
                                    买入价格（元/kWh）
                                </div>
                                <div
                                    class="data"
                                    v-for="(val, key) in buyObj"
                                    :key="key"
                                >
                                    <a-form-item label="" :name="key">
                                        <a-input-number
                                            v-model:value="formState[key]"
                                            :min="0"
                                            :max="50"
                                            :step="0.0001"
                                            :precision="4"
                                            background="#fff"
                                        ></a-input-number>
                                    </a-form-item>
                                </div>
                            </div>
                            <div class="flex setting">
                                <div
                                    class="setting-title text-secondar-text dark:text-60-dark pb-6"
                                >
                                    <span>卖出价格（元/kWh）</span>
                                </div>
                                <div
                                    class="data"
                                    v-for="(val, key) in saleObj"
                                    :key="key"
                                >
                                    <a-form-item label="" :name="key">
                                        <a-input-number
                                            v-model:value="formState[key]"
                                            :min="0"
                                            :max="50"
                                            :step="0.0001"
                                            :precision="4"
                                            background="#fff"
                                        ></a-input-number>
                                    </a-form-item>
                                </div>
                            </div>
                        </div>
                    </div>
                </a-form>
            </div>
            <template #footer>
                <div class="flex justify-center items-center gap-x-3">
                    <el-button plain round @click="onClose">
                        <span>{{ $t('common_guanbi') }}</span>
                    </el-button>
                    <el-button plain round type="primary" @click="onSave"
                        >保存</el-button
                    >
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import {
    reactive,
    ref,
    getCurrentInstance,
    onMounted,
    toRaw,
    computed,
    nextTick,
    watch,
} from 'vue'
import { useRoute } from 'vue-router'
import api from '@/apiService/strategy'
import { fullTimePeriod, modelStatusOptions, segmentTypes } from '../../util'
import _cloneDeep from 'lodash/cloneDeep'

const { proxy } = getCurrentInstance()
const route = useRoute()
const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    tempList: {
        type: Array,
        default: () => [],
    },
    isEdit: {
        type: Boolean,
        default: false,
    },
    detail: {
        type: Object,
        default: () => ({}),
    },
})
const selectedModel = ref(null)
const formRef = ref(null)
const emit = defineEmits(['update', 'onClose'])

const electricSegments = ref([
    {
        startTime: '00:00',
        endTime: undefined,
        segmentType: undefined,
        endTimeList: fullTimePeriod,
    },
])

const formState = reactive({
    sharpPrice: undefined,
    peakPrice: undefined,
    normalPrice: undefined,
    valleyPrice: undefined,
    lowestValleyPrice: undefined,
    sharpSalePrice: undefined,
    peakSalePrice: undefined,
    normalSalePrice: undefined,
    valleySalePrice: undefined,
    lowestValleySalePrice: undefined,
})

const rules = ref({
    name: [
        {
            required: true,
            message: '请填写模版名称',
        },
        {
            max: 20,
            message: '模版名称不能超过20个字符', // 这个不对啊，我想点击按钮前不要有校验。
        },
    ],
})
const buyObj = reactive({
    sharpPrice: undefined,
    peakPrice: undefined,
    normalPrice: undefined,
    valleyPrice: undefined,
    lowestValleyPrice: undefined,
})
const saleObj = reactive({
    sharpSalePrice: undefined,
    peakSalePrice: undefined,
    normalSalePrice: undefined,
    valleySalePrice: undefined,
    lowestValleySalePrice: undefined,
})

const handleChange = async () => {
    let res = await api.getElecPriceTemplateDetail({
        id: selectedModel.value,
        stationId: route.query.stationId,
    })
    Object.keys(res.data.data).forEach((key) => {
        formState[key] = res.data.data[key]
    })
    nextTick(() => {
        electricSegments.value = res.data.data.electricSegments
    })
}

const onAdd = (item, index) => {
    if (!electricSegments.value[index].startTime) {
        proxy.$message.error('请选择开始时间')
    } else if (!electricSegments.value[index].endTime) {
        proxy.$message.error('请选择结束时间')
    } else if (!(electricSegments.value[index].segmentType >= 0)) {
        proxy.$message.error('请选择时段类型')
    } else {
        const options = _cloneDeep(fullTimePeriod)
        let StartIndex = fullTimePeriod.findIndex((i) => i == item.endTime)
        electricSegments.value.push({
            startTime: electricSegments.value[index].endTime,
            endTime: '',
            segmentType: undefined,
            endTimeList: options.splice(StartIndex + 1),
        })
    }
}

const onDelete = (item, index) => {
    // 判断是否是最后一个x，如果是 ，则删除最后一条数据
    if (index != electricSegments.value.length - 2) {
        return
    } else {
        electricSegments.value.splice(index + 1, 1)
        let StartIndex = fullTimePeriod.findIndex(
            (i) =>
                i ==
                electricSegments.value[electricSegments.value.length - 1]
                    .startTime
        )
        const options = _cloneDeep(fullTimePeriod)
        electricSegments.value[electricSegments.value.length - 1][
            'endTimeList'
        ] = options.splice(StartIndex + 1)
    }
}

const getPricePropertyName = (segmentType) => {
    switch (segmentType) {
        case 1:
            return 'sharpPrice'
        case 2:
            return 'peakPrice'
        case 3:
            return 'normalPrice'
        case 4:
            return 'valleyPrice'
        case 5:
            return 'lowestValleyPrice'
        default:
            return null
    }
}
const getPricePropertyNameSale = (segmentType) => {
    switch (segmentType) {
        case 1:
            return 'sharpSalePrice'
        case 2:
            return 'peakSalePrice'
        case 3:
            return 'normalSalePrice'
        case 4:
            return 'valleySalePrice'
        case 5:
            return 'lowestValleySalePrice'
        default:
            return null
    }
}
const sortAndFilterArray = (arr, order) => {
    return arr
        .filter((item) => item !== null) // 去除null值
        .sort((a, b) => order.indexOf(a) - order.indexOf(b)) // 根据priceOrder进行排序
}

const getName = (priceOrder, flag) => {
    if (flag == 'buy') {
        const arr = electricSegments.value.map((item) => {
            return getPricePropertyName(item.segmentType)
        })
        return sortAndFilterArray([...new Set(arr)], priceOrder)
    } else {
        const arr = electricSegments.value.map((item) => {
            return getPricePropertyNameSale(item.segmentType)
        })
        return sortAndFilterArray([...new Set(arr)], priceOrder)
    }
}

const isPricesDescending = (sortedArray, formState, flag) => {
    if (sortedArray.length <= 1) {
        if (formState[sortedArray[0]]) {
            return true
        } else {
            proxy.$message.error('选中时段价格不能为空')
            return false
        }
    } else {
        for (let i = 0; i < sortedArray.length - 1; i++) {
            const currentPrice = formState[sortedArray[i]]
            const nextPrice = formState[sortedArray[i + 1]]
            // 如果当前价格小于等于下一个价格，返回false
            if (!currentPrice || !nextPrice) {
                proxy.$message.error('选中时段价格不能为空')
                return false
            } else if (currentPrice <= nextPrice) {
                proxy.$message.error(
                    flag == 1 ? '买入价格区间错误!' : '卖出价格区间错误!'
                )
                return false
            }
        }
        return true // 如果所有价格都按照顺序递减，则返回true
    }
}

const onSave = async () => {
    if (
        JSON.stringify(oldFormState.value) == JSON.stringify(formState) &&
        JSON.stringify(oldElectricSegments.value) ==
            JSON.stringify(electricSegments.value)
    ) {
        proxy.$message.success('保存成功')
        return
    }
    const priceOrder = [
        'sharpPrice',
        'peakPrice',
        'normalPrice',
        'valleyPrice',
        'lowestValleyPrice',
    ]
    const priceOrderSale = [
        'sharpSalePrice',
        'peakSalePrice',
        'normalSalePrice',
        'valleySalePrice',
        'lowestValleySalePrice',
    ]

    const types = electricSegments.value.map((item) => item.segmentType)
    const setTypes = [...new Set(types)].sort()
    // 验证是否配置了峰，谷，平时段
    if (![2, 3, 4].every((num) => types.includes(num))) {
        proxy.$message.error('峰，谷，平时段必须配置！')
        return
    }
    let len = electricSegments.value.length
    let last = electricSegments.value[len - 1]
    // 验证时间段是否合法
    if (last.endTime != '24:00') {
        proxy.$message.error('最后一个时间段必须为24:00')
        return false
    } else if (!last.segmentType) {
        proxy.$message.error('时段类型不能为空')
        return false
    }
    // 验证选中的阶段中的价格是否按照顺序递减
    const sortedArray = getName(priceOrder, 'buy')
    const sortedArraySale = getName(priceOrderSale, 'sale')
    if (!isPricesDescending(sortedArray, formState, 1)) {
        return
    }
    if (!isPricesDescending(sortedArraySale, formState, 2)) {
        return
    }

    if (props.isEdit) {
        formRef.value.validate().then(async () => {
            let params = {
                ...toRaw(formState),
                stationId: route.query.stationId,
                electricSegments: electricSegments.value.map((i) => {
                    return {
                        startTime: i.startTime,
                        endTime: i.endTime,
                        segmentType: i.segmentType,
                    }
                }),
                id: props.detail.id,
            }
            let res = await api.updateElecPriceTemplate(params)
            if (res.data.data) {
                onClose()
                emit('update')
                // await getData()
                proxy.$message.success('更新成功')
            } else {
                proxy.$message.error('更新失败')
            }
        })
    } else {
        formRef.value.validate().then(async () => {
            let params = {
                ...toRaw(formState),
                stationId: route.query.stationId,
                electricSegments: electricSegments.value.map((i) => {
                    return {
                        startTime: i.startTime,
                        endTime: i.endTime,
                        segmentType: i.segmentType,
                    }
                }),
            }
            await api.addElecPriceTemplate(params)
            onClose()
            emit('update')
            // await getData()
            proxy.$message.success('创建成功')
        })
    }
}
const onClose = () => {
    formRef.value.resetFields()
    electricSegments.value = [
        {
            startTime: '00:00',
            endTime: undefined,
            segmentType: undefined,
            endTimeList: fullTimePeriod,
        },
    ]
    selectedModel.value = null
    emit('onClose')
}
const oldFormState = ref({})
const oldElectricSegments = ref([])
const setData = async () => {
    Object.keys(props.detail).forEach((key) => {
        formState[key] = props.detail[key]
    })
    nextTick(() => {
        electricSegments.value = props.detail.electricSegments
        let StartIndex = fullTimePeriod.findIndex(
            (i) =>
                i ==
                electricSegments.value[electricSegments.value.length - 1]
                    .startTime
        )
        const options = _cloneDeep(fullTimePeriod)
        electricSegments.value[electricSegments.value.length - 1][
            'endTimeList'
        ] = options.splice(StartIndex + 1)
        setTimeout(() => {
            oldElectricSegments.value = _cloneDeep(electricSegments.value)
            oldFormState.value = _cloneDeep(formState)
        }, 200)
    })
}
watch(
    () => props.visible,
    (val) => {
        if (val && props.isEdit) {
            setData()
        }
    }
)
</script>

<style lang="less" scoped>
.addTemp {
    :deep(
            .ant-select-disabled.ant-select:not(.ant-select-customize-input)
                .ant-select-selector
        ) {
        background: #fff;
    }

    :deep(.ant-input-number-disabled) {
        background: #fff;
    }

    .flex-box {
        align-items: center;
        justify-content: space-between;

        .flex-items-time {
            width: 300px;
            display: flex;
        }

        .flex-items-operation {
            width: 40px;
            background: rgba(149, 158, 195, 0.1);
            color: #959ec3;
            font-size: 12px;
            line-height: 24px;
            text-align: center;
            border-radius: 2px;
        }

        .flex-items-strategy {
            width: 160px;
        }

        .flex-items-power {
            width: 185px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .flex-items-action {
            width: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .setting {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;

        .setting-title {
            width: 132px;
        }

        .data {
            width: 100px;
        }
    }
}

:deep(.ant-modal-wrap) {
    z-index: 2022;
}

.ant-modal-wrap {
    z-index: 2022;
}
</style>
