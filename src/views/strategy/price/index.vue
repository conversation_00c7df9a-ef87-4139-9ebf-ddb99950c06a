<template>
    <div class="px-4">
        <div
            class="flex mb-5 items-center justify-between pb-4 border-b border-border"
        >
            <div class="flex models">
                <div
                    class="model-item"
                    :class="{ active: tabActiveKey == item.key }"
                    @click="changeTabs(item)"
                    v-for="item in models"
                    :key="item.key"
                >
                    {{ item.title }}
                </div>
            </div>
        </div>
        <div class="content">
            <div class="text-center" v-if="tabActiveKey == 'setting'">
                <setting
                    :hasChanged="hasChanged"
                    @updateChanged="updateChanged"
                />
            </div>
            <div class="" v-if="tabActiveKey == 'temp'">
                <temp />
            </div>
        </div>
    </div>
    <el-dialog v-model="modalVisible" title="提示：" width="500">
        <span>当前页面有内容尚未保存，是否确认离开？</span>
        <template #footer>
            <div class="dialog-footer">
                <el-button round @click="onCancel" class="mr-3">否</el-button>
                <el-button plain round type="primary" @click="onConfirm">
                    是
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import api from '@/apiService/strategy'
import { onMounted, reactive, ref, getCurrentInstance } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import setting from './setting.vue'
import temp from './temp.vue'
const { proxy } = getCurrentInstance()
const router = useRouter()
const route = useRoute()
const goRouter = () => {
    router.go(-1)
}
const tabActiveKey = ref('setting')
const models = ref([
    {
        title: '电价配置',
        key: 'setting',
    },
    {
        title: '电价模版',
        key: 'temp',
    },
])
const referenceSite = ref('1')
const referenceSites = ref([
    {
        title: '站点1',
        key: '1',
    },
    {
        title: '站点2',
        key: '2',
    },
    {
        title: '站点3',
        key: '3',
    },
])
const hasChanged = ref(false)
const updateChanged = (e) => {
    hasChanged.value = e
}
const modalVisible = ref(false)
const changeItem = ref('setting')
const changeTabs = (item) => {
    if (item.key == tabActiveKey.value) {
        return
    }
    if (hasChanged.value) {
        modalVisible.value = true
        changeItem.value = item.key
    } else {
        tabActiveKey.value = item.key
        changeItem.value = item.key
    }
}
const onCancel = () => {
    modalVisible.value = false
}
const onConfirm = async () => {
    tabActiveKey.value = changeItem.value
    hasChanged.value = false // 恢复默认
    modalVisible.value = false
}
// 手动
const getData = async () => {
    // await api.getElecPriceTemplateList({
    //     stationId: route.query.stationId,
    // })
}
const createTemp = () => {}
onMounted(async () => {
    await getData()
})
</script>

<style lang="less" scoped>
.models {
    // background: rgba(34, 34, 34, 0.04);
    border: 1px solid var(--border);
    border-radius: 4px;
    padding: 8px;
    position: relative;

    &::before {
        display: block;
        content: '';
        // border-bottom: 4px solid rgba(34, 34, 34, 0.04);
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        position: absolute;
        left: 110px;
        bottom: 100%;
    }

    .model-item {
        font-size: 14px;
        line-height: 24px;
        text-align: center;
        padding: 8px 16px;
        background: transparent;
        cursor: pointer;
        transition: all 0.2s;

        &.active {
            background: #fff;
            color: var(--themeColor);
        }
    }
}
.dark {
    .models .model-item {
        color: var(--text-100);
    }
    .models .model-item.active {
        background-color: var(--bg-f5);
        color: var(--themeColor);
    }
}
.tactics-tip {
    background: rgba(22, 119, 255, 0.1);
    color: #1677ff;
    border-radius: 2px;
    font-size: 12px;
}

:deep(.ant-switch-checked) {
    background-color: var(--themeColor);
}

:deep(.ant-form-item) {
    margin-bottom: 12px;
}

:deep(.ant-form-item-label) {
    width: 72px !important;
}

:deep(.ant-form-item-label > label) {
    color: theme('colors.secondar-text');
}
:deep(.ant-popover-inner) {
    background-color: transparent !important;
}
.ant-popover-inner {
    background-color: transparent !important;
}
</style>
