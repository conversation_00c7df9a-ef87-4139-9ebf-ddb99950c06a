<template>
    <a-spin :spinning="loading">
        <div class="relative">
            <div class="absolute -top-20 right-0">
                <el-button plain round type="primary" @click="createTemp">创建模版</el-button>
            </div>
            <div>
                <mw-table :dataSource="dataSource" :columns="columns" :hasPage="true"
                    :pageConfig="{ changePage, paginationProps }" :customRow="Rowclick" :rowKey="(record) => record.id"
                    @change="onTableChange" :showRefresh="false" class="text-sm">
                    <template #name="{ text }">
                        <div class="overflow" style="max-width: 328px">
                            {{ text || '-' }}
                        </div>
                    </template>
                    <template #useStationCount="{ record }">
                        <dictionary :statusOptions="modelStatusOptions" :value="record.useStationCount > 0"
                            :color="'color'" />
                        <span class="align-middle">{{
                            record.useStationCount > 0
                                ? '（' + record.useStationCount + '）'
                                : ''
                        }}</span>
                    </template>
                    <template #creatorName="{ text }">
                        <div class="overflow" style="max-width: 328px">
                            {{ text || '-' }}
                        </div>
                    </template>
                    <template #createTime="{ record }">
                        <div>
                            {{ record.createTime }}
                        </div>
                    </template>
                </mw-table>
            </div>
            <add-model v-model:visible="addVisible" :tempList="allTempList" @onClose="onClose" @update="onUpdate" />
            <temp-detail v-model:visible="detailVisible" @onClose="detailVisible = false"
                v-model:selectedTempId="selectedTempId" @update="onUpdate"></temp-detail>
        </div>
    </a-spin>
</template>

<script setup>
import { usePagenation } from '@/common/setup'
import {
    reactive,
    ref,
    getCurrentInstance,
    onMounted,
    toRaw,
    computed,
} from 'vue'
import api from '@/apiService/strategy'
import Dictionary from '@/components/table/dictionary.vue'
import { fullTimePeriod, modelStatusOptions, segmentTypes } from '../util'
import _cloneDeep from 'lodash/cloneDeep'
import tempDetail from './tempDetail.vue'
import { useRoute } from 'vue-router'
import addModel from './components/addModel.vue'

const { proxy } = getCurrentInstance()
const route = useRoute()

const columns = [
    {
        title: '模版名称',
        dataIndex: 'name',
        key: 'name',
        slots: {
            customRender: 'name',
        },
        align: 'left',
        width: '35%',
    },
    {
        title: '状态',
        dataIndex: 'useStationCount',
        key: 'useStationCount',
        slots: {
            customRender: 'useStationCount',
        },
        align: 'left',
        width: '20%',
    },
    {
        title: '创建人',
        dataIndex: 'creatorName',
        key: 'creatorName',
        slots: {
            customRender: 'creatorName',
        },
        align: 'center',
        width: '20%',
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        key: 'createTime',
        slots: {
            customRender: 'createTime',
        },
        align: 'right',
        width: '25%',
    },
]
const dataSource = ref([])
const addVisible = ref(false)
const loading = ref(false)
const getData = async () => {
    loading.value = true
    //
    const params = {
        current: pageParam.value.current,
        size: pageParam.value.size,
    }
    let res = await api.getElecPriceTemplateList({
        ...params,
        stationId: route.query.stationId,
    })
    dataSource.value = res.data.data.records
    paginationProps.value.total = res.data.data.total
    loading.value = false
}
const { paginationProps, changePage, onTableChange, refresh, pageParam } =
    usePagenation(getData)
const detailVisible = ref(false)
const selectedTempId = ref()
const Rowclick = (record) => {
    return {
        onClick: async (event) => {
            // 获取详情，打开弹窗
            selectedTempId.value = record.id
            detailVisible.value = true
        }, // 点击行
    }
}
const requestFlag = ref(true)
const createTemp = async () => {
    addVisible.value = true
    // if (requestFlag.value) {
    let allRes = await api.getElecPriceTemplateList({
        current: 1,
        size: 9999,
        stationId: route.query.stationId,
    })
    allTempList.value = allRes.data.data.records
    // requestFlag.value = false
    // }
}
const onClose = () => {
    addVisible.value = false
}

const formRef = ref()
const validSegments = () => { }

const selectedModel = ref(null)
const allTempList = ref([])
const name = ref('')
const onUpdate = async () => {
    await getData()
}
onMounted(async () => {
    await getData()
})
</script>

<style lang="less" scoped></style>
