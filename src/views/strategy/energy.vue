<template>
    <a-spin :spinning="loading">
        <div class="px-4 text-title dark:text-title-dark">
            <div
                class="flex mb-3 items-center justify-between pb-4 border-b border-border"
            >
                <div class="flex models">
                    <div
                        class="model-item"
                        :class="{ active: tabActiveKey == item.value }"
                        @click="changeTabs(item)"
                        v-for="item in runModes"
                        :key="item.value"
                    >
                        {{ item.name }}
                    </div>
                </div>
                <div class="leading-5">
                    <span class="inline-block leading-5 h-5"
                        >当前执行策略：</span
                    >
                    <!-- tactics-tip需要判断是否有值 -->
                    <span
                        class="rounded-sm px-2 inline-block"
                        :style="{
                            fontSize: '12px',
                            background:
                                statusObj.backGroundColor || 'var(--bg-f5)',
                            color: statusObj.color || 'var(--text-100)',
                            height: '20px',
                            lineHeight: '20px',
                        }"
                    >
                        {{ currentData }}
                    </span>
                    <span class="ml-6 leading-5 h-5 inline-block">
                        更新时间：{{
                            detailInfo.effectTime
                                ? moment(detailInfo.effectTime).format(
                                      'YYYY-MM-DD HH:mm:ss'
                                  )
                                : '-'
                        }}
                    </span>
                    <span class="ml-6 leading-5 h-5 inline-block"
                        >当前模式运行时间：{{ runTime }}
                    </span>
                    <span
                        class="ml-2"
                        v-if="detailInfo.onlineStatus == 0"
                        style="
                            background: rgba(253, 117, 11, 0.1);
                            color: #fd750b;
                            padding: 0 8px;
                            font-size: 12px;
                            linh-height: 20px;
                            height: 20px;
                            display: inline-block;
                        "
                    >
                        设备已离线
                    </span>
                    <span
                        class="ml-6"
                        :class="
                            detailInfo.onlineStatus == 0
                                ? 'opacity-50 cursor-not-allowed'
                                : ''
                        "
                        >运行模式：
                        <a-select
                            v-model:value="runMode"
                            placeholder="请选择运行模式"
                            class="w-40"
                            :disabled="detailInfo.onlineStatus == 0"
                            @change="runModeChange"
                        >
                            <a-select-option
                                v-for="item in runModes"
                                :key="item.value"
                                :value="item.value"
                                >{{ item.name }}</a-select-option
                            >
                        </a-select>
                    </span>
                </div>
            </div>
            <div class="content">
                <div class="text-center" v-if="tabActiveKey == 1">
                    <manual
                        :data="detailInfo"
                        :containerList="containerList"
                        :containerNo="containerNo"
                        @update="update"
                        @onContainerChange="onContainerChange"
                    />
                </div>
                <div class="" v-if="tabActiveKey == 2">
                    <plan @update="update" />
                </div>
                <div class="" v-if="tabActiveKey == 3">
                    <ai />
                </div>
            </div>
        </div>
    </a-spin>
    <!--  :before-close="handleClose" -->
    <el-dialog v-model="modalVisible" title="提示：" width="500">
        <span>是否确定将运行模式调整为「{{ newMode?.name }}」？</span>
        <template #footer>
            <div class="dialog-footer inline-flex items-center gap-x-3">
                <el-button plain round @click="cancelChangeRunMode"
                    >取消</el-button
                >
                <el-button
                    plain
                    round
                    type="primary"
                    @click="confrimChangeRunMode"
                    :loading="btnLoading"
                >
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import {
    onMounted,
    reactive,
    ref,
    computed,
    getCurrentInstance,
    nextTick,
} from 'vue'
import { useRouter, useRoute } from 'vue-router'
import moment from 'moment'
import manual from './components/manual.vue'
import plan from './components/plan.vue'
import ai from './components/ai.vue'
import api from '@/apiService/strategy'
import apiService from '@/apiService/device'
import { chargeStatus } from '@/views/device/const.js'
import Dictionary from '@/components/table/dictionary.vue'
import { runModes, runStatuss, gridStatuss } from './util'
import store from '@/store'
import { useStore } from 'vuex'
const { proxy } = getCurrentInstance()
const router = useRouter()
const route = useRoute()
// const store = useStore()
const goRouter = () => {
    router.go(-1)
}
const tabActiveKey = ref(1)
const models = ref([
    {
        title: '手动模式',
        key: 1,
    },
    {
        title: '计划模式',
        key: 2,
    },
    // {
    //     title: 'AI模式',
    //     key: 3,
    // },
])
const changeTabs = (item) => {
    store.commit('strategy/setMenuCurrent', item.value)
    tabActiveKey.value = item.value
    if (item.value == 1) {
        loading.value = true
        setTimeout(() => {
            loading.value = false
        }, 150)
    }
}
// 手动

const runMode = ref(0)

const statusObj = computed(() => {
    const item =
        runModes.find((s) => s.value === detailInfo.value.runMode) || {}
    return item
})
const runTime = computed(() => {
    if (detailInfo.value.onlineStatus == 0) {
        return '-'
    }
    // 使用当前时间减去effectTime:"1970-01-01 08:00:00"
    const backendDate = moment(detailInfo.value.effectTime)
    // 获取当前日期和时间
    const currentDate = moment(new Date())
    // 计算日期差异
    const diffDuration = moment.duration(currentDate.diff(backendDate)) // 注意这里取反
    // 获取天数、小时数和分钟数
    const daysDiff = Math.floor(diffDuration.asDays())
    const hoursDiff = diffDuration.hours()
    const minutesDiff = diffDuration.minutes()

    return `${daysDiff}天${hoursDiff}小时${minutesDiff}分钟`
})
const currentData = computed(() => {
    if (detailInfo.value.onlineStatus == 0) {
        return '-'
    }
    if (detailInfo.value.runMode == 1) {
        return (
            (detailInfo.value.runStatus == 0 ? '待机' : '开机') +
            ' · ' +
            (detailInfo.value.gridStatus == 0 ? '离网' : '并网') +
            ' · ' +
            (chargeStatus[detailInfo.value.chargeStatus] +
                ' · ' +
                detailInfo.value.power +
                'kW')
        )
    } else if (detailInfo.value.runMode == 2) {
        // 计划模式下
        return detailInfo.value.currentStrategyName || '-'
    } else if (detailInfo.value.runMode == 3) {
        return 'AI智能均衡模式'
    } else {
        return '-'
    }
})
const update = async () => {
    await getData()
}
const detailInfo = ref({})
const loading = ref(false)
const getData = async () => {
    // const stationId = route.query.stationNo
    try {
        let res = await api.getRunMode({
            stationId: route.query.stationId,
            containerNo: containerNo.value,
        })
        if (res.data.data) {
            detailInfo.value = res.data.data
            detailInfo.value.power = res.data.data?.power || 0
            store.commit('strategy/setRunModeInfo', detailInfo.value)
            runMode.value = res.data.data.runMode || 1
            oldMode.value = res.data.data.runMode
            tabActiveKey.value = runMode.value
        } else {
            nextTick(() => {
                setTimeout(() => {
                    changeTabs(runModes[1])
                }, 300)
            })
        }
    } catch (error) {
        nextTick(() => {
            changeTabs(runModes[1])
        })
        loading.value = false
        // tabActiveKey.value = 2
    }
}

const modalVisible = ref(false)
const newMode = ref() // 新的运行模式
const oldMode = ref() // 旧的运行模式
const runModeChange = (e) => {
    modalVisible.value = true
    newMode.value = runModes.find((s) => s.value === e)
}
const btnLoading = ref(false)
const confrimChangeRunMode = async () => {
    btnLoading.value = true
    const res = await api.changeStationRunMode({
        stationId: route.query.stationId,
        runMode: newMode.value.value,
    })
    if (res.data.data.length > 0) {
        proxy.$message.error(`机柜编号${res.data.data}切换失败!`)
    }
    btnLoading.value = false
    modalVisible.value = false
}
const cancelChangeRunMode = () => {
    modalVisible.value = false
    runMode.value = oldMode.value
}
const containerNo = ref(null)
const containerList = ref([])
const getContainerList = async () => {
    let res = await apiService.getContainerList({
        stationNo: route.query.stationNo,
    })
    containerList.value = res.data.data
    containerNo.value = res.data.data[0].containerNo
}
const onContainerChange = (e) => {
    containerNo.value = e
    getData()
}
onMounted(async () => {
    //
    loading.value = true

    await getContainerList()
    if (store.state.strategy?.runModeInfo?.runMode > 0) {
        detailInfo.value = store.state.strategy?.runModeInfo
        runMode.value = store.state.strategy?.runModeInfo?.runMode || 0
    } else {
        await getData()
    }
    tabActiveKey.value = runMode.value
    loading.value = false
})
</script>

<style lang="less" scoped>
.models {
    // background: rgba(34, 34, 34, 0.04);
    border: 1px solid var(--border);
    border-radius: 4px;
    padding: 8px;
    position: relative;

    &::before {
        display: block;
        content: '';
        border-bottom: 4px solid var(--border);
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        position: absolute;
        left: 24px;
        bottom: 100%;
    }

    .model-item {
        font-size: 14px;
        line-height: 24px;
        text-align: center;
        padding: 8px 16px;
        background: transparent;
        cursor: pointer;
        transition: all 0.2s;

        &.active {
            background: #fff;
            color: var(--themeColor);
        }
    }
}

.tactics-tip {
    border-radius: 2px;
    font-size: 12px;
}

:deep(.ant-switch-checked) {
    background-color: var(--themeColor);
}

:deep(.ant-form-item) {
    margin-bottom: 12px;
}

:deep(.ant-form-item-label) {
    width: 72px !important;
}

:deep(.ant-form-item-label > label) {
    color: var(--text-60);
}

.dark {
    .models .model-item {
        color: var(--text-100);
    }
    .models .model-item.active {
        background-color: var(--bg-f5);
        color: var(--themeColor);
    }
}
:deep(.ant-checkbox) {
    background-color: var(--input-bg);
    color: var(--input-bg);
}
.ant-checkbox-checked::after {
    background: var(--input-bg);
}
:deep(.ant-checkbox + span) {
    color: var(--text-100);
}
</style>
