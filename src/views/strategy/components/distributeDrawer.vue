<template>
    <el-drawer
        :model-value="visible"
        :size="486"
        :show-close="false"
        @close="onClose"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div><span>能效管理策略分发</span></div>
                <div class="flex items-center gap-x-3">
                    <el-button plain round @click="onClose">{{
                        $t('common_guanbi')
                    }}</el-button>
                    <el-button
                        plain
                        round
                        type="primary"
                        @click="submit"
                        :loading="btnLoading"
                        >立即分发</el-button
                    >
                </div>
            </div>
        </template>
        <div class="text-left">
            <el-form
                ref="formRef"
                :rules="rules"
                :model="formState"
                label-width="auto"
                style="max-width: 600px"
                label-position="left"
            >
                <div class="mb-3 pl-2">分发策略选择</div>
                <el-form-item label="选择策略" prop="strategyIds">
                    <el-select
                        v-model="formState.strategyIds"
                        multiple
                        placeholder="请选择站点策略"
                        filterable
                        no-match-text="无匹配数据"
                    >
                        <el-option
                            v-for="item in strategyStationList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <div class="mb-3 pl-2">分发站点选择</div>
                <el-form-item label="分发站点" prop="stationIds">
                    <el-select
                        v-model="formState.stationIds"
                        multiple
                        placeholder="请选择分发站点"
                        filterable
                        no-match-text="无匹配数据"
                    >
                        <el-option
                            v-for="item in stations"
                            :key="item.id"
                            :label="item.stationName"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
            </el-form>
        </div>
    </el-drawer>
</template>

<script setup>
import { ref, reactive, toRaw, getCurrentInstance, watch, computed } from 'vue'
import api from '@/apiService/strategy'
import apiService from '@/apiService/device'
const emit = defineEmits(['update', 'onClose'])
import { prioritys, weeklys, cycles } from '../util'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

const { proxy } = getCurrentInstance()
const route = useRoute()
const store = useStore()
const props = defineProps({
    visible: Boolean,
    strategyStationList: Array,
    tempList: Array,
})
const strategyIds = ref([])
const stations = ref([])
const stationIds = ref([])
const onClose = () => {
    formRef.value.resetFields()
    emit('onClose')
}
const formRef = ref()
const formState = reactive({
    strategyIds: [],
    stationIds: [],
})
// const treeData = ref([])
const selectedSuppiler = ref()
const activeSystem = computed(() => localStorage.getItem('activeSystem'))
const getData = async () => {
    // let res = await api.getStationList()
    // const {
    //     data: { data, code },
    // } = await apiService.getDeviceTree()
    // const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])
    // const tree = [
    //     {
    //         name: getCompanyInfo?.value?.orgName,
    //         id: getCompanyInfo?.value?.orgId,
    //         children: data || [],
    //     },
    // ]
    // treeData.value = tree
    const page = {
        orgId: '1726907974555729921',
        stationType:
            activeSystem.value == 'car'
                ? 'vehicle_battery'
                : 'energy_storage_cabinet',
    }
    const res = await api.getOrgAndSubOrgStationNameList(page)
    stations.value = res.data.data.filter(
        (item) => item.id != route.query.stationId
    )
}
watch(
    () => props.visible,
    (val) => {
        if (val) {
            getData()
        }
    }
)
const rules = {
    strategyIds: [
        {
            required: true,
            message: '请选择执行策略',
        },
    ],
    stationIds: [
        {
            required: true,
            message: '请选择分发站点',
        },
    ],
}
const btnLoading = ref(false)
const submit = async () => {
    btnLoading.value = true
    formRef.value.validate().then(async (valid) => {
        if (valid) {
            //
            let res = await api.authorizationStation({
                ...toRaw(formState),
            })
            if (res.data.data) {
                proxy.$message.success('操作成功')
                onClose()
                emit('update')
            }
            btnLoading.value = false
        }
        btnLoading.value = false
    })
    btnLoading.value = false
}

const displayRender = ({ labels }) => {
    return labels[labels.length - 1]
}
const filterOption = (input, option) => {
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
const optionsVisible = ref(false)

const showOptions = () => {
    optionsVisible.value = !optionsVisible.value
}
const suppliers = ref([])
</script>

<style lang="less" scoped>
:deep(.ant-form-item-label) {
    width: 72px !important;
}

:deep(.ant-form-item-label > label) {
    color: theme('colors.secondar-text');
}

:deep(.ant-checkbox + span) {
    padding-left: 3px;
    padding-right: 0;
}

:deep(.ant-checkbox-group-item) {
    margin-right: 20px;
    margin-top: 8px;

    &:nth-child(5) {
        margin-right: 0;
    }
}

:deep(.ant-calendar-range-picker-input) {
    text-align: left;
    width: 84px;
}
</style>
