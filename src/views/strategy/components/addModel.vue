<template>
    <a-modal
        :visible="visible"
        width="1200px"
        :footer="null"
        @cancel="onClose"
        :closable="false"
        :bodyStyle="{
            padding: 0,
            height: '675px',
        }"
        :headerStyle="{
            border: 'none',
        }"
        :destroyOnClose="true"
    >
        <template #title>
            <div class="flex justify-between items-center">
                <div class="text-title dark:text-title-dark">
                    {{ isEdit ? '编辑' : '添加策略模版' }}
                </div>
                <div>
                    <a-select
                        v-model:value="currentId"
                        placeholder="请选择参照模版"
                        class="w-40"
                        @change="changeTab"
                    >
                        <a-select-option
                            v-for="item in models"
                            :key="item.id"
                            :value="item.id"
                            >{{ item.name }}</a-select-option
                        >
                    </a-select>
                </div>
            </div>
        </template>
        <div
            class="gap-x-4 overflow-y-auto overflow-x-hidden h-full p-6 pt-0 text-title dark:text-title-dark"
        >
            <div class="addModel">
                <div class="text-center mb-6 font-medium">运行逻辑设定</div>
                <a-form
                    :model="formState"
                    ref="formRef"
                    labelAlign="left"
                    :rules="rules"
                    hideRequiredMark
                >
                    <div class="flex justify-center items-start gap-x-8">
                        <div class="w-60">
                            <a-form-item
                                label="模版名称"
                                name="name"
                                class="mb-3"
                            >
                                <a-input
                                    v-model:value="formState.name"
                                    :maxlength="20"
                                ></a-input>
                            </a-form-item>
                            <a-form-item
                                label="策略类型"
                                name="strategyType"
                                class="mb-3"
                            >
                                <a-select
                                    v-model:value="formState.strategyType"
                                    placeholder="请选择执行策略"
                                    class="w-40"
                                >
                                    <a-select-option
                                        v-for="item in strategyTypes"
                                        :key="item.value"
                                        :value="item.value"
                                        >{{ item.name }}</a-select-option
                                    >
                                </a-select>
                            </a-form-item>
                        </div>
                        <div v-if="isEdit" class="leading-8">
                            <div class="flex mb-3">
                                <div
                                    class="text-secondar-text dark:text-60-dark"
                                >
                                    使用状态：
                                </div>
                                <div class="w-56">
                                    <dictionary
                                        :statusOptions="modelStatusOptions"
                                        :value="
                                            formState.useStations?.length > 0
                                        "
                                        :color="'color'"
                                    />
                                </div>
                            </div>
                            <div class="flex mb-3">
                                <div
                                    class="text-secondar-text dark:text-60-dark"
                                >
                                    使用情况：
                                </div>
                                <div>
                                    <div
                                        class="text-title dark:text-title-dark"
                                        v-for="item in formState.useStations"
                                        :key="item"
                                    >
                                        {{ item.name }}
                                    </div>
                                    <div
                                        v-if="
                                            formState.useStations?.length == 0
                                        "
                                    >
                                        -
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="width: 1162px" class="mx-auto">
                        <div
                            style="width: 880px; margin: 0 auto"
                            class="border border-transparent"
                        >
                            <strategy-chart :data="strategySegments" />
                            <div
                                class="text-secondar-text dark:text-60-dark mt-4"
                            >
                                <div class="flex flex-box leading-6">
                                    <div
                                        class="flex-items-time flex justify-between items-center"
                                    >
                                        <div class="w-30 text-center">
                                            开始时间
                                        </div>
                                        <div></div>
                                        <div class="w-30 text-center">
                                            结束时间
                                        </div>
                                    </div>
                                    <div class="flex-items-operation"></div>
                                    <div
                                        class="flex-items-strategy text-center"
                                    >
                                        控制策略
                                    </div>
                                    <div class="flex-items-power">
                                        <div class="text-center flex-1">
                                            执行功率(kW)
                                        </div>
                                    </div>
                                    <div class="flex-items-action"></div>
                                </div>
                                <div
                                    class="flex flex-box mt-3"
                                    v-for="(item, index) in strategySegments"
                                    :key="index"
                                >
                                    <div
                                        class="flex-items-time flex justify-between items-center"
                                    >
                                        <div class="w-30 text-center">
                                            <a-select
                                                v-model:value="item.startTime"
                                                disabled
                                                class="w-full text-left"
                                                placeholder="请选择"
                                            >
                                                <a-select-option
                                                    v-for="item in item.startTimeList"
                                                    :key="item"
                                                    :value="item"
                                                    >{{ item }}</a-select-option
                                                >
                                            </a-select>
                                        </div>
                                        <div>--</div>
                                        <div class="w-30 text-center">
                                            <a-select
                                                v-model:value="item.endTime"
                                                :disabled="
                                                    strategySegments.length -
                                                        1 >
                                                    index
                                                "
                                                class="w-full text-left"
                                                placeholder="请选择"
                                            >
                                                <a-select-option
                                                    v-for="item in item.endTimeList"
                                                    :key="item"
                                                    :value="item"
                                                    >{{ item }}</a-select-option
                                                >
                                            </a-select>
                                        </div>
                                    </div>
                                    <div class="flex-items-operation">
                                        <div>执行</div>
                                    </div>
                                    <div class="flex-items-strategy">
                                        <a-select
                                            v-model:value="item.workStatus"
                                            class="w-40"
                                            @change="
                                                workStatusChange(item, index)
                                            "
                                            placeholder="请选择"
                                        >
                                            <a-select-option
                                                v-for="item in chargeState"
                                                :key="item.value"
                                                :value="item.value"
                                                >{{
                                                    item.name
                                                }}</a-select-option
                                            >
                                        </a-select>
                                    </div>
                                    <div class="flex-items-power">
                                        <div>预计执行功率:</div>
                                        <a-input-number
                                            v-model:value="item.power"
                                            :min="0"
                                            :max="installedPower"
                                            :step="1"
                                            :precision="0"
                                            background="#fff"
                                            style="width: 88px"
                                            :disabled="!item.workStatus"
                                        ></a-input-number>
                                    </div>
                                    <div>
                                        <div v-if="item.endTime == '24:00'">
                                            <div
                                                class="flex-items-action opacity-50 cursor-not-allowed"
                                            >
                                                +
                                            </div>
                                        </div>
                                        <div v-else>
                                            <div
                                                class="flex-items-action cursor-pointer"
                                                v-if="
                                                    index ==
                                                    strategySegments.length - 1
                                                "
                                                @click="onAdd(item, index)"
                                            >
                                                <div class="select-none">+</div>
                                            </div>
                                            <div
                                                class="flex-items-action cursor-pointer"
                                                v-else-if="
                                                    index ==
                                                    strategySegments.length - 2
                                                "
                                                @click="onDelete(item, index)"
                                            >
                                                <div class="select-none">x</div>
                                            </div>
                                            <div
                                                v-else
                                                class="flex-items-action cursor-pointer opacity-50"
                                            >
                                                <div
                                                    class="select-none cursor-not-allowed"
                                                >
                                                    x
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <a-divider></a-divider>
                    <div
                        class="text-center mb-6 font-medium text-title dark:text-title-dark"
                    >
                        约束参数配置
                    </div>
                    <div
                        class="flex flex-wrap form-flex"
                        :class="
                            formState.strategyType == 0 ? 'form-flex-0' : ''
                        "
                    >
                        <template
                            v-for="(item, index) in formList"
                            :key="index"
                        >
                            <a-form-item
                                :label="item.label"
                                :name="item.name"
                                class="form-flex-item"
                                v-if="
                                    item.auth.includes(formState.strategyType)
                                "
                            >
                                <a-input-number
                                    v-if="item.type == 'number'"
                                    v-model:value="formState[item.name]"
                                    :min="item.min"
                                    :max="item.max"
                                    :step="item.step"
                                />
                                <a-select
                                    v-if="item.type == 'select'"
                                    v-model:value="formState[item.name]"
                                >
                                    <a-select-option
                                        v-for="ite in item.options"
                                        :key="ite.value"
                                        :value="ite.value"
                                        >{{ ite.label }}</a-select-option
                                    >
                                </a-select>
                            </a-form-item>
                        </template>
                    </div>
                </a-form>
            </div>
            <div class="text-center">
                <div class="inline-flex gap-x-3 items-center">
                    <el-button plain round @click="onClose">{{
                        $t('common_guanbi')
                    }}</el-button>
                    <confirm-button
                        v-if="isEdit"
                        :title="'是否确认删除？'"
                        @confirm="onDeleteModel"
                        placement="bottom-end"
                    >
                        <template #reference>
                            <el-button plain round type="primary"
                                >删除</el-button
                            >
                        </template>
                    </confirm-button>

                    <el-button
                        plain
                        round
                        type="primary"
                        @click="onSave"
                        :disabled="formState.beAuthorized == 1 && isEdit"
                        >保存</el-button
                    >
                </div>
            </div>
        </div>
    </a-modal>
</template>

<script setup>
import {
    ref,
    reactive,
    toRaw,
    onMounted,
    computed,
    getCurrentInstance,
    nextTick,
    watch,
} from 'vue'
import { useRoute } from 'vue-router'
import StrategyChart from './strategyChart.vue'
import {
    fullTimePeriod,
    strategyTypes,
    transformerCapProtectOptions,
    backFlowPreventionOptions,
    modelStatusOptions,
} from '../util'
import _cloneDeep from 'lodash/cloneDeep'
import api from '@/apiService/strategy'
import Dictionary from '@/components/table/dictionary.vue'

const { proxy } = getCurrentInstance()
const route = useRoute()
const emit = defineEmits(['update', 'onClose'])
const props = defineProps({
    visible: Boolean,
    isEdit: {
        type: Boolean,
        default: false,
    },
    detail: {
        type: Object,
        default: () => ({}),
    },
    models: {
        type: Array,
        default: () => [],
    },
    installedPower: {
        type: Number,
        default: 0,
    },
})
const oldFormState = ref({})
const oldStrategySegments = ref([])
const setData = async () => {
    Object.keys(props.detail).forEach((key) => {
        formState[key] = props.detail[key]
    })
    nextTick(() => {
        strategySegments.value = props.detail.strategySegments
        let StartIndex = fullTimePeriod.findIndex(
            (i) =>
                i ==
                strategySegments.value[strategySegments.value.length - 1]
                    .startTime
        )
        const options = _cloneDeep(fullTimePeriod)
        strategySegments.value[strategySegments.value.length - 1][
            'endTimeList'
        ] = options.splice(StartIndex + 1)
        setTimeout(() => {
            oldStrategySegments.value = _cloneDeep(strategySegments.value)
            oldFormState.value = _cloneDeep(formState)
        }, 200)
    })
}
watch(
    () => props.visible,
    (val) => {
        if (val && props.isEdit) {
            setData()
        } else {
            Object.keys(formState).forEach((key) => {
                formState[key] = undefined
            })
            formState.strategyType = 0
            formState.backFlowPrevention = 0
            formState.plannedPower = 0
            formState.transformerCapProtect = 0
            strategySegments.value = [
                {
                    startTime: '00:00',
                    endTime: undefined,
                    workStatus: 0,
                    power: 0,
                    endTimeList: fullTimePeriod,
                },
            ]
        }
    }
)
const formList = computed(() => {
    return [
        {
            name: 'socMax',
            label: 'SOC上限（%）',
            type: 'number',
            min: 0,
            max: 100,
            step: 1,
            auth: [0, 1],
            options: null,
        },
        {
            name: 'socMin',
            label: 'SOC下限（%）',
            type: 'number',
            min: 0,
            max: 100,
            step: 1,
            auth: [0, 1],
            options: null,
        },
        {
            name: 'backFlowPrevention',
            label: '防逆流',
            type: 'select',
            min: 0,
            max: 999,
            step: 1,
            auth: [0],
            options: backFlowPreventionOptions,
        },
        {
            auth: [0],
        },
        {
            name: 'gridPowerLimit',
            label: '电网供电功率下限（kW）',
            type: 'number',
            min: 0,
            max: props.installedPower || 215,
            step: 1,
            auth: [0],
            options: null,
        },
        {
            name: 'gridPowerLimitTolerance',
            label: '电网供电功率下限误差（kW）',
            type: 'number',
            min: 0,
            max: 10,
            step: 1,
            auth: [0],
            options: null,
        },
        {
            name: 'batVMax',
            label: '最高电压单体门限（kW）',
            type: 'number',
            min: 2.8,
            max: 3.6,
            step: 0.1,
            auth: [0],
            options: null,
        },

        {
            name: 'batVMin',
            label: '最低电压单体门限（kW）',
            type: 'number',
            min: 2.8,
            max: 3.6,
            step: 0.1,
            auth: [0],
            options: null,
        },
        {
            name: 'transformerCapProtect',
            label: '变压器容量保护',
            type: 'select',
            min: 0,
            max: 999,
            step: 1,
            auth: [0],
            options: transformerCapProtectOptions,
        },

        {
            name: 'transformerCapMax',
            label: '变压器最大容量（kW）',
            type: 'number',
            min: 0,
            max: 9999999,
            step: 1,
            auth: [0],
            options: null,
        },
        {
            name: 'transformerCapRatio',
            label: '容量上限系数（0-100）',
            type: 'number',
            min: 0,
            max: 100,
            step: 1,
            auth: [0],
            options: null,
        },
        {
            name: 'tempMax',
            label: '温度上限（℃）',
            type: 'number',
            min: 0,
            max: 9999,
            step: 1,
            auth: [0, 1],
            options: null,
        },
        {
            name: 'startChargePower',
            label: '开始充电功率（kW）',
            type: 'number',
            min: 0,
            max: props.installedPower || 215,
            step: 1,
            auth: [1],
            options: null,
        },
        {
            name: 'endChargePower',
            label: '结束充电功率（kW）：',
            type: 'number',
            min: 0,
            max: props.installedPower || 215,
            step: 1,
            auth: [1],
            options: null,
        },

        {
            name: 'thresholdDischargePower',
            label: '放电阈值（kW）',
            type: 'number',
            min: 0,
            max: props.installedPower || 215,
            step: 1,
            auth: [1],
            options: null,
        },
        {
            name: 'endThresholdDischargePower',
            label: '放电结束阈值（kW）',
            type: 'number',
            min: 0,
            max: props.installedPower || 215,
            step: 1,
            auth: [1],
            options: null,
        },
        {
            name: 'thresholdDischargePowerSupplement',
            label: '放电结束补放值（kW）',
            type: 'number',
            min: 0,
            max: props.installedPower || 215,
            step: 1,
            auth: [1],
            options: null,
        },
        {
            name: 'chargePowerMax',
            label: '最大充电功率（kW）',
            type: 'number',
            min: 0,
            max: props.installedPower || 215,
            step: 1,
            auth: [0, 1],
            options: null,
        },

        {
            name: 'dischargePowerMax',
            label: '最大放电功率（kW）',
            type: 'number',
            min: 0,
            max: props.installedPower || 215,
            step: 1,
            auth: [0, 1],
            options: null,
        },
        {
            name: 'beforePowerOnTime',
            label: '空调提前开启时间（min）',
            type: 'number',
            min: 0,
            max: 1440,
            step: 1,
            auth: [0, 1],
            options: null,
        },
        {
            name: 'afterPowerOffTime',
            label: '空调延后关闭时间（min）',
            type: 'number',
            min: 0,
            max: 1440,
            step: 1,
            auth: [0, 1],
            options: null,
        },
    ]
})
const formRef = ref()
const formState = reactive({
    name: '',
    strategyType: 0,
    plannedPower: 0,
    socMax: undefined,
    socMin: undefined,
    backFlowPrevention: 0,
    gridPowerLimit: undefined,
    gridPowerLimitTolerance: undefined,
    batVMax: undefined,
    batVMin: undefined,
    transformerCapProtect: 0,
    transformerCapMax: undefined,
    transformerCapRatio: undefined,
    tempMax: undefined,
    chargePowerMax: undefined,
    dischargePowerMax: undefined,
    beforePowerOnTime: undefined,
    afterPowerOffTime: undefined,
    strategySegments: [],
    startChargePower: undefined,
    endChargePower: undefined,
    thresholdDischargePower: undefined,
    endThresholdDischargePower: undefined,
    thresholdDischargePowerSupplement: undefined,
})
const socMaxValidator = async (rule, value) => {
    if (formState.socMax <= formState.socMin) {
        return Promise.reject('SOC上限不能低于SOC下限')
    } else {
        return Promise.resolve()
    }
}
const socMinValidator = async (rule, value) => {
    if (formState.socMax >= formState.socMin) {
        return Promise.reject('SOC下限不能高于SOC上限')
    } else {
        return Promise.resolve()
    }
}

const rules = {
    name: [
        {
            required: true,
            message: '请填写模版名称',
        },
    ],
    socMax: [
        {
            required: true,
            message: '请填写SOC上限',
        },
        {
            validator: socMaxValidator,
        },
    ],
    socMin: [
        {
            required: true,
            message: '请填写SOC下限',
        },
    ],
    gridPowerLimit: [
        {
            required: true,
            message: '请填写电网供电功率下限',
        },
    ],
    gridPowerLimitTolerance: [
        {
            required: true,
            message: '请填写电网供电功率下限容差',
        },
    ],
    batVMax: [
        {
            required: true,
            message: '请填写电池电压上限',
        },
    ],
    batVMin: [
        {
            required: true,
            message: '请填写电池电压下限',
        },
    ],
    transformerCapMax: [
        {
            required: true,
            message: '请填写变压器容量上限',
        },
    ],
    transformerCapRatio: [
        {
            required: true,
            message: '请填写变压器容量比例',
        },
    ],
    tempMax: [
        {
            required: true,
            message: '请填写温度上限',
        },
    ],
    chargePowerMax: [
        {
            required: true,
            message: '请填写充电功率上限',
        },
    ],
    dischargePowerMax: [
        {
            required: true,
            message: '请填写放电功率上限',
        },
    ],
    beforePowerOnTime: [
        {
            required: true,
            message: '请填写提前开启时间',
        },
    ],
    afterPowerOffTime: [
        {
            required: true,
            message: '请填写延迟关闭时间',
        },
    ],
    startChargePower: [
        {
            required: true,
            message: '请填写开始充电功率',
        },
    ],
    endChargePower: [
        {
            required: true,
            message: '请填写结束充电功率',
        },
    ],
    thresholdDischargePower: [
        {
            required: true,
            message: '请填写放电阈值功率',
        },
    ],
    endThresholdDischargePower: [
        {
            required: true,
            message: '请填写结束放电阈值',
        },
    ],
    thresholdDischargePowerSupplement: [
        {
            required: true,
            message: '请填写放电补发阈值',
        },
    ],
}

const currentId = ref(null)

const chargeState = ref([
    {
        name: '充电',
        value: 2,
    },
    {
        name: '放电',
        value: 1,
    },
    {
        name: '待机',
        value: 0,
    },
])
const strategySegments = ref([
    {
        startTime: '00:00',
        endTime: undefined,
        workStatus: 0,
        power: 0,
        endTimeList: fullTimePeriod,
    },
])

// 切换策略
const workStatusChange = (item, index) => {
    if (item.workStatus == 0) {
        strategySegments.value[index].power = 0
    }
}
const onAdd = (item, index) => {
    if (!strategySegments.value[index].startTime) {
        proxy.$message.error('请选择开始时间')
    } else if (!strategySegments.value[index].endTime) {
        proxy.$message.error('请选择结束时间')
    } else if (!(strategySegments.value[index].workStatus >= 0)) {
        proxy.$message.error('请选择执行策略')
    } else if (
        strategySegments.value[index].workStatus != 0 &&
        !strategySegments.value[index].power
    ) {
        proxy.$message.error('充放电功率不能为0')
    } else {
        const options = _cloneDeep(fullTimePeriod)
        let StartIndex = fullTimePeriod.findIndex((i) => i == item.endTime)
        strategySegments.value.push({
            startTime: strategySegments.value[index].endTime,
            endTime: '',
            workStatus: undefined,
            power: 0,
            endTimeList: options.splice(StartIndex + 1),
        })
    }
}

const onDelete = (item, index) => {
    const options = _cloneDeep(fullTimePeriod)
    let StartIndex = fullTimePeriod.findIndex((i) => i == item.startTime)
    strategySegments.value[index].endTimeList = options.splice(StartIndex + 1)
    // 判断是否是最后一个x，如果是 ，则删除最后一条数据
    if (index != strategySegments.value.length - 2) {
        return
    } else {
        strategySegments.value.splice(index + 1, 1)
    }
}

const onClose = () => {
    formRef.value.resetFields()
    strategySegments.value = [
        {
            startTime: '00:00',
            endTime: undefined,
            workStatus: 0,
            power: 0,
            endTimeList: fullTimePeriod,
        },
    ]
    currentId.value = null
    emit('onClose')
}
const getTempDetail = async () => {
    let res = await api.getStrategyTemplateDetail({
        id: currentId.value,
        stationId: route.query.stationId,
    })
    Object.keys(res.data.data).forEach((key) => {
        formState[key] = res.data.data[key]
    })
    formState.name = ''
    nextTick(() => {
        strategySegments.value = res.data.data.strategySegments
    })
}

const changeTab = async (e) => {
    currentId.value = e
    await getTempDetail()
}

const validSegments = computed(() => {
    let len = strategySegments.value.length
    let last = strategySegments.value[len - 1]
    if (last.endTime === '24:00' && (last.workStatus === 0 || last.power)) {
        return true
    } else {
        return false
    }
})
const onSave = async () => {
    formRef.value.validate().then(async () => {
        if (!validSegments.value) {
            proxy.$message.error('时间段不合法')
            return
        }
        if (props.isEdit) {
            if (
                JSON.stringify(oldFormState.value) ==
                    JSON.stringify(formState) &&
                JSON.stringify(oldStrategySegments.value) ==
                    JSON.stringify(strategySegments.value)
            ) {
                proxy.$message.success('保存成功')
                return
            }

            let res = await api.updateStrategyTemplate({
                ...formState,
                id: props.detail.id,
                stationId: route.query.stationId,
                strategySegments: strategySegments.value,
            })
            if (res.data.data) {
                proxy.$message.success('保存成功')
                onClose()
                emit('update')
            }
        } else {
            let res = await api.addStrategyTemplate({
                ...formState,
                stationId: route.query.stationId,
                strategySegments: strategySegments.value,
            })
            if (res.data.data) {
                proxy.$message.success('保存成功')
                onClose()
                emit('update')
            }
        }
    })
}
const onDeleteModel = async () => {
    let res = await api.deleteStrategyTemplate({
        id: props.detail.id,
        stationId: route.query.stationId,
    })
    if (res.data.code == 0) {
        proxy.$message.success('删除成功')
        // await getData()
        // currentId.value = menus.value[0].id
        onClose()
        emit('update')
        // await getTempDetail()
    }
}
</script>

<style lang="less" scoped>
.addModel {
    :deep(
            .ant-select-disabled.ant-select:not(.ant-select-customize-input)
                .ant-select-selector
        ) {
        background: #fff;
    }

    :deep(.ant-input-number-disabled) {
        background: #fff;
    }

    .form-flex {
        column-gap: 16px;
        width: 1162px;
        margin: 0 auto;

        .form-flex-item {
            width: calc(~'24.5% - 12px');

            :deep(.ant-form-item-control) {
                width: 88px;
                flex: none;
            }

            :deep(.ant-form-item-label) {
                flex: 1;
            }
        }

        &.form-flex-0 {
            .form-flex-item {
                &:nth-child(4n-2) {
                    width: calc(~'26.5% - 12px');
                }
            }
        }
    }

    .flex-box {
        align-items: center;
        justify-content: space-between;

        .flex-items-time {
            width: 300px;
            display: flex;
        }

        .flex-items-operation {
            width: 40px;
            background: rgba(149, 158, 195, 0.1);
            color: #959ec3;
            font-size: 12px;
            line-height: 24px;
            text-align: center;
            border-radius: 2px;
        }

        .flex-items-strategy {
            width: 160px;
        }

        .flex-items-power {
            width: 185px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .flex-items-action {
            width: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
}

:deep(.ant-form-item-explain) {
    font-size: 12px;
}

:deep(.ant-form-item-extra) {
    font-size: 12px;
}

@keyframes error {
    0% {
        border-color: transparent;
        box-shadow: 0 0 0 2px rgba(255, 77, 79, 0);
    }

    100% {
        border-color: #ff4d4f;
        box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
    }
}

// .errorTranstion {
//     border: 1px solid #ff4d4f;
//     // transition: all 2s;
//     animation: error 0.3s linear;
// }
.modal-btn {
    width: 54px;
    padding: 0;
}

:deep(
        .ant-btn-primary[disabled],
        .ant-btn-primary[disabled]:hover,
        .ant-btn-primary[disabled]:focus,
        .ant-btn-primary[disabled]:active
    ) {
    color: #fff;
    background-color: var(--themeColor);
    border-color: var(--themeColor);
    opacity: 0.6;
}
</style>
