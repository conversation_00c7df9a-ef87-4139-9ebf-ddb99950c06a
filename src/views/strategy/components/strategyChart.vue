<template>
    <div class="flex period-main" :class="[size]" style="width: 100%">
        <div
            class="rounded relative period"
            :class="[size]"
            v-for="(item, index) in strategySegments"
            :key="index"
            :style="{ width: item.percent + '%' }"
        >
            <a-tooltip style="z-index: 99999999">
                <template #title>
                    <div class="text-xs">
                        <div>
                            <span class="inline-block w-14">时间段:</span
                            >{{ item.startTime }} - {{ item.endTime }}
                        </div>
                        <div>
                            <span class="inline-block w-14">控制策略:</span
                            >{{ batteryStatus[item.workStatus] }}
                        </div>
                        <div>
                            <span class="inline-block w-14">执行功率:</span
                            >{{ item.power }}kW
                        </div>
                    </div>
                </template>
                <div
                    class="text-ff text-center font-medium cursor-pointer period-item"
                    :class="[
                        'period' + item.workStatus,
                        size == 'middle' ? 'h-8 leading-8' : 'h-3.5',
                    ]"
                >
                    <span v-if="item.workStatus && size != 'small'">{{
                        item.power
                    }}</span>
                </div>
            </a-tooltip>
            <div
                v-if="index == 0 && size != 'small'"
                class="absolute top-0 left-0 time start-time"
            >
                {{ item.startTime }}
            </div>
            <div
                v-if="size != 'small'"
                class="absolute time end-time"
                :class="item.endTime == '24:00' ? 'last-end-time' : ''"
            >
                {{ item.endTime }}
            </div>
        </div>
    </div>
</template>

<script setup>
import { fullTimePeriod } from '../util'
import { batteryStatus } from '@/views/device/const.js'
import { watch, ref } from 'vue'
const strategySegments = ref([])

const props = defineProps({
    size: {
        type: String,
        default: 'middle',
    },
    data: {
        type: Array,
        default: () => [],
    },
})
const setData = () => {
    //
    strategySegments.value.forEach((item) => {
        item['startIndex'] = fullTimePeriod.findIndex(
            (i) => i == item.startTime
        )
        item['endIndex'] = fullTimePeriod.findIndex((i) => i == item.endTime)
        item['percent'] = (100 * (item.endIndex - item.startIndex)) / 96
    })
}
watch(
    () => props.data,
    (val) => {
        if (val[0]?.endTime) {
            strategySegments.value = props.data
        } else {
            strategySegments.value = [
                {
                    startTime: '00:00',
                    endTime: '24:00',
                    workStatus: 0,
                    power: 0,
                },
            ]
        }
        setData()
    },
    { deep: true, immediate: true }
)
</script>

<style lang="less" scoped>
.ant-tooltip {
    z-index: 999999;
}
:deep(.ant-tooltip) {
    z-index: 999999;
}
.time {
    font-size: 12px;
    color: var(--text-60);
}
.end-time {
    position: absolute;
    top: 0;
    left: 100%;
    transform: translateX(-50%);
    &.last-end-time {
        transform: translateX(-100%);
    }
}
.period-main {
    position: relative;
    &::after {
        display: block;
        content: '';
        left: 0;
        top: 20px;
        width: 100%;
        height: calc(~'100% - 20px');
        background: #f5f7f7;
        position: absolute;
        z-index: 0;
    }
    &.small {
        &::after {
            background: #d8d8d8;
        }
    }
}
.period {
    position: relative;
    padding-top: 20px;
    z-index: 2;

    .period-item {
        border-left: 1px solid #f5f7f7;
    }

    .period0 {
        background: #f5f7f7;
    }
    .period1 {
        background: #4b82ff;
    }
    .period2 {
        background: #33be4f;
    }
    &.small {
        padding-top: 0;
        .period-item {
            border-left: none;
        }
        .period0 {
            background: #d8d8d8;
        }
    }
}
</style>

<style lang="less">
.ant-tooltip {
    z-index: 999999;
}
</style>
