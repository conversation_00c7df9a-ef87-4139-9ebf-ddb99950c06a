<template>
    <a-spin :spinning="loading">
        <div>
            <div class="flex justify-end">
                <el-select
                    v-model="selectedNo"
                    placeholder="请选择"
                    style="width: 120px; margin-right: 16px"
                    @change="onContainerChange"
                >
                    <el-option
                        v-for="item in containerList"
                        :key="item.containerNo"
                        :label="'机柜' + item.containerNo.substring(3)"
                        :value="item.containerNo"
                    />
                </el-select>
            </div>
            <div class="inline-block text-left w-56 mt-5">
                <div class="mb-4">调整控制模式</div>
                <a-form
                    :model="manualFormState"
                    ref="formRef"
                    labelAlign="left"
                >
                    <a-form-item label="pcs控制" name="runStatus">
                        <span class="text-title dark:text-title-dark">
                            {{
                                manualFormState.runStatus ? '开机' : '待机'
                            }}</span
                        ><a-switch
                            class="w-10 h-6 switch ml-5"
                            @change="runStatusChange"
                            :checkedValue="1"
                            :unCheckedValue="0"
                            v-model:checked="manualFormState.runStatus"
                        />
                    </a-form-item>
                    <a-form-item label="并网控制" name="gridStatus">
                        <span class="text-title dark:text-title-dark">
                            {{
                                manualFormState.gridStatus ? '并网' : '离网'
                            }}</span
                        ><a-switch
                            class="w-10 h-6 switch ml-5"
                            :checkedValue="1"
                            :unCheckedValue="0"
                            v-model:checked="manualFormState.gridStatus"
                        />
                    </a-form-item>
                    <a-form-item label="计划功率" name="power" class="mt-1.5">
                        <a-input-number
                            v-model:value="manualFormState.power"
                            :min="minPower"
                            :max="maxPower"
                            :step="1"
                            :precision="0"
                            background="#fff"
                            :disabled="!manualFormState.runStatus"
                            class="w-22"
                        ></a-input-number>
                        <span class="text-third-text ml-1">kW</span>
                    </a-form-item>
                    <div class="text-secondar-text dark:text-60-dark text-xs">
                        *输入值为正代表放电，为负代表充电
                    </div>
                    <div class="text-center mt-8">
                        <el-button
                            plain
                            round
                            type="primary"
                            class="modal-btn"
                            :disabled="btnDisabled"
                            :loading="btnLoading"
                            @click="manualSave"
                            >保存</el-button
                        >
                    </div>
                </a-form>
            </div>
        </div>
    </a-spin>
</template>

<script setup>
import {
    reactive,
    ref,
    watch,
    computed,
    onMounted,
    getCurrentInstance,
} from 'vue'
import api from '@/apiService/strategy'

import apiService from '@/apiService/device'
import { useRoute } from 'vue-router'
import _cloneDeep from 'lodash/cloneDeep'
const { proxy } = getCurrentInstance()
const route = useRoute()
const manualFormState = reactive({
    runStatus: false,
    gridStatus: false,
    power: 0,
})

const emit = defineEmits(['update'])
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    containerList: {
        type: Array,
        default: () => [],
    },
    containerNo: {
        type: String,
        default: '',
    },
})
const selectedNo = computed(() => {
    return props.containerNo
})
const runMode = ref()
const btnDisabled = computed(() => {
    if (runMode.value != 1) {
        return true
    } else {
        if (manualFormState.runStatus == 0) {
            return false
        } else {
            if (manualFormState.power == 0) {
                return true
            } else {
                return false
            }
        }
    }
})
const maxPower = computed(() => {
    return deviceInfo.value.installedPower
})
const minPower = computed(() => {
    return -deviceInfo.value.installedPower
})
const deviceInfo = ref({})
const getDeviceInfo = async () => {
    const { stationNo } = route.query
    let res = await apiService.getStationStaticInfo({ stationNo })
    deviceInfo.value = res.data.data
}
const oldFormState = ref({})
watch(
    () => props.data,
    (val) => {
        if (val) {
            // debugger
            manualFormState.runStatus = val.runStatus
            manualFormState.gridStatus = val.gridStatus
            manualFormState.power = val.power
            runMode.value = val.runMode
            oldFormState.value = _cloneDeep(manualFormState)
            getDeviceInfo()
        }
    },
    { immediate: true }
)
const loading = ref(false)
const runStatusChange = () => {
    if (manualFormState.runStatus == 0) {
        manualFormState.power = 0
    }
}
const btnLoading = ref(false)
const manualSave = async () => {
    //  向父级传递参数
    btnLoading.value = true
    if (JSON.stringify(oldFormState.value) == JSON.stringify(manualFormState)) {
        proxy.$message.success('保存成功')
        btnLoading.value = false
        return
    }
    let res = await api.changeRunMode({
        stationId: route.query.stationId,
        runMode: 1,
        containerNo: selectedNo.value,
        ...manualFormState,
    })
    if (res.data.data) {
        proxy.$message.success('保存成功')
    } else {
        proxy.$message.error('保存失败')
    }
    btnLoading.value = false
    emit('update')
}
const onContainerChange = (e) => {
    emit('onContainerChange', e)
}
</script>

<style lang="less" scoped>
:deep(.ant-form-item) {
    margin-bottom: 4px;
}

.modal-btn {
    padding: 0 12px;
}

:deep(.ant-switch) {
    min-width: 40px;
    height: 24px;
}

.ant-switch-loading-icon,
.ant-switch::after {
    width: 20px;
    height: 20px;
}

:deep(.ant-form-item-label) {
    width: 64px !important;
}
</style>
