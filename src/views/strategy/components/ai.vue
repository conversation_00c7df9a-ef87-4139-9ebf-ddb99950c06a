<template>
    <a-spin :spinning="loading">
        <div>
            <mw-table
                :dataSource="dataSource"
                :columns="columns"
                :hasPage="true"
                :pageConfig="{ changePage, paginationProps }"
                :customRow="Rowclick"
                :rowKey="(record) => record.id"
                @change="onTableChange"
                :showRefresh="false"
                class="text-sm"
            >
                <template #updateTime="{ text }">
                    <div class="">
                        {{ text || '-' }}
                    </div>
                </template>
                <template #status="{ record }">
                    <dictionary
                        :statusOptions="statusOptions"
                        :value="record.status"
                        :color="'backGroundColor'"
                    />
                </template>
                <template #model="{ text }">
                    {{ text }}
                </template>
                <template #detail="{ record }">
                    <div class="cursor-pointer" @click.stop="goDetail(record)">
                        >
                    </div>
                </template>
            </mw-table>
            <el-drawer
                v-model="visible"
                :size="486"
                :show-close="false"
                @close="onClose"
                wrapClassName="drawerBox"
            >
                <template #header>
                    <div
                        class="drawer-header flex items-center justify-between leading-5.5"
                    >
                        <div
                            class="drawer-header text-primary-text dark:text-80-dark"
                        >
                            <span>{{ '策略更新日志' }}</span>
                        </div>
                        <div class="flex gap-x-3 items-center">
                            <el-button plain round @click="onClose"
                                >取消</el-button
                            >
                            <!-- <el-button plain type="primary" round :loading="addLoading" @click="submit">保存</el-button> -->
                        </div>
                    </div>
                </template>
                <div>更新策略</div>
                <div>
                    <strategy-chart
                        :data-source="strategySegments"
                        size="small"
                    />
                    <div class="px-4">
                        <div
                            v-for="(item, index) in strategySegments"
                            :key="index"
                            class="flex justify-between items-center px-4 leading-5.5 mt-2"
                        >
                            <div>{{ item.startTime }}-{{ item.endTime }}</div>
                            <div>
                                <dictionary
                                    :statusOptions="chargeState"
                                    :value="String(item.workStatus)"
                                    labelKey="name"
                                    :color="'backGroundColor'"
                                />
                            </div>
                            <div>计划功率：{{ item.power }}kW</div>
                        </div>
                    </div>
                </div>
                <div class="p-4 rounded-lg bg-background mt-4">
                    <div>原有策略</div>
                    <div>
                        <strategy-chart
                            :data-source="strategySegments"
                            size="small"
                        />
                        <div class="">
                            <div
                                v-for="(item, index) in strategySegments"
                                :key="index"
                                class="flex justify-between items-center px-4 leading-5.5 mt-2"
                            >
                                <div>
                                    {{ item.startTime }}-{{ item.endTime }}
                                </div>
                                <div>
                                    <dictionary
                                        :statusOptions="chargeState"
                                        :value="String(item.workStatus)"
                                        labelKey="name"
                                        :color="'backGroundColor'"
                                    />
                                </div>
                                <div>计划功率：{{ item.power }}kW</div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-drawer>
        </div>
    </a-spin>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { usePagenation } from '@/common/setup'
import StrategyChart from './strategyChart.vue'
import Dictionary from '@/components/table/dictionary.vue'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const chargeState = ref([
    {
        label: t('status_chongdianzhong'),
        name: t('status_chongdian'),
        value: '2',
        icon: 'icon-ica-dianchi-chongdian',
        color: '#33BE4F',
        backGroundColor: '#33BE4F',
    },
    {
        label: t('status_fangdianzhong'),
        name: t('status_fangdian'),
        value: '1',
        icon: 'icon-ica-dianchi-fangdian',
        color: '#73ADFF',
        backGroundColor: '#73ADFF',
    },
    {
        label: t('status_daiji'),
        name: t('status_daiji'),
        value: '0',
        icon: 'icon-ica-dianchi-yunhang',
        color: '#222222',
        backGroundColor: '#d9d9d9',
    },
    {
        label: t('status_lixian'),
        name: t('status_lixian'),
        value: '3',
        icon: 'icon-ica-dianchi-lixian',
        color: '#666666',
        backGroundColor: '#666666',
    },
])
const statusOptions = ref([
    {
        value: 0,
        label: '失败',
        color: '#EA0C28',
        colorOffset: '#EA0C28',
        backGroundColor: '#EA0C28',
    },
    {
        value: 1,
        label: '成功',
        color: '#33BE4F',
        colorOffset: '#33BE4F',
        backGroundColor: '#33BE4F',
    },
])
const columns = [
    {
        title: '策略更新时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
        slots: {
            customRender: 'updateTime',
        },
        align: 'left',
        width: '20%',
    },
    {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        slots: {
            customRender: 'status',
        },
        align: 'center',
        width: '25%',
    },
    {
        title: '运行模式',
        dataIndex: 'model',
        key: 'model',
        slots: {
            customRender: 'model',
        },
        align: 'center',
        width: '25%',
    },
    {
        title: '策略详情',
        dataIndex: 'detail',
        key: 'detail',
        slots: {
            customRender: 'detail',
        },
        align: 'right',
        width: '20%',
    },
]
const dataSource = ref([
    {
        id: 1,
        updateTime: '2020年2月31日',
        status: 0,
        model: 1,
    },
    { id: 2, updateTime: '2020年2月31日', status: 1, model: 2 },
])

const strategySegments = ref([
    {
        startTime: '00:00',
        endTime: '06:15',
        workStatus: 0,
        power: 0,
    },
    {
        startTime: '06:15',
        endTime: '12:00',
        workStatus: 1,
        power: 1,
    },
    {
        startTime: '12:00',
        endTime: '16:00',
        workStatus: 2,
        power: 2,
    },
    {
        startTime: '16:00',
        endTime: '22:00',
        workStatus: 1,
        power: 1,
    },
    {
        startTime: '22:00',
        endTime: '24:00',
        workStatus: 0,
        power: 0,
    },
])
const loading = ref(false)
const getData = async () => {
    //
    loading.value = true
    setTimeout(() => {
        loading.value = false
    }, 200)
}
const { paginationProps, changePage, onTableChange, refresh, pageParam } =
    usePagenation(getData)
const Rowclick = (record) => {
    return {
        onClick: (event) => {}, // 点击行
    }
}
const visible = ref(false)
const goDetail = (record) => {
    visible.value = true
}
const onClose = () => {
    visible.value = false
}
onMounted(async () => {
    await getData()
})
</script>

<style lang="less" scoped>
:deep(.mw-table .ant-table-tbody > tr > td) {
    font-size: 14px;
}
</style>
