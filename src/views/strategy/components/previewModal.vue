<template>
    <a-modal
        :visible="visible"
        width="1200px"
        :footer="null"
        title="策略预览"
        @cancel="onClose"
        :bodyStyle="{
            paddingTop: 0,
        }"
        :headerStyle="{
            border: 'none',
        }"
    >
        <div class="flex gap-x-3">
            <a-calendar
                v-model:value="values"
                valueFormat="YYYY-MM-DD"
                style="
                    width: 768px;
                    height: 612px;
                    border: 1px solid #d9d9d9;
                    border-radius: 8px;
                "
                @select="dateSelect"
            >
                <template #dateFullCellRender="{ current: values }">
                    <div>
                        <div class="text-right">
                            {{ dayjs(values).date() }}日
                        </div>
                        <div class="text-right h-5">
                            <div
                                v-for="(item, index) in getListData(values)"
                                :key="index"
                                class="px-2 leading-5 text-xs inline-block rounded-sm"
                                style="
                                    background-color: rgba(149, 158, 195, 0.1);
                                    color: #959ec3;
                                "
                            >
                                {{ item?.name }}
                            </div>
                        </div>
                    </div>
                </template>
                <!-- <template  #dateCellRender="{ current: values }">
                    
                </template> -->
                <template #monthCellRender="{ current: values }">
                    <div v-if="getMonthData(values)" class="notes-month">
                        <section>{{ getMonthData(values) }}</section>
                        <span></span>
                    </div>
                </template>
                <template #headerRender>
                    <div
                        class="flex justify-center items-center py-2 border-0 border-b border-border"
                    >
                        <el-button
                            style="padding: 0 4px; border: none"
                            class="btn-hover"
                            size="small"
                            @click="changeMonth(-1)"
                        >
                            <el-icon :size="20">
                                <CaretLeft />
                            </el-icon>
                        </el-button>
                        <div class="mx-4">
                            {{ values }}
                        </div>
                        <el-button
                            style="padding: 0 4px; border: none"
                            class="btn-hover"
                            size="small"
                            @click="changeMonth(1)"
                        >
                            <el-icon :size="20">
                                <CaretRight />
                            </el-icon>
                        </el-button>
                    </div>
                </template>
            </a-calendar>
            <div class="border border-border flex-1 rounded-lg flex flex-col">
                <div class="bg-background leading-12 px-4">
                    {{ values }}<span class="ml-2">{{ weekDay }}</span
                    ><span
                        class="ml-7 text-xs px-2 leading-5.5 tactics-tip inline-block"
                    >
                        {{ currentData?.name }}</span
                    >
                </div>
                <div class="px-4 py-5 flex flex-1 flex-col">
                    <div class="mb-3">{{ previewDetail?.name }}</div>
                    <div class="mb-4" v-if="strategySegments?.length">
                        <strategy-chart
                            size="small"
                            v-model:data="strategySegments"
                        />
                    </div>
                    <div class="space-y-2">
                        <div
                            v-for="(item, index) in strategySegments"
                            :key="index"
                        >
                            <div class="flex justify-between items-center">
                                <div
                                    class="flex justify-between items-center gap-x-10"
                                >
                                    <div class="" style="width: 86px">
                                        {{ item.startTime }}-{{ item.endTime }}
                                    </div>
                                    <div class="text-left w-15">
                                        <dictionary
                                            :statusOptions="chargeState"
                                            :value="String(item.workStatus)"
                                            labelKey="name"
                                            :color="'backGroundColor'"
                                        />
                                    </div>
                                </div>
                                <div
                                    class="text-primary-text dark:text-80-dark text-left w-30"
                                >
                                    计划功率：{{ item.power }}kW
                                </div>
                            </div>
                        </div>
                    </div>
                    <div
                        v-if="!strategySegments?.length"
                        class="flex-1 flex justify-center items-center"
                    >
                        <empty-data description="未配置"> </empty-data>
                    </div>
                </div>
            </div>
        </div>
        <div class="text-center mt-4">
            <el-button plain round @click="onClose">{{
                $t('common_guanbi')
            }}</el-button>
        </div>
    </a-modal>
</template>

<script setup>
import { ref, reactive, toRaw, onMounted, computed, watch } from 'vue'

import Dictionary from '@/components/table/dictionary.vue'
import dayjs from 'dayjs'
import api from '@/apiService/strategy'
import StrategyChart from './strategyChart.vue'
import { useRoute } from 'vue-router'
import { CaretLeft, CaretRight } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const chargeState = ref([
    {
        label: t('status_chongdianzhong'),
        name: t('status_chongdian'),
        value: '2',
        icon: 'icon-ica-dianchi-chongdian',
        color: '#33BE4F',
        backGroundColor: '#33BE4F',
    },
    {
        label: t('status_fangdianzhong'),
        name: t('status_fangdian'),
        value: '1',
        icon: 'icon-ica-dianchi-fangdian',
        color: '#73ADFF',
        backGroundColor: '#73ADFF',
    },
    {
        label: t('status_daiji'),
        name: t('status_daiji'),
        value: '0',
        icon: 'icon-ica-dianchi-yunhang',
        color: '#222222',
        backGroundColor: '#d9d9d9',
    },
    {
        label: t('status_lixian'),
        name: t('status_lixian'),
        value: '3',
        icon: 'icon-ica-dianchi-lixian',
        color: '#666666',
        backGroundColor: '#666666',
    },
])
const route = useRoute()
const emit = defineEmits(['update', 'onClose'])
const props = defineProps({
    visible: Boolean,
    id: String,
})
const onClose = () => {
    emit('onClose')
}
const values = ref('')

watch(
    () => props.visible,
    (val) => {
        if (val) {
            // do something
            values.value = dayjs(new Date()).format('YYYY-MM-DD')
            getData()
        }
    }
)
onMounted(() => {
    values.value = dayjs(new Date()).format('YYYY-MM-DD')
    // getData()
})
const daysOfWeek = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
const weekDay = computed(() => {
    // 获取星期后转换成汉字
    return daysOfWeek[new Date(values.value).getDay()]
})
const getListData = (value) => {
    // 获取当前月份
    // const month =

    let listData
    if (
        previewData.value.length &&
        dayjs(previewData.value[0].date).month() == value.month()
    ) {
        listData = [previewData.value[value.date() - 1]]
        return listData || []
    }

    // switch (value.date()) {
    //     case 8:
    //         listData = [
    //             {
    //                 content: '标准策略',
    //             },
    //         ]
    //         break
    //     case 10:
    //         listData = [
    //             {
    //                 content: '标准策略',
    //             },
    //         ]
    //         break
    //     case 15:
    //         listData = [
    //             {
    //                 content: '标准策略',
    //             },
    //         ]
    //         break
    //     default:
    //         listData = null
    // }
    // return listData || []
}
const getMonthData = (value) => {
    if (value.month() === 8) {
        return 1394
    }
}

const changeMonth = async (offset) => {
    let [year, month] = values.value.split('-').map(Number)
    month += offset

    if (month < 1) {
        month = 12
        year -= 1
    } else if (month > 12) {
        month = 1
        year += 1
    }
    values.value = `${year}-${String(month).padStart(2, '0')}-01`
    await getData()
}
const previewData = ref([])

const list = ref([])
const getData = async () => {
    let res = await api.previewStation({
        stationId: route.query.stationId,
        month: dayjs(values.value).format('YYYY-MM'),
    })

    previewData.value = res.data.data
    await getDetail(values.value)
    // 获取对应日期下的数据
}
const previewDetail = ref({})
const strategySegments = ref([])
const currentData = ref({})
const getDetail = async (date) => {
    let selectedDate
    if (date) {
        selectedDate = date
    } else {
        selectedDate = values.value
    }
    currentData.value = previewData.value.find((item) => {
        return dayjs(item.date).format('YYYY-MM-DD') == selectedDate
    })
    if (currentData.value.templateId) {
        let res = await api.getStrategyTemplateDetail({
            id: currentData.value.templateId,
            stationId: route.query.stationId,
        })
        previewDetail.value = res.data.data
        strategySegments.value = res.data.data.strategySegments
    } else {
        previewDetail.value = null
        strategySegments.value = []
    }
    return
}

const dateSelect = async (date) => {
    getData()
}
</script>

<style lang="less" scoped>
:deep(.ant-fullcalendar-fullscreen .ant-fullcalendar-date) {
    padding: 17px 14px;
    height: 82px;
    border: 1px solid theme('colors.border');
    margin: 0;
}

:deep(.ant-fullcalendar-cell) {
    padding: 17px 14px;
    height: 82px;
    border: 1px solid theme('colors.border');
    margin: 0;
    cursor: pointer;
}

:deep(.ant-fullcalendar-last-month-cell) {
    opacity: 0.3;
}

:deep(.ant-fullcalendar-next-month-btn-day) {
    opacity: 0.3;
}

:deep(.ant-fullcalendar-fullscreen .ant-fullcalendar-month, ) {
    height: 82px;
    border: 1px solid theme('colors.border');
    margin: 0;
}

// :deep(.ant-fullcalendar-today) {
//     position: relative;
//     &::after {
//         content: '';
//         position: absolute;
//         top: 0;
//         right: 0;
//         bottom: 0;
//         left: 0;
//         width: 100%;
//         height: 100%;
//         border: 1px solid var(--themeColor);
//         border-radius: 1px;
//     }
// }
:deep(.ant-fullcalendar-selected-day) {
    position: relative;

    &::after {
        content: '';
        position: absolute;
        top: -1px;
        right: 0;
        bottom: 0;
        left: -1px;
        width: calc(100% + 2px);
        height: calc(100% + 2px);
        border: 1px solid var(--themeColor);
        border-radius: 4px;
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
    }
}

:deep(.ant-fullcalendar-column-header) {
    padding: 16px 14px;
    border: 1px solid theme('colors.border');
    border-bottom: 0;
}

// :deep(.ant-fullcalendar-fullscreen .ant-fullcalendar-content) {
// width: 88px;
// height: 22px;
// line-height: 22px;
// font-size: 12px;
// color: #959ec3;
// background: rgba(149, 158, 195, 0.1);
// text-align: center;
// border-radius: 2px;
// }
:deep(.ant-badge-status-text) {
    margin: 0;
    font-size: 12px;
    line-height: 22px;
}

:deep(.ant-btn-text) {
    border: none !important;
}

.tactics-tip {
    background: rgba(149, 158, 195, 0.1);
    color: #959ec3;
    border-radius: 2px;
    font-size: 12px;
}

:deep(.ant-badge-not-a-wrapper:not(.ant-badge-status)) {
    vertical-align: initial;
}

.events {
    width: 88px;
    height: 22px;
    line-height: 22px;
    font-size: 12px;
    color: #959ec3;
    background: rgba(149, 158, 195, 0.1);
    text-align: center;
    border-radius: 2px;
}
</style>

<style lang="less">
.ant-modal-header {
    border: none;
}
</style>
