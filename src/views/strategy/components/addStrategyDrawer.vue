<template>
    <el-drawer
        :model-value="visible"
        :size="486"
        :show-close="false"
        @close="onClose"
        wrapClassName="drawerBox"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header text-primary-text dark:text-80-dark">
                    <span>{{ '添加运行策略' }}</span>
                </div>
                <div class="flex gap-x-3 items-center">
                    <el-button plain round @click="onClose">{{
                        $t('common_guanbi')
                    }}</el-button>
                    <el-button
                        plain
                        type="primary"
                        round
                        :loading="addLoading"
                        @click="submit"
                        >添加</el-button
                    >
                </div>
            </div>
        </template>
        <div class="text-left form">
            <div>
                <el-select
                    v-model="modelValue"
                    placeholder="请选择参照运行策略"
                    @change="onTempChange"
                    no-match-text="无匹配数据"
                >
                    <el-option
                        v-for="item in strategyStationList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
            </div>
            <div class="px-3 py-4 bg-background rounded-lg mt-4">
                <div class="text-secondar-text dark:text-60-dark mb-3">
                    运行参数设定
                </div>
                <el-config-provider :locale="zhCn">
                    <el-form
                        ref="formRef"
                        :rules="rules"
                        :model="formState"
                        label-width="auto"
                        style="max-width: 600px"
                        label-position="left"
                        hide-required-asterisk
                    >
                        <el-form-item label="策略名称" prop="name">
                            <el-input
                                v-model="formState.name"
                                :placeholder="'请输入策略名称'"
                                :maxlength="20"
                            />
                        </el-form-item>
                        <el-form-item label="执行策略" prop="templateId">
                            <el-select
                                v-model="formState.templateId"
                                placeholder="请选择执行策略"
                                no-match-text="无匹配数据"
                            >
                                <el-option
                                    v-for="item in tempList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="优先级" prop="priority">
                            <el-select
                                v-model="formState.priority"
                                placeholder="请选择优先级"
                                no-match-text="无匹配数据"
                            >
                                <el-option
                                    v-for="item in prioritys"
                                    :key="item.value"
                                    :label="item.name"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="执行周期" prop="cycle">
                            <el-select
                                v-model="formState.cycle"
                                placeholder="请选择执行周期"
                                @change="changeCycle"
                                no-match-text="无匹配数据"
                            >
                                <el-option
                                    v-for="item in cycles"
                                    :key="item.value"
                                    :label="item.name"
                                    :value="item.value"
                                />
                            </el-select>
                            <div>
                                <el-checkbox-group
                                    v-model="formState.weekly"
                                    @change="changeWeek"
                                >
                                    <el-checkbox
                                        :label="item.label"
                                        :value="item.value"
                                        v-for="item in weeklys"
                                        :key="item.value"
                                    />
                                </el-checkbox-group>
                            </div>
                        </el-form-item>
                        <el-form-item label="启止时间" prop="rangeDate">
                            <el-date-picker
                                v-model="formState.rangeDate"
                                type="daterange"
                                :start-placeholder="$t('common_kaishiriqi')"
                                :end-placeholder="$t('common_jieshuriqi')"
                                value-format="YYYY-MM-DD"
                            />
                        </el-form-item>
                    </el-form>
                </el-config-provider>
            </div>
        </div>
    </el-drawer>
</template>

<script setup>
import { ref, reactive, toRaw, getCurrentInstance } from 'vue'
import api from '@/apiService/strategy'
const emit = defineEmits(['update', 'onClose'])
import { prioritys, weeklys, cycles } from '../util'
import { useRoute } from 'vue-router'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
const { proxy } = getCurrentInstance()
const route = useRoute()
const props = defineProps({
    visible: Boolean,
    strategyStationList: Array,
    tempList: Array,
})

const modelValue = ref(null)
const options = ref([
    {
        name: '模板1',
        value: '1',
    },
    {
        name: '模板2',
        value: '2',
    },
    {
        name: '模板3',
        value: '3',
    },
])
const formRef = ref(null)
const formState = reactive({
    name: '',
    templateId: undefined,
    priority: undefined,
    cycle: undefined,
    weekly: [],
    rangeDate: [],
})

const rules = {
    name: [
        {
            required: true,
            message: '请填写模版名称',
        },
    ],
    templateId: [
        {
            required: true,
            message: '请选择执行策略',
        },
    ],
    priority: [
        {
            required: true,
            message: '请选择优先级',
        },
    ],
    rangeDate: [
        {
            required: true,
            message: '请选择启止时间',
        },
    ],
}

const onClose = () => {
    formRef.value.resetFields()
    formState.weekly = []
    modelValue.value = null
    emit('onClose')
}
const addLoading = ref(false)
const submit = () => {
    addLoading.value = true
    formRef.value
        .validate()
        .then(async () => {
            if (formState.weekly.length == 0) {
                proxy.$message.error('请选择执行周期')
                return
            }
            let res = await api.addStrategyStation({
                ...formState,
                weekly: formState.weekly.join(','),
                startDate: formState.rangeDate[0],
                endDate: formState.rangeDate[1],
                stationId: route.query.stationId,
                rangeDate: undefined,
                cycle: undefined,
            })
            if (res.data.data) {
                proxy.$message.success('添加成功')
                emit('update', true)
                onClose()
            }
            addLoading.value = false
        })
        .catch((error) => {
            //
        })
}

const onTempChange = (e) => {
    const res = props.strategyStationList.find((s) => s.id == e)
    formState.name = ''
    formState.templateId = res.templateId
    formState.priority = res.priority
    formState.weekly = res.weekly.split(',')
    formState.rangeDate = [res.startDate, res.endDate]
    if (formState.weekly.length == 7) {
        formState.cycle = 1
    } else {
        formState.cycle = 2
    }
}

// 切换周期
const changeCycle = (e) => {
    if (e == 1) {
        formState.weekly = ['1', '2', '3', '4', '5', '6', '7']
    } else {
        formState.weekly = []
    }
}
const changeWeek = (e) => {
    if (e.length == 7) {
        formState.cycle = 1
    } else {
        formState.cycle = 2
    }
}
</script>

<style lang="less" scoped>
.form {
    :deep(.el-date-editor .el-range-input) {
        width: 90px;
    }

    :deep(.el-date-editor .el-range-separator) {
        flex: initial;
    }

    :deep(.el-date-editor .el-range__close-icon) {
        margin-left: auto;
    }
}
</style>
