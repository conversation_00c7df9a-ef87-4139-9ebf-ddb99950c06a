<template>
    <a-spin :spinning="loading">
        <div class="text-center">
            <div class="flex justify-end gap-x-3">
                <a-select
                    v-model:value="selectedStrategyId"
                    placeholder="请选择运行策略"
                    class="w-40 text-left"
                    @change="onTempChange"
                >
                    <a-select-option
                        v-for="item in strategyStationList"
                        :key="item.id"
                        :value="item.id"
                        >{{ item.name }}</a-select-option
                    >
                </a-select>
                <el-button plain round type="primary" @click="onDistribute"
                    >策略分发</el-button
                >
                <el-button plain round type="primary" @click="onAdd"
                    >添加运行策略</el-button
                >
                <el-button plain round type="primary" @click="onlook"
                    >查看策略模版</el-button
                >
            </div>
            <div class="inline-block text-left w-72 mt-7">
                <div class="mb-6 text-center">运行参数设定</div>
                <a-form
                    :model="formState"
                    ref="formRef"
                    :rules="rules"
                    :labelCol="{ span: 6 }"
                    labelAlign="left"
                    hideRequiredMark
                >
                    <a-form-item label="策略名称" name="name">
                        <a-input
                            v-model:value="formState.name"
                            placeholder="请输入策略名称"
                            style="width: 200px"
                            :maxlength="20"
                        ></a-input>
                    </a-form-item>
                    <a-form-item label="执行策略" name="templateId">
                        <a-select
                            v-model:value="formState.templateId"
                            placeholder="请选择执行策略"
                            class="w-40"
                        >
                            <a-select-option
                                v-for="item in tempList"
                                :key="item.id"
                                :value="item.id"
                                >{{ item.name }}</a-select-option
                            >
                        </a-select>
                    </a-form-item>
                    <a-form-item label="优先级" name="priority">
                        <a-select
                            v-model:value="formState.priority"
                            placeholder="请选择优先级"
                            class="w-40"
                        >
                            <a-select-option
                                v-for="item in prioritys"
                                :key="item.value"
                                :value="item.value"
                                >{{ item.name }}</a-select-option
                            >
                        </a-select>
                    </a-form-item>
                    <a-form-item label="执行周期">
                        <div>
                            <a-select
                                v-model:value="formState.cycle"
                                placeholder="请选择执行周期"
                                class="w-40"
                                @change="changeCycle"
                            >
                                <a-select-option
                                    v-for="item in cycles"
                                    :key="item.value"
                                    :value="item.value"
                                    >{{ item.name }}</a-select-option
                                >
                            </a-select>
                            <!-- <div>
                                <a-checkbox-group
                                    v-model:value="formState.weekly"
                                    name="checkboxgroup"
                                    @change="changeWeek"
                                    :options="weeklys"
                                />
                            </div> -->
                        </div>
                    </a-form-item>
                    <a-form-item label=" " :colon="false" style="width: 300px">
                        <div>
                            <div>
                                <a-checkbox-group
                                    v-model:value="formState.weekly"
                                    name="checkboxgroup"
                                    @change="changeWeek"
                                    :options="weeklys"
                                />
                            </div>
                        </div>
                    </a-form-item>
                    <a-form-item label="启止时间" name="rangeDate">
                        <a-range-picker
                            v-model:value="formState.rangeDate"
                            valueFormat="YYYY-MM-DD"
                        >
                            <template #suffixIcon>
                                <iconSvg name="calendar" class="calendar" />
                            </template>
                        </a-range-picker>
                    </a-form-item>
                    <div class="text-center pt-5 gap-x-3 flex justify-center">
                        <confirm-button
                            :title="'是否确认删除？?'"
                            @confirm="onDelete"
                            placement="bottom-end"
                        >
                            <template #reference>
                                <el-button round plain>删除</el-button>
                            </template>
                        </confirm-button>
                        <el-button round plain @click="onFresh">刷新</el-button>
                        <el-button plain round type="primary" @click="onPreview"
                            >预览</el-button
                        >
                        <el-button plain round type="primary" @click="onSave"
                            >保存</el-button
                        >
                    </div>
                </a-form>
            </div>
            <distribute-drawer
                :visible="distributeVisible"
                @onClose="distributeVisible = false"
                @update="onUpdate"
                v-model:strategyStationList="strategyStationList"
            />
            <add-strategy-drawer
                :visible="addVisible"
                @onClose="onDrawerClose"
                @update="onUpdate"
                v-model:strategyStationList="strategyStationList"
                v-model:tempList="tempList"
            />
            <preview-modal
                :visible="previewVisible"
                @onClose="onModalClose"
                v-model:id="selectedStrategyId"
            />
        </div>
    </a-spin>
</template>

<script setup>
import { reactive, ref, onMounted, computed, getCurrentInstance } from 'vue'
import AddStrategyDrawer from './addStrategyDrawer.vue'
import PreviewModal from './previewModal.vue'
import DistributeDrawer from './distributeDrawer.vue'
import api from '@/apiService/strategy'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { prioritys, weeklys, cycles } from '../util'
import { locale } from 'moment'
import _cloneDeep from 'lodash/cloneDeep'
const { proxy } = getCurrentInstance()
const router = useRouter(),
    route = useRoute()
const formRef = ref(null)
const formState = reactive({
    name: undefined,
    templateId: undefined,
    priority: undefined,
    cycle: undefined,
    weekly: undefined,
    rangeDate: [],
})

const rules = {
    name: [
        {
            required: true,
            message: '请输入策略名称',
            trigger: 'blur',
        },
    ],
    templateId: [
        {
            required: true,
            message: '请选择模板',
            trigger: 'change',
        },
    ],
    priority: [
        {
            required: true,
            message: '请选择优先级',
        },
    ],
    cycle: [
        {
            required: true,
            message: '请选择执行周期',
            trigger: 'change',
        },
    ],
    rangeDate: [
        {
            required: true,
            message: '请选择执行时间',
        },
    ],
}
const emit = defineEmits(['update'])
const loading = ref(false)

const value1 = ref(null)
const selectedStrategyId = ref(null)
const tempList = ref([])
const strategyStationList = ref([])

const addVisible = ref(false)
const previewVisible = ref(false)
const distributeVisible = ref(false)
const onDistribute = async () => {
    distributeVisible.value = true
    // let res = await api.authorizationStation({
    //     stationIds: route.query.stationId,
    //     strategyIds: selectedStrategyId.value,
    // })
}

const onAdd = () => {
    addVisible.value = true
}
const onDrawerClose = () => {
    addVisible.value = false
}

const onlook = () => {
    //
    router.push({
        path: '/strategy/models',
        query: {
            stationNo: route.query.stationNo,
            stationId: route.query.stationId,
            stationOrgId: route.query.stationOrgId,
            stationName: route.query.stationName,
        },
    })
}
const onDelete = async () => {
    const len = strategyStationList.value.length
    let res = await api.deleteStation({ id: selectedStrategyId.value })
    if (res.data.data) {
        proxy.$message.success('删除成功')
        await getBasicData()
        emit('update')
        if (len <= 1) {
            // 页面刷新
            window.location.reload()
        }
    }
}
const onFresh = async () => {
    await getBasicData(true)
    proxy.$message.success('刷新成功')
}

const onPreview = () => {
    previewVisible.value = true
}
const onModalClose = () => {
    previewVisible.value = false
}
const onSave = async () => {
    //  向父级传递参数

    if (!formState.weekly?.length) {
        proxy.$message.error('请选择执行周期')
        return
    }
    if (JSON.stringify(oldFormState.value) == JSON.stringify(formState)) {
        proxy.$message.success('保存成功')
        return
    }
    formRef.value
        .validate()
        .then(async () => {
            loading.value = true
            if (selectedStrategyId.value) {
                let res = await api.updateStation({
                    id: selectedStrategyId.value,
                    ...formState,
                    startDate: formState.rangeDate[0],
                    endDate: formState.rangeDate[1],
                    cycle: undefined,
                    weekly: formState.weekly.join(','),
                })
                if (res.data.data) {
                    proxy.$message.success('保存成功')
                }
                await getBasicData(true)
                emit('update')
            } else {
                let res = await api.addStrategyStation({
                    ...formState,
                    startDate: formState.rangeDate[0],
                    endDate: formState.rangeDate[1],
                    cycle: undefined,
                    weekly: formState.weekly.join(','),
                    stationId: route.query.stationId,
                    rangeDate: undefined,
                })
                if (res.data.data) {
                    proxy.$message.success('保存成功')
                }
                await getBasicData()
                emit('update')
            }
            loading.value = false
        })
        .catch((error) => {
            loading.value = false
        })
}
const store = useStore()
const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])
const orgId = ref(
    getCompanyInfo?.value?.orgId ? getCompanyInfo.value.orgId : void 0
)
// 获取模版详情
// 获取模版列表，并
const getTempList = async () => {
    let res = await api.getStrategyTemplatePage({
        stationId: route.query.stationId,
    })
    tempList.value = res.data.data.records
}

// 切换周期
const changeCycle = (e) => {
    if (e == 1) {
        formState.weekly = ['1', '2', '3', '4', '5', '6', '7']
    } else {
        formState.weekly = []
    }
}

const changeWeek = (e) => {
    if (e.length == 7) {
        formState.cycle = 1
    } else {
        formState.cycle = 2
    }
}
// 切换模版
const onTempChange = async (e) => {
    //
    loading.value = true
    await getStrategyDetail()
    setTimeout(() => {
        loading.value = false
    }, 200)
}
const oldFormState = ref({})
const getStrategyDetail = async () => {
    const res = strategyStationList.value.find(
        (s) => s.id == selectedStrategyId.value
    )
    if (res) {
        formState.name = res.name
        formState.templateId = res.templateId
        formState.priority = res.priority
        formState.weekly = res.weekly.split(',')
        formState.rangeDate = [res.startDate, res.endDate]
        if (formState.weekly.length == 7) {
            formState.cycle = 1
        } else {
            formState.cycle = 2
        }
        oldFormState.value = _cloneDeep(formState)
    }
}

// 获取策略列表
const getStrategyStationList = async (isFresh) => {
    let res = await api.getStrategyStationList({
        stationId: route.query.stationId,
    })
    strategyStationList.value = res.data.data
    if (isFresh || strategyStationList.value.length == 0) {
        // selectedStrategyId.value = strategyStationList.value[0].id
    } else {
        selectedStrategyId.value = strategyStationList.value[0].id
    }

    await getStrategyDetail()
    // formState
}

const getBasicData = async (isFresh) => {
    loading.value = true
    await getTempList()
    await getStrategyStationList(isFresh)
    loading.value = false
}
const onUpdate = async () => {
    await getBasicData()
}
onMounted(async () => {
    //
    await getBasicData()
})
</script>

<style lang="less" scoped>
:deep(.ant-checkbox + span) {
    padding-left: 3px;
    padding-right: 0;
}

:deep(.ant-checkbox-group-item) {
    margin-right: 9px;
    margin-top: 8px;

    &:nth-child(4) {
        margin-right: 0;
    }
}

.ant-form-item .ant-select,
.ant-form-item .ant-cascader-picker {
    width: 200px;
}

:deep(.ant-checkbox-checked .ant-checkbox-inner) {
    background-color: #1f1f1f;
    border-color: #1f1f1f;
}

:deep(.ant-checkbox-checked::after) {
    border-color: #1f1f1f;
}

:deep(.ant-checkbox-wrapper:hover .ant-checkbox-inner, ) {
    border-color: #1f1f1f;
}

:deep(.ant-checkbox:hover .ant-checkbox-inner, ) {
    border-color: #1f1f1f;
}

:deep(.ant-checkbox-input:focus + .ant-checkbox-inner) {
    border-color: #1f1f1f;
}
</style>
