<template>
  <div class="timeline-container">
    <div class="timeline" ref="timeline">
      <div v-for="(segment, index) in timeSegments" :key="index" class="time-segment"
        :style="{ left: `${segment.start}%`, width: `${segment.width}%`, backgroundColor: segment.color }"></div>
      <div v-for="(marker, index) in timeMarkers" :key="'marker-' + index" class="time-marker"
        :style="{ left: `${marker.position}%` }" @mousedown="startDrag($event, index)"></div>
    </div>
    <div class="time-inputs">
      <div v-for="(time, index) in times" :key="index" class="time-input">
        <input :value="formatTime(time.start)" @input="updateTime($event, index, 'start')" />
        <span>-</span>
        <input :value="formatTime(time.end)" @input="updateTime($event, index, 'end')" />
        <select v-model="time.action">
          <option value="充电">充电</option>
          <option value="放电">放电</option>
          <option value="待机">待机</option>
        </select>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      times: [
        { start: 0, end: 375, action: '充电' },
        { start: 375, end: 720, action: '放电' },
        { start: 720, end: 1170, action: '待机' },
        { start: 1170, end: 1440, action: '充电' },
      ],
      draggingIndex: null,
      dragStartX: 0,
    };
  },
  computed: {
    timeSegments() {
      return this.times.map((time, index) => ({
        start: (time.start / 1440) * 100,
        width: ((time.end - time.start) / 1440) * 100,
        color: this.getColorForAction(time.action),
      }));
    },
    timeMarkers() {
      return this.times.map((time) => ({
        position: (time.start / 1440) * 100,
      }));
    },
  },
  methods: {
    formatTime(minutes) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
    },
    getColorForAction(action) {
      const colors = { '充电': 'green', '放电': 'blue', '待机': 'gray' };
      return colors[action] || 'gray';
    },
    startDrag(event, index) {
      this.draggingIndex = index;
      this.dragStartX = event.clientX;
      document.addEventListener('mousemove', this.drag);
      document.addEventListener('mouseup', this.stopDrag);
    },
    drag(event) {
      if (this.draggingIndex === null) return;

      const timeline = this.$refs.timeline;
      const timelineRect = timeline.getBoundingClientRect();
      const deltaX = event.clientX - this.dragStartX;
      const deltaTime = Math.round((deltaX / timelineRect.width) * 1440);

      let newStart = this.times[this.draggingIndex].start + deltaTime;

      // 限制拖动范围
      const minTime = this.draggingIndex > 0 ? this.times[this.draggingIndex - 1].start : 0;
      const maxTime = this.draggingIndex < this.times.length - 1 ? this.times[this.draggingIndex + 1].end : 1440;

      newStart = Math.max(minTime, Math.min(newStart, maxTime));

      this.times[this.draggingIndex].start = newStart;
      if (this.draggingIndex > 0) {
        this.times[this.draggingIndex - 1].end = newStart;
      }

      this.dragStartX = event.clientX;
    },
    stopDrag() {
      this.draggingIndex = null;
      document.removeEventListener('mousemove', this.drag);
      document.removeEventListener('mouseup', this.stopDrag);
    },
    updateTime(event, index, type) {
      const [hours, minutes] = event.target.value.split(':').map(Number);
      const totalMinutes = hours * 60 + minutes;
      this.times[index][type] = totalMinutes;
    },
  },
};
</script>

<style scoped>
.timeline-container {
  width: 100%;
  padding: 20px;
}

.timeline {
  position: relative;
  height: 30px;
  background-color: #f0f0f0;
  margin-bottom: 20px;
}

.time-segment {
  position: absolute;
  height: 100%;
  top: 0;
}

.time-marker {
  position: absolute;
  width: 2px;
  height: 30px;
  background-color: red;
  cursor: ew-resize;
}

.time-inputs {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.time-input {
  display: flex;
  gap: 5px;
}
</style>
