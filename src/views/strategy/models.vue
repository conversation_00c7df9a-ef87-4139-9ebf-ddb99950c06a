<template>
    <a-spin :spinning="loading">
        <div class="px-4 py-5 models">
            <div class="rounded pb-4">
                <div class="flex justify-between items-center px-3">
                    <div class="font-medium text-title dark:text-title-dark">
                        策略模版
                    </div>
                    <el-button plain round type="primary" @click="onAddModel"
                        >创建模版</el-button
                    >
                </div>
                <mw-table
                    :dataSource="menus"
                    :columns="columns"
                    :hasPage="true"
                    :pageConfig="{ changePage, paginationProps }"
                    :customRow="Rowclick"
                    :rowKey="(record) => record.id"
                    @change="onTableChange"
                    :showRefresh="false"
                    class="text-sm"
                >
                    <template #name="{ text }">
                        <div class="overflow" style="max-width: 328px">
                            <div class="">
                                {{ text || '-' }}
                            </div>
                        </div>
                    </template>
                    <template #useStationCount="{ record }">
                        <dictionary
                            :statusOptions="modelStatusOptions"
                            :value="record.useStationCount > 0"
                            :color="'color'"
                        />
                        <span class="align-middle">{{
                            record.useStationCount > 0
                                ? '（' + record.useStationCount + '）'
                                : ''
                        }}</span>
                    </template>
                    <template #creatorName="{ text }">
                        <div class="overflow" style="max-width: 328px">
                            {{ text || '-' }}
                        </div>
                    </template>
                    <template #createTime="{ record }">
                        <div class="cursor-pointer">
                            {{ record.createTime }}
                        </div>
                    </template>
                </mw-table>
            </div>
            <add-model
                :visible="addModelVisible"
                v-model:detail="detail"
                @onClose="onModalClose"
                :isEdit="isEdit"
                v-model:models="menus"
                @update="update"
                v-model:installedPower="deviceInfo.installedPower"
            />
        </div>
    </a-spin>
</template>

<script setup>
import {
    onMounted,
    reactive,
    ref,
    getCurrentInstance,
    computed,
    nextTick,
} from 'vue'
import { usePagenation } from '@/common/setup'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import moment from 'moment'
import StrategyChart from './components/strategyChart.vue'
import _cloneDeep from 'lodash/cloneDeep'
import AddModel from './components/addModel.vue'
import Dictionary from '@/components/table/dictionary.vue'

import {
    fullTimePeriod,
    strategyTypes,
    transformerCapProtectOptions,
    backFlowPreventionOptions,
    modelStatusOptions,
} from './util'
import api from '@/apiService/strategy'
import apiService from '@/apiService/device'
const { proxy } = getCurrentInstance()
const route = useRoute()
const menus = ref([])
const currentId = ref(null)
const loading = ref(false)
const store = useStore()
const columns = [
    {
        title: '模版名称',
        dataIndex: 'name',
        key: 'name',
        slots: {
            customRender: 'name',
        },
        align: 'left',
        width: '35%',
    },
    {
        title: '状态',
        dataIndex: 'useStationCount',
        key: 'useStationCount',
        slots: {
            customRender: 'useStationCount',
        },
        align: 'left',
        width: '20%',
    },
    {
        title: '创建人',
        dataIndex: 'creatorName',
        key: 'creatorName',
        slots: {
            customRender: 'creatorName',
        },
        align: 'center',
        width: '20%',
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        key: 'createTime',
        slots: {
            customRender: 'createTime',
        },
        align: 'right',
        width: '25%',
    },
]
const isEdit = ref(false)
const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])
const orgId = ref(
    getCompanyInfo?.value?.orgId ? getCompanyInfo.value.orgId : void 0
)
const detail = ref({})
const getTempDetail = async (isFresh) => {
    loading.value = true
    let res = await api.getStrategyTemplateDetail({
        id: currentId.value,
        stationId: route.query.stationId,
    })
    detail.value = res.data.data
    if (res.data.code == 0 && isFresh) {
        proxy.$message.success('刷新成功')
    }
    Object.keys(res.data.data).forEach((key) => {
        formState[key] = res.data.data[key]
    })
    nextTick(() => {
        strategySegments.value = res.data.data.strategySegments
    })
    loading.value = false
    addModelVisible.value = true
}
const getData = async () => {
    const params = {
        current: pageParam.value.current,
        size: pageParam.value.size,
    }
    let res = await api.getStrategyTemplatePage({
        ...params,
        stationId: route.query.stationId,
    })
    // let res = await api.getStrategyTemplateList({
    //     ...params,
    //     stationId: route.query.stationId,
    // })
    menus.value = res.data.data.records
    paginationProps.value.total = res.data.data.total
}
const { paginationProps, changePage, onTableChange, refresh, pageParam } =
    usePagenation(getData)
const Rowclick = (record) => {
    return {
        onClick: async (event) => {
            // 获取详情，打开弹窗
            currentId.value = record.id
            isEdit.value = true
            await getTempDetail()
        }, // 点击行
    }
}

const formState = reactive({
    name: '',
    strategyType: undefined,
    plannedPower: 0,
    socMax: undefined,
    socMin: undefined,
    backFlowPrevention: 0,
    gridPowerLimit: undefined,
    gridPowerLimitTolerance: undefined,
    batVMax: undefined,
    batVMin: undefined,
    transformerCapProtect: 0,
    transformerCapMax: undefined,
    transformerCapRatio: undefined,
    tempMax: undefined,
    chargePowerMax: undefined,
    dischargePowerMax: undefined,
    beforePowerOnTime: undefined,
    afterPowerOffTime: undefined,
    strategySegments: [],
    startChargePower: undefined,
    endChargePower: undefined,
    thresholdDischargePower: undefined,
    endThresholdDischargePower: undefined,
    thresholdDischargePowerSupplement: undefined,
})

const socMaxValidator = async (rule, value) => {
    if (formState.socMax <= formState.socMin) {
        return Promise.reject('SOC上限不能低于SOC下限')
    } else {
        return Promise.resolve()
    }
}
const socMinValidator = async (rule, value) => {
    if (formState.socMax <= formState.socMin) {
        return Promise.reject('SOC下限不能高于SOC上限')
    } else {
        return Promise.resolve()
    }
}

const rules = {
    name: [
        {
            required: true,
            message: '请填写模版名称',
        },
    ],
    // strategyType: [
    //     {
    //         required: false,
    //         message: '',
    //
    //     },
    // ],
    socMax: [
        {
            required: true,
            message: '请填写SOC上限',
        },
        {
            validator: socMaxValidator,
        },
    ],
    socMin: [
        {
            required: true,
            message: '请填写SOC下限',
        },
        {
            validator: socMinValidator,
        },
    ],
    // backFlowPrevention: [
    //     {
    //         required: false,
    //         message: '',
    //
    //     },
    // ],
    gridPowerLimit: [
        {
            required: true,
            message: '请填写电网供电功率下限',
        },
    ],
    gridPowerLimitTolerance: [
        {
            required: true,
            message: '请填写电网供电功率下限容差',
        },
    ],
    batVMax: [
        {
            required: true,
            message: '请填写电池电压上限',
        },
    ],
    batVMin: [
        {
            required: true,
            message: '请填写电池电压下限',
        },
    ],
    // transformerCapProtect: [
    //     {
    //         required: true,
    //         message: '请填写变压器容量保护',
    //
    //     },
    // ],
    transformerCapMax: [
        {
            required: true,
            message: '请填写变压器容量上限',
        },
    ],
    transformerCapRatio: [
        {
            required: true,
            message: '请填写变压器容量比例',
        },
    ],
    tempMax: [
        {
            required: true,
            message: '请填写温度上限',
        },
    ],
    chargePowerMax: [
        {
            required: true,
            message: '请填写充电功率上限',
        },
    ],
    dischargePowerMax: [
        {
            required: true,
            message: '请填写放电功率上限',
        },
    ],
    beforePowerOnTime: [
        {
            required: true,
            message: '请填写提前开启时间',
        },
    ],
    afterPowerOffTime: [
        {
            required: true,
            message: '请填写延迟关闭时间',
        },
    ],
    startChargePower: [
        {
            required: true,
            message: '请填写开始充电功率',
        },
    ],
    endChargePower: [
        {
            required: true,
            message: '请填写结束充电功率',
        },
    ],
    thresholdDischargePower: [
        {
            required: true,
            message: '请填写放电阈值功率',
        },
    ],
    endThresholdDischargePower: [
        {
            required: true,
            message: '请填写结束放电阈值',
        },
    ],
    thresholdDischargePowerSupplement: [
        {
            required: true,
            message: '请填写放电补发阈值',
        },
    ],
}

const chargeState = ref([
    {
        name: '充电',
        value: 2,
    },
    {
        name: '放电',
        value: 1,
    },
    {
        name: '待机',
        value: 0,
    },
])
const strategySegments = ref([
    {
        startTime: '00:00',
        endTime: undefined,
        workStatus: 0,
        power: 0,
        endTimeList: fullTimePeriod,
    },
])

// 切换策略
const workStatusChange = (item, index) => {
    if (item.workStatus == 0) {
        strategySegments.value[index].power = 0
    }
}
const onAdd = (item, index) => {
    if (!strategySegments.value[index].startTime) {
        proxy.$message.error('请选择开始时间')
    } else if (!strategySegments.value[index].endTime) {
        proxy.$message.error('请选择结束时间')
    } else if (!(strategySegments.value[index].workStatus >= 0)) {
        proxy.$message.error('请选择执行策略')
    } else if (
        strategySegments.value[index].workStatus != 0 &&
        !strategySegments.value[index].power
    ) {
        proxy.$message.error('充放电功率不能为0')
    } else {
        const options = _cloneDeep(fullTimePeriod)
        let StartIndex = fullTimePeriod.findIndex((i) => i == item.endTime)
        strategySegments.value.push({
            startTime: strategySegments.value[index].endTime,
            endTime: '',
            workStatus: undefined,
            power: 0,
            endTimeList: options.splice(StartIndex + 1),
        })
    }
}

const onDelete = (item, index) => {
    // 判断是否是最后一个x，如果是 ，则删除最后一条数据
    const options = _cloneDeep(fullTimePeriod)
    let StartIndex = fullTimePeriod.findIndex((i) => i == item.startTime)
    strategySegments.value[index].endTimeList = options.splice(StartIndex + 1)
    if (index != strategySegments.value.length - 2) {
        return
    } else {
        strategySegments.value.splice(index + 1, 1)
    }
}

const formRef = ref(null)
const onSave = async () => {
    let res = await api.updateStrategyTemplate({
        ...formState,
        id: currentId.value,
        stationId: route.query.stationId,
    })
    if (res.data.data) {
        proxy.$message.success('保存成功')
        // formRef.value.resetFields()
        // emit('onClose')
        // emit('update')
    }
}

const addModelVisible = ref(false)
const onAddModel = () => {
    isEdit.value = false
    addModelVisible.value = true
}
const onModalClose = () => {
    addModelVisible.value = false
}
const deviceInfo = ref({
    installedPower: 0,
})
const getDeviceInfo = async () => {
    const { stationNo } = route.query
    let res = await apiService.getStationStaticInfo({ stationNo })
    deviceInfo.value = res.data.data
}
onMounted(async () => {
    loading.value = true
    await getData()
    await getDeviceInfo()
    loading.value = false
})

const update = async () => {
    await getData()
}
</script>

<style lang="less" scoped>
.menu-tab {
    position: relative;
    line-height: 24px;
    padding-bottom: 4px;
    transition: all 0.3s;

    &::after {
        display: block;
        content: '';
        width: 36px;
        height: 4px;
        background: transparent;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
    }

    &.active {
        color: var(--themeColor);

        &::after {
            background: var(--themeColor);
        }
    }
}

.flex-box {
    align-items: center;
    justify-content: space-between;

    .flex-items-time {
        width: 300px;
        display: flex;
    }

    .flex-items-operation {
        width: 40px;
        background: rgba(149, 158, 195, 0.1);
        color: #959ec3;
        font-size: 12px;
        line-height: 24px;
        text-align: center;
        border-radius: 2px;
    }

    .flex-items-strategy {
        width: 160px;
    }

    .flex-items-power {
        width: 185px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .flex-items-action {
        width: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

.models {
    :deep(
            .ant-select-disabled.ant-select:not(.ant-select-customize-input)
                .ant-select-selector
        ) {
        background: #fff;
    }

    :deep(.ant-input-number-disabled) {
        background: #fff;
    }

    .form-flex {
        column-gap: 45px;
        width: 880px;
        margin: 0 auto;

        .form-flex-item {
            width: calc(~'33.333% - 30px');
        }

        :deep(.ant-form-item-label) {
            flex: 1;
        }

        :deep(.ant-form-item-control) {
            width: 88px;
            flex: none;
        }
    }
}
</style>
