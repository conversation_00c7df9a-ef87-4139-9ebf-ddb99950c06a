<template>
    <div class="user">
        <div class="text-base leading-8 go-box flex items-center">
            <span
                class="cursor-pointer mr-1 a text-primary-text dark:text-80-dark"
                @click="goBlack"
                >{{ $t('Home') }}</span
            >

            <iconSvg
                name="rightIcon"
                class="more-icon text-primary-text dark:text-80-dark"
            />
            <!-- 站点详情 -->
            <span class="ml-1 text-40 dark:text-40-dark">{{
                $t('Account')
            }}</span>
        </div>

        <div class="user-content">
            <div class="flex justify-center mt-16">
                <div class="" style="width: 520px">
                    <div class="">
                        <div
                            class="text-secondar-text dark:text-60-dark mb-2.5"
                        >
                            {{ $t('Basic Info') }}
                        </div>
                        <div class="flex items-center justify-between mb-3">
                            <div
                                class="text-secondar-text dark:text-60-dark w-25"
                            >
                                {{ $t('username') }}
                            </div>
                            <div
                                class="flex-1 mr-3 w-56 text-title dark:text-title-dark"
                            >
                                {{ loginInfo.realName }}
                            </div>
                            <div>
                                <el-button
                                    plain
                                    round
                                    @click="onEdit('username')"
                                >
                                    <div>{{ $t('Edit') }}</div>
                                    <iconSvg
                                        name="edit"
                                        class="w-4 h-4 ml-0.5"
                                    />
                                </el-button>
                            </div>
                        </div>
                        <div class="flex items-center justify-between mb-3">
                            <div
                                class="text-secondar-text dark:text-60-dark w-25"
                            >
                                {{ $t('phone') }}
                            </div>
                            <div
                                class="flex-1 mr-3 w-80 text-title dark:text-title-dark"
                            >
                                {{ loginInfo.phone }}
                            </div>
                            <div>
                                <el-button plain round @click="onEdit('phone')">
                                    <div>{{ $t('Edit') }}</div>
                                    <iconSvg
                                        name="edit"
                                        class="w-4 h-4 ml-0.5"
                                    />
                                </el-button>
                            </div>
                        </div>
                        <div class="flex items-center justify-between mb-3">
                            <div
                                class="text-secondar-text dark:text-60-dark w-25"
                            >
                                {{ $t('Mail') }}
                            </div>
                            <div
                                class="flex-1 mr-3 w-80 text-title dark:text-title-dark"
                            >
                                {{ loginInfo.email }}
                            </div>
                            <div>
                                <el-button plain round @click="onEdit('email')">
                                    <div>{{ $t('Edit') }}</div>
                                    <iconSvg
                                        name="edit"
                                        class="w-4 h-4 ml-0.5"
                                    />
                                </el-button>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div
                                class="text-secondar-text dark:text-60-dark w-25"
                            >
                                {{ $t('Password') }}
                            </div>
                            <div
                                class="flex-1 mr-3 w-56 text-title dark:text-title-dark"
                            >
                                ***********
                            </div>
                            <div>
                                <el-button
                                    plain
                                    round
                                    @click="onEdit('password')"
                                >
                                    <div>{{ $t('Edit') }}</div>
                                    <iconSvg
                                        name="edit"
                                        class="w-4 h-4 ml-0.5"
                                    />
                                </el-button>
                            </div>
                        </div>
                    </div>
                    <div class="mt-5" v-if="activeSystem != 'car'">
                        <div class="text-secondar-text dark:text-60-dark mb-1">
                            {{ $t('Account Binding') }}
                        </div>
                        <!-- 微信/ -->
                        <div class="text-third-text dark:text-40-dark mb-3">
                            {{ $t('tips_bind_l') }}
                            {{ $t('DingTalkMiniProgram') }}
                            {{ $t('tips_bind_r') }}
                        </div>
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex-1 flex items-center">
                                <img
                                    src="@/assets/device/weichat.png"
                                    alt=""
                                    class="w-5 h-5 mr-1"
                                    srcset=""
                                />
                                <div class="text-base dark:text-80-dark">
                                    {{ $t('WeChat') }}
                                </div>
                            </div>
                            <div>
                                <confirm-button
                                    v-if="
                                        weChatInfo && weChatInfo.thirdUserName
                                    "
                                    :title="$t('tips_unbind_WeChat')"
                                    @confirm="unbind('WECHAT')"
                                    placement="bottom-end"
                                >
                                    <template #reference>
                                        <el-button
                                            plain
                                            round
                                            class="btn-hover"
                                            type="primary"
                                        >
                                            <span>{{ $t('Unbind') }}</span>
                                            <span class="icon-box ml-0.5">
                                                <iconSvg
                                                    name="unbind"
                                                    className="icon-default"
                                                />
                                            </span>
                                        </el-button>
                                    </template>
                                </confirm-button>

                                <el-button
                                    plain
                                    round
                                    class="btn-hover"
                                    type="primary"
                                    @click="openBindVisible('WECHAT')"
                                    v-else
                                >
                                    <span>{{ $t('Bind') }}</span>
                                    <span class="icon-box ml-0.5">
                                        <iconSvg
                                            name="bind"
                                            className="icon-default"
                                        />
                                    </span>
                                </el-button>
                            </div>
                        </div>
                        <div
                            class="mb-8"
                            v-if="weChatInfo && weChatInfo.thirdUserName"
                        >
                            <div class="text-center">
                                <img
                                    src="@/assets/wechat.jpg"
                                    alt=""
                                    width="200"
                                />
                            </div>
                            <div
                                class="text-center text-primary-text dark:text-80-dark"
                            >
                                {{
                                    $t('tips_bind_WeChat').replace(
                                        's%',
                                        '【' +
                                            weChatInfo.thirdUserName.substring(
                                                0,
                                                6
                                            ) +
                                            '******】'
                                    )
                                }}
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex-1 flex items-center">
                                <img
                                    src="@/assets/device/dingtalk.png"
                                    alt=""
                                    class="w-5 h-5 mr-1"
                                    srcset=""
                                />
                                <div
                                    class="text-base text-title dark:text-80-dark"
                                >
                                    {{ $t('DingTalk') }}
                                </div>
                            </div>
                            <div>
                                <confirm-button
                                    v-if="
                                        dingtalkInfo &&
                                        dingtalkInfo.thirdUserName
                                    "
                                    :title="$t('tips_unbind_DingTalk')"
                                    @confirm="unbind('DING_TALK')"
                                    placement="bottom-end"
                                >
                                    <template #reference>
                                        <el-button
                                            plain
                                            round
                                            class="btn-hover"
                                            type="primary"
                                        >
                                            <span>{{ $t('Unbind') }}</span>
                                            <span class="icon-box ml-0.5">
                                                <iconSvg
                                                    name="unbind"
                                                    className="icon-default"
                                                />
                                            </span>
                                        </el-button>
                                    </template>
                                </confirm-button>
                                <el-button
                                    plain
                                    round
                                    class="btn-hover"
                                    type="primary"
                                    @click="openBindVisible('DING_TALK')"
                                    v-else
                                >
                                    <span>{{ $t('Bind') }}</span>
                                    <span class="icon-box ml-0.5">
                                        <iconSvg
                                            name="bind"
                                            className="icon-default"
                                        />
                                    </span>
                                </el-button>
                            </div>
                        </div>
                        <div v-if="dingtalkInfo && dingtalkInfo.thirdUserName">
                            <div class="text-center">
                                <vue-qr
                                    :logoSrc="logoSrc"
                                    :text="url2"
                                    :callback="test"
                                    :size="200"
                                ></vue-qr>
                            </div>
                            <div
                                class="text-center text-primary-text dark:text-80-dark"
                            >
                                {{
                                    $t('tips_bind_DingTalk').replace(
                                        's%',
                                        '【' + dingtalkInfo.thirdUserName + '】'
                                    )
                                }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <edit-name
                v-model:visible="editNameVisible"
                :currentUsername="loginInfo.realName"
                @confirm="onUpdate"
            />
            <edit-phone
                v-model:visible="editPhoneVisible"
                :currentPhone="loginInfo.phone"
                :currentEmail="loginInfo.email"
                @confirm="onUpdate"
            />
            <edit-email
                v-model:visible="editEmailVisible"
                :currentPhone="loginInfo.phone"
                :currentEmail="loginInfo.email"
                @confirm="onUpdate"
            />
            <edit-psw
                v-model:visible="editPswVisible"
                :currentPhone="loginInfo.phone"
                :currentEmail="loginInfo.email"
                @confirm="onUpdate"
            />

            <el-drawer
                v-model="bindVisible"
                :lockScroll="true"
                :size="486"
                :show-close="false"
                @close="closeDrawer"
            >
                <template #header>
                    <div
                        class="drawer-header flex items-center justify-between leading-5.5"
                    >
                        <div class="drawer-header">
                            <span
                                >{{
                                    deviceType == 'WECHAT'
                                        ? $t('WeChat')
                                        : $t('DingTalk')
                                }}{{ $t('Account Binding') }}</span
                            >
                        </div>
                        <div>
                            <el-button plain round @click="closeDrawer">{{
                                $t('common_guanbi')
                            }}</el-button>
                        </div>
                    </div>
                </template>
                <QR-code :type="deviceType" :uri="uri" />
            </el-drawer>
        </div>
    </div>
</template>

<script setup>
import service from '@/apiService/device'
import api from '@/apiService/index'
import QRCode from '@/components/QRCode.vue'
import { computed, onMounted, reactive, toRefs, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import store from '@/store'
import editName from './editName.vue'
import editPhone from './editPhone.vue'
import editEmail from './editEmail.vue'
import editPsw from './editPsw.vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const route = useRoute()
const router = useRouter()
const logoSrc = require('@/assets/login/icon.png')
const editPhoneVisible = ref(false),
    editNameVisible = ref(false),
    editPswVisible = ref(false),
    bindVisible = ref(false),
    count = ref(0),
    bindInfo = ref(undefined),
    deviceType = ref(''),
    weChatInfo = ref(undefined),
    dingtalkInfo = ref(undefined),
    uri = ref(''),
    hasWechatCode = ref(false)
const url2 = ref(
    `dingtalk://dingtalkclient/action/open_micro_app?appId=129525&page=pages%2Fdevice%2Findex%3FuserId%3D${store.state.user.userInfoData.userId}`
)
let ApiTimer = null
const getBindInfo = async (type) => {
    console.log('[ 3 ] >', 3)
    let res = await service.getBindThirdAccount()
    bindInfo.value = res.data.data
    weChatInfo.value = res.data.data.filter(
        (item) => item.platform == 'WECHAT'
    )[0]
    dingtalkInfo.value = res.data.data.filter(
        (item) => item.platform == 'DING_TALK'
    )[0]
}
const openBindVisible = async (type) => {
    // 获取绑定信息
    deviceType.value = type
    if (type == 'WECHAT') {
        if (!hasWechatCode.value) {
            // 获取微信绑定二维码
            let res = await service.createWechatBindQrcode()
            const base64Data = res.data.data.qrcodeBytes // 注意替换为实际数据
            const byteCharacters = atob(base64Data) // 解码Base64
            const byteNumbers = new Array(byteCharacters.length)
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i)
            }
            const byteArray = new Uint8Array(byteNumbers)
            const blob = new Blob([byteArray], { type: 'image/png' })
            // 创建图片URL
            const imgURL = URL.createObjectURL(blob)
            // const img = new Image()
            uri.value = imgURL
            hasWechatCode.value = true
        }
    }
    bindVisible.value = true
}
let bindTimer = null
watch(
    () => bindVisible.value,
    async (newVal) => {
        if (newVal) {
            console.log('[ newVal ] >', newVal)
            // 监听弹窗是否打开，如果打开则调取绑定接口，看当前类型是否已绑定，若已绑定，关闭弹窗
            bindTimer = setInterval(async () => {
                await getBindInfo(deviceType.value)
                if (
                    (weChatInfo.value && deviceType.value == 'WECHAT') ||
                    (dingtalkInfo.value && deviceType.value == 'DING_TALK')
                ) {
                    // 关闭弹窗
                    ElMessage.success(t('Successed'))
                    clearInterval(bindTimer)
                    setTimeout(() => {
                        bindVisible.value = false
                    }, 200)
                }
            }, 2000)
        }
    }
)

const activeSystem = computed(() => {
    return localStorage.getItem('activeSystem')
})

const test = () => {}

const closeDrawer = () => {
    //
    editNameVisible.value = false
    editPhoneVisible.value = false
    editEmailVisible.value = false
    editPswVisible.value = false
    bindVisible.value = false
    deviceType.value = ''
    clearInterval(bindTimer)
}
const editEmailVisible = ref(false)
const onEdit = (e) => {
    //
    console.log('[ e ] >', e)
    if (e === 'username') {
        editNameVisible.value = true
    } else if (e === 'phone') {
        editPhoneVisible.value = true
    } else if (e === 'password') {
        editPswVisible.value = true
    } else if (e === 'email') {
        editEmailVisible.value = true
    }
}
// 修改手机号
const stepOneForm = reactive({
    phone: '',
    CAPTCHA: '',
})
// 修改手机号第一步  获取验证码

const unbind = async (deviceType) => {
    let res = await service.unBindThirdAccount({
        platform: deviceType,
    })
    if (res.data.data) {
        ElMessage.success(t('Successed'))
        await getBindInfo()
    } else {
        ElMessage.error(res.data.msg)
    }
}
const loginInfo = ref({
    realName: '',
    phone: '',
    staffId: '',
})
const getLoginCurrentUserDetail = async () => {
    let res = await api.getLoginCurrentUserDetail()
    loginInfo.value = res.data.data
    stepOneForm.phone = loginInfo.value.phone
}

const onUpdate = () => {
    //
    store.dispatch('user/setUserInfoData')
    closeDrawer()
    getLoginCurrentUserDetail()
}

const goBlack = () => {
    if (activeSystem.value == 'car') {
        router.push({
            path: '/vehicle',
        })
    } else {
        router.push({
            path: '/device',
        })
    }
}
onMounted(async () => {
    //
    console.log('[ route ] >', route.params)
    await getLoginCurrentUserDetail()
    await getBindInfo()
    if (route.params.openPws == '1') {
        onEdit('password')
    }
})
</script>

<style lang="less" scoped>
.user-content {
    background: var(--car-pie-border);
    border-radius: 8px;
    min-height: calc(~'100vh - 140px');
    padding: 16px 16px 40px;
}

:deep(.el-button.is-round) {
    padding: 8px 12px;
}

:deep(.el-form-item__label) {
    color: rgba(34, 34, 34, 0.65);
}
.go-box {
    margin-bottom: 16px;
    .bt-box-go {
        display: inline-block;
        width: 32px;
        height: 32px;
        line-height: 32px;
        background-color: #fff;
        text-align: center;
        border-radius: 4px;

        &:hover {
            background-color: var(--themeColor);
            color: #fff;
        }
    }
}
:deep(.more-icon) {
    width: 20px;
    height: 20px;
}
.user {
    padding-top: 88px;
}
</style>
