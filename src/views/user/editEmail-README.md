# EditEmail 组件使用说明

## 概述

`editEmail.vue` 是一个用于修改用户邮箱地址的组件，基于 `editPhone.vue` 组件开发。该组件提供了两步验证流程：首先验证当前身份，然后输入新邮箱并验证。

## 功能特性

- 两步验证流程：身份验证 → 邮箱修改
- 支持短信验证和邮箱验证两种身份验证方式
- 新邮箱格式验证
- 验证码发送倒计时功能（60秒）
- 防止设置相同邮箱
- 表单验证和错误提示
- 国际化支持
- 抽屉式界面

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible | Boolean | false | 控制抽屉的显示/隐藏 |
| currentPhone | String | '' | 当前手机号，用于身份验证 |
| currentEmail | String | '' | 当前邮箱，用于身份验证和重复检查 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:visible | visible: boolean | 抽屉显示状态变化时触发 |
| confirm | - | 邮箱修改成功后触发 |

## 使用示例

### 基础使用

```vue
<template>
  <div>
    <el-button @click="openEditEmail">修改邮箱</el-button>
    
    <edit-email
      v-model:visible="editEmailVisible"
      :currentPhone="userPhone"
      :currentEmail="userEmail"
      @confirm="handleEmailUpdated"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import EditEmail from '@/views/user/editEmail.vue'

const editEmailVisible = ref(false)
const userPhone = ref('13800138000')
const userEmail = ref('<EMAIL>')

const openEditEmail = () => {
  editEmailVisible.value = true
}

const handleEmailUpdated = () => {
  console.log('邮箱修改成功')
  // 刷新用户信息等操作
}
</script>
```

### 在用户设置页面中使用

```vue
<template>
  <div class="user-settings">
    <div class="setting-item">
      <span>邮箱：{{ userInfo.email }}</span>
      <el-button @click="editEmail">修改</el-button>
    </div>
    
    <edit-email
      v-model:visible="editEmailVisible"
      :currentPhone="userInfo.phone"
      :currentEmail="userInfo.email"
      @confirm="refreshUserInfo"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import EditEmail from '@/views/user/editEmail.vue'

const editEmailVisible = ref(false)
const userInfo = reactive({
  phone: '13800138000',
  email: '<EMAIL>'
})

const editEmail = () => {
  editEmailVisible.value = true
}

const refreshUserInfo = async () => {
  // 重新获取用户信息
  await getUserInfo()
}
</script>
```

## 组件流程

### 第一步：身份验证
1. 使用 `certification` 组件进行身份验证
2. 支持短信验证（使用当前手机号）或邮箱验证（使用当前邮箱）
3. 输入验证码并验证通过后进入第二步

### 第二步：邮箱修改
1. 输入新邮箱地址
2. 点击"获取验证码"发送邮箱验证码到新邮箱
3. 输入验证码
4. 提交修改

## 验证规则

### 新邮箱验证
- 必填项验证
- 邮箱格式验证（使用正则表达式）
- 不能与当前邮箱相同

### 验证码验证
- 必填项验证
- 长度验证（4位）

## 国际化支持

组件支持中英文国际化，使用的文本键包括：

- `ChangeEmail`: 修改邮箱
- `NewEmail`: 新邮箱
- `email_tips02`: 新邮箱不能与旧邮箱相同
- `Please enter a valid email`: 请输入正确的邮箱地址
- `Failed to update email`: 邮箱修改失败
- `Verification code sent successfully`: 验证码发送成功
- `Failed to send verification code`: 验证码发送失败

## API 接口

### 身份验证接口
```javascript
// 二次认证
await service.secondAuth({
  phone: authType === 'sms' ? currentPhone : undefined,
  email: authType === 'email' ? currentEmail : undefined,
  code: verificationCode,
  authType: authType, // 'sms' 或 'email'
})
```

### 邮箱验证码发送接口
```javascript
// 发送邮箱验证码（需要实现）
await service.sendEmailCode({ 
  email: newEmail, 
  emailVerifyType: 'updateEmail' 
})
```

### 邮箱修改接口
```javascript
// 修改邮箱
await api.updateMeInfo({
  email: newEmail,
  emailCode: verificationCode,
})
```

## 注意事项

1. **API接口集成**：
   - 邮箱验证码发送接口需要根据实际项目进行实现
   - 确保 `updateMeInfo` 接口支持邮箱修改参数

2. **验证码发送**：
   - 新邮箱验证码会发送到用户输入的新邮箱地址
   - 确保邮箱服务配置正确

3. **错误处理**：
   - 组件内部已包含基本的错误处理
   - 可根据实际需求调整错误提示

4. **安全考虑**：
   - 修改邮箱需要先进行身份验证
   - 验证码有时效性限制

## 样式定制

组件使用 Element Plus 的抽屉组件，可以通过以下方式自定义样式：

```vue
<style>
/* 自定义抽屉宽度 */
.el-drawer {
  width: 500px !important;
}

/* 自定义表单样式 */
.el-form-item__label {
  color: #606266;
}
</style>
```

## 测试

可以使用提供的测试页面 `src/views/test/editEmail-test.vue` 来测试组件功能：

```bash
# 在路由中添加测试页面
{
  path: '/test/edit-email',
  component: () => import('@/views/test/editEmail-test.vue')
}
```
