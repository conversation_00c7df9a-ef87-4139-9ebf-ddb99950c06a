# Certification 组件使用说明

## 概述

`certification.vue` 是一个通用的身份验证组件，支持短信验证和邮箱验证两种方式。该组件提供了统一的验证码发送和验证功能。

## 功能特性

- 支持短信验证和邮箱验证两种方式
- 自动根据传入的手机号和邮箱判断可用的验证方式
- 验证码发送倒计时功能（60秒）
- 表单验证
- 国际化支持
- 响应式数据输出

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| currentPhone | String | '' | 当前手机号，用于短信验证 |
| currentEmail | String | '' | 当前邮箱，用于邮箱验证 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:authType | authType: string | 当前选择的验证方式（'sms' 或 'email'） |
| update:phone | phone: string | 当前手机号 |
| update:email | email: string | 当前邮箱 |
| update:code | code: string | 当前输入的验证码 |

## 使用示例

### 基础使用

```vue
<template>
  <certification
    :currentPhone="userPhone"
    :currentEmail="userEmail"
    @update:authType="handleAuthTypeUpdate"
    @update:code="handleCodeUpdate"
  />
</template>

<script setup>
import { ref } from 'vue'
import Certification from '@/views/user/certification.vue'

const userPhone = ref('13800138000')
const userEmail = ref('<EMAIL>')
const authType = ref('')
const verificationCode = ref('')

const handleAuthTypeUpdate = (value) => {
  authType.value = value
  console.log('验证方式:', value)
}

const handleCodeUpdate = (value) => {
  verificationCode.value = value
  console.log('验证码:', value)
}
</script>
```

### 在表单中使用

```vue
<template>
  <el-form @submit="handleSubmit">
    <certification
      :currentPhone="form.phone"
      :currentEmail="form.email"
      @update:authType="form.authType = $event"
      @update:code="form.code = $event"
    />
    <el-button type="primary" @click="handleSubmit">提交</el-button>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import Certification from '@/views/user/certification.vue'

const form = reactive({
  phone: '13800138000',
  email: '<EMAIL>',
  authType: '',
  code: ''
})

const handleSubmit = () => {
  console.log('表单数据:', form)
  // 进行验证和提交逻辑
}
</script>
```

## 组件行为

### 验证方式选择逻辑

1. 如果传入了 `currentPhone`，默认选择短信验证
2. 如果没有传入 `currentPhone` 但传入了 `currentEmail`，选择邮箱验证
3. 如果都没有传入，两个选项都会被禁用

### 验证码发送

- 点击"获取验证码"按钮后，会有60秒的倒计时
- 倒计时期间按钮会被禁用
- 发送成功会显示成功提示，失败会显示错误提示

### 表单验证

- 验证码输入框必填
- 验证码长度必须为4位
- 邮箱格式验证（仅邮箱验证时）

## 国际化

组件支持中英文国际化，使用的文本键包括：

- `SMS verification`: 短信验证
- `Email Verification`: 邮箱验证
- `OriginalPhone`: 原手机号
- `Current Email`: 当前邮箱
- `Captcha`: 验证码
- `GetVerificationCode`: 获取验证码
- `Captcha_tips02`: 倒计时提示
- `placeholder_qingshuru`: 请输入
- `Please enter a valid email`: 请输入正确的邮箱地址
- `Verification code sent successfully`: 验证码发送成功
- `Failed to send verification code`: 验证码发送失败

## 注意事项

1. 需要确保项目中已配置好邮箱验证码发送的API接口
2. 组件内部的API调用部分需要根据实际项目的接口进行调整
3. 建议在使用前测试验证码发送功能是否正常
4. 组件会自动清空验证码当切换验证方式时

## API 接口集成

目前组件中的API调用是注释状态，需要根据实际项目进行集成：

```javascript
// 短信验证码发送
await service.sendSmsCode({ 
  phone: phone, 
  smsVerifyType: 'secondAuth' 
})

// 邮箱验证码发送（需要实现）
await service.sendEmailCode({ 
  email: email, 
  emailVerifyType: 'secondAuth' 
})
```
