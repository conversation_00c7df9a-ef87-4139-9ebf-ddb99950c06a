<template>
    <el-drawer
        v-model="drawerVisible"
        :size="486"
        :lockScroll="true"
        :show-close="false"
        @close="closeDrawer"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header">
                    <span>{{ $t('ChangePassword') }}</span>
                </div>
                <div class="flex gap-x-3" v-if="status === 1">
                    <el-button plain round @click="closeDrawer">{{
                        $t('Cancle')
                    }}</el-button>
                    <el-button plain round type="primary" @click="nextStep">{{
                        $t('NextStep')
                    }}</el-button>
                </div>
                <div class="flex gap-x-3" v-if="status === 2">
                    <el-button plain round @click="prevStep">{{
                        $t('PreviousStep')
                    }}</el-button>
                    <el-button
                        plain
                        round
                        type="primary"
                        @click="confirmChange"
                        :disabled="!btnDisabled"
                        >{{ $t('ConfirmTheChanges') }}</el-button
                    >
                </div>
            </div>
        </template>
        <div v-if="status === 1">
            <certification
                :currentPhone="props.currentPhone"
                :currentEmail="props.currentEmail"
                @update:authType="handleAuthTypeUpdate"
                @update:code="handleCodeUpdate"
            />
        </div>
        <div v-if="status === 2">
            <el-form
                ref="stepTwoFormRef"
                style="max-width: 600px"
                :model="stepTwoForm"
                :rules="stepTwoFormRules"
                label-width="auto"
                class="stepTwoForm"
                hide-required-asterisk
                :status-icon="false"
            >
                <el-form-item
                    :label="$t('NewPassword')"
                    prop="newPassword"
                    :label-position="'left'"
                >
                    <el-input
                        v-model="stepTwoForm.newPassword"
                        type="password"
                        :placeholder="$t('Password_tips01')"
                        show-password
                        :maxlength="32"
                    />
                </el-form-item>
                <el-form-item
                    :label="$t('ConfirmPassword')"
                    prop="confirmPassword"
                    :label-position="'left'"
                >
                    <el-input
                        v-model="stepTwoForm.confirmPassword"
                        type="password"
                        :placeholder="$t('Password_tips02')"
                        show-password
                        :maxlength="32"
                    />
                </el-form-item>
            </el-form>
        </div>
    </el-drawer>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import service from '@/apiService/device'
import { useStore } from 'vuex'
import api from '@/apiService/index'
import Cookies from 'js-cookie'
import { useI18n } from 'vue-i18n'
import certification from './certification.vue'

const { t, locale } = useI18n()
const store = useStore()
const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    currentPhone: {
        type: String,
        default: '',
    },
    currentEmail: {
        type: String,
        default: '',
    },
})

const emit = defineEmits(['update:visible', 'confirm'])

const drawerVisible = computed({
    get() {
        return props.visible
    },
    set(value) {
        emit('update:visible', value)
    },
})

// 当前步骤状态
const status = ref(1)

// 表单ref
const stepTwoFormRef = ref(null)

// 第二步表单数据
const stepTwoForm = reactive({
    newPassword: '',
    confirmPassword: '',
})

const regex = /^[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>/?]{8,32}$/
const btnDisabled = computed(() => {
    return (
        regex.test(stepTwoForm.newPassword) &&
        stepTwoForm.newPassword === stepTwoForm.confirmPassword
    )
})

// 验证新密码是否符合规则
const validatePassword = (rule, value, callback) => {
    if (!value) {
        callback(new Error(t('Password_tips03')))
    } else if (value.length < 8) {
        callback(new Error(t('Password_tips04')))
    } else if (!regex.test(value)) {
        callback(new Error(t('Password_tips05')))
    } else {
        callback()
    }
}

// 验证两次密码是否一致
const validateConfirmPassword = (rule, value, callback) => {
    if (!value) {
        callback(new Error(t('Password_tips06')))
    } else if (value !== stepTwoForm.newPassword) {
        callback(new Error(t('Password_tips07')))
    } else {
        callback()
    }
}

const stepTwoFormRules = {
    newPassword: [
        { required: true, validator: validatePassword, trigger: 'blur' },
    ],
    confirmPassword: [
        { required: true, validator: validateConfirmPassword, trigger: 'blur' },
    ],
}

// certification组件相关状态
const authType = ref('sms')
const verificationCode = ref('')

// 处理certification组件的事件
const handleAuthTypeUpdate = (value) => {
    authType.value = value
}

const handleCodeUpdate = (value) => {
    verificationCode.value = value
}

// 下一步
const nextStep = async () => {
    if (!verificationCode.value) {
        ElMessage.error(
            t('placeholder_qingshuru') +
                (locale.value == 'zh' ? '' : ' ') +
                t('Captcha')
        )
        return
    }

    try {
        // 验证码验证
        let res = await service.secondAuth({
            phone: authType.value === 'sms' ? props.currentPhone : undefined,
            email: authType.value === 'email' ? props.currentEmail : undefined,
            code: verificationCode.value,
            authType: authType.value,
        })
        if (res.data.data) {
            status.value = 2
            verificationCode.value = ''
        }
    } catch (error) {
        ElMessage.error(t('Verification failed'))
    }
}

// 上一步
const prevStep = () => {
    status.value = 1
    stepTwoForm.newPassword = ''
    stepTwoForm.confirmPassword = ''
}

// 确认修改
const confirmChange = async () => {
    if (!stepTwoFormRef.value) return
    await stepTwoFormRef.value.validate(async (valid) => {
        if (valid) {
            try {
                let res = await api.updateMeInfo({
                    password: stepTwoForm.newPassword,
                })
                if (res.data.data) {
                    ElMessage.success(t('Password_tips08'))
                    store.commit('user/setHidetip', true)
                    Cookies.set('modifyPasswordFlag', 1, {
                        expires: 7,
                        path: '/',
                        domain: 'ssnj.com',
                    })
                    emit('confirm', 'updatePsw')
                    closeDrawer()
                }
            } catch (error) {
                ElMessage.error(t('Password_tips09'))
            }
        }
    })
}

// 关闭抽屉
const closeDrawer = () => {
    if (status.value === 2) stepTwoFormRef.value?.resetFields()
    status.value = 1
    stepTwoForm.newPassword = ''
    stepTwoForm.confirmPassword = ''
    verificationCode.value = ''

    emit('update:visible', false)
}
</script>

<style scoped></style>
