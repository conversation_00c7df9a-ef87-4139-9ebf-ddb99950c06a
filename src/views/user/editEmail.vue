<template>
    <el-drawer
        v-model="drawerVisible"
        :size="486"
        :lockScroll="true"
        :show-close="false"
        @close="closeDrawer"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header">
                    <span>{{ $t('ChangeEmail') }}</span>
                </div>
                <div class="flex gap-x-3" v-if="status === 1">
                    <el-button plain round @click="closeDrawer">{{
                        $t('Cancle')
                    }}</el-button>
                    <el-button plain round type="primary" @click="nextStep">{{
                        $t('NextStep')
                    }}</el-button>
                </div>
                <div class="flex gap-x-3" v-if="status === 2">
                    <el-button plain round @click="prevStep">{{
                        $t('PreviousStep')
                    }}</el-button>
                    <el-button
                        plain
                        round
                        type="primary"
                        @click="confirmChange"
                        >{{ $t('ConfirmTheChanges') }}</el-button
                    >
                </div>
            </div>
        </template>
        <div v-if="status === 1">
            <certification
                :currentPhone="props.currentPhone"
                :currentEmail="props.currentEmail"
                @update:authType="handleAuthTypeUpdate"
                @update:code="handleCodeUpdate"
            />
        </div>

        <div v-if="status === 2">
            <el-form
                ref="stepTwoFormRef"
                style="max-width: 600px"
                :model="stepTwoForm"
                :rules="stepTwoFormRules"
                label-width="auto"
                class="stepTwoForm"
                hide-required-asterisk
                :status-icon="false"
            >
                <el-form-item
                    :label="$t('NewEmail') + ':'"
                    prop="newEmail"
                    :label-position="'left'"
                >
                    <el-input
                        v-model="stepTwoForm.newEmail"
                        :placeholder="$t('NewEmail')"
                        type="email"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item
                    :label="$t('Captcha') + ':'"
                    prop="captcha"
                    :label-position="'left'"
                >
                    <el-input
                        v-model="stepTwoForm.captcha"
                        :placeholder="$t('Captcha')"
                        :maxlength="6"
                    >
                        <template #append>
                            <el-button
                                plain
                                type="primary"
                                @click="sendEmailCaptcha"
                                :disabled="isEmailCaptchaDisabled"
                            >
                                {{ emailCaptchaButtonText }}
                            </el-button>
                        </template>
                    </el-input>
                </el-form-item>
            </el-form>
        </div>
    </el-drawer>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import service from '@/apiService/device'
import api from '@/apiService/index'
import { useI18n } from 'vue-i18n'
import { email } from '@/common/reg'
import certification from './certification.vue'

const { t, locale } = useI18n()
const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    currentPhone: {
        type: String,
        default: '',
    },
    currentEmail: {
        type: String,
        default: '',
    },
})

const emit = defineEmits(['update:visible', 'confirm'])

// 计算属性用于双向绑定visible
const drawerVisible = computed({
    get() {
        return props.visible
    },
    set(value) {
        emit('update:visible', value)
    },
})

// 当前步骤状态
const status = ref(1)

// 表单ref
const stepTwoFormRef = ref(null)

// 第二步表单数据
const stepTwoForm = reactive({
    newEmail: '',
    captcha: '',
})

// 表单验证规则
const stepTwoFormRules = {
    newEmail: [
        {
            required: true,
            message:
                t('placeholder_qingshuru') +
                (locale.value == 'zh' ? '' : ' ') +
                t('NewEmail'),
            trigger: 'blur',
        },
        {
            pattern: email,
            message: t('Please enter a valid email'),
            trigger: 'blur',
        },
    ],
    captcha: [
        {
            required: true,
            message:
                t('placeholder_qingshuru') +
                (locale.value == 'zh' ? '' : ' ') +
                t('Captcha'),
            trigger: 'blur',
        },
        { min: 6, max: 6, message: t('Captcha_tips03'), trigger: 'blur' },
    ],
}

// 邮箱验证码相关
const isEmailCaptchaDisabled = ref(false)
const emailCaptchaButtonText = ref(t('GetVerificationCode'))
let emailTimer = null

const authType = ref('email')
const verificationCode = ref('')

// 处理certification组件的事件
const handleAuthTypeUpdate = (value) => {
    authType.value = value
}

const handleCodeUpdate = (value) => {
    verificationCode.value = value
}

// 发送邮箱验证码
const sendEmailCaptcha = async () => {
    const newEmail = stepTwoForm.newEmail
    if (!newEmail) {
        ElMessage.error(t('Please enter a valid email'))
        return
    }

    // 验证邮箱格式
    if (!email.test(newEmail)) {
        ElMessage.error(t('Please enter a valid email'))
        return
    }

    // 检查新邮箱是否与当前邮箱相同
    if (newEmail === props.currentEmail) {
        ElMessage.error(t('email_tips02'))
        return
    }

    try {
        // 这里添加发送邮箱验证码的接口调用
        await service.sendEmailCode({
            email: newEmail,
            verifyType: 'updateEmail',
        })

        isEmailCaptchaDisabled.value = true
        let count = 60
        emailTimer = setInterval(() => {
            if (count > 0) {
                emailCaptchaButtonText.value = t('Captcha_tips02').replace(
                    's%',
                    count
                )
                count--
            } else {
                clearInterval(emailTimer)
                emailCaptchaButtonText.value = t('GetVerificationCode')
                isEmailCaptchaDisabled.value = false
            }
        }, 1000)

        ElMessage.success(t('Verification code sent successfully'))
    } catch (error) {
        ElMessage.error(t('Failed to send verification code'))
    }
}

// 下一步
const nextStep = async () => {
    if (!verificationCode.value) {
        ElMessage.error(
            t('placeholder_qingshuru') +
                (locale.value == 'zh' ? '' : ' ') +
                t('Captcha')
        )
        return
    }

    try {
        // 验证码验证
        let res = await service.secondAuth({
            phone: authType.value === 'sms' ? props.currentPhone : undefined,
            email: authType.value === 'email' ? props.currentEmail : undefined,
            code: verificationCode.value,
            authType: authType.value,
        })
        if (res.data.data) {
            status.value = 2
            verificationCode.value = ''
        }
    } catch (error) {
        ElMessage.error(t('Verification failed'))
    }
}

// 上一步
const prevStep = () => {
    status.value = 1
    stepTwoForm.newEmail = ''
    stepTwoForm.captcha = ''
}

// 确认修改
const confirmChange = async () => {
    if (!stepTwoFormRef.value) return
    if (stepTwoForm.newEmail === props.currentEmail) {
        ElMessage.error(t('email_tips02'))
        return
    }
    await stepTwoFormRef.value.validate(async (valid) => {
        if (valid) {
            try {
                // 修改邮箱的接口调用
                let res = await api.updateMeInfo({
                    email: stepTwoForm.newEmail,
                    code: stepTwoForm.captcha,
                })
                if (res.data.code === 0) {
                    ElMessage.success(t('Successed'))
                    emit('confirm')
                    closeDrawer()
                }
            } catch (error) {
                ElMessage.error(t('Failed to update email'))
            }
        }
    })
}

// 关闭抽屉
const closeDrawer = () => {
    // 重置表单和状态
    if (status.value === 2) stepTwoFormRef.value?.resetFields()
    status.value = 1
    stepTwoForm.newEmail = ''
    stepTwoForm.captcha = ''
    verificationCode.value = ''
    if (emailTimer) clearInterval(emailTimer)
    emailCaptchaButtonText.value = t('GetVerificationCode')
    isEmailCaptchaDisabled.value = false

    emit('update:visible', false)
}
</script>

<style scoped></style>
