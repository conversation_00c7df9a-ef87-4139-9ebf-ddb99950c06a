<template>
    <div class="qr-example-container">
        <h2>二维码跳转功能演示</h2>

        <!-- 功能说明 -->
        <div class="description">
            <h3>功能说明</h3>
            <ul>
                <li>
                    生成一个中转页面链接，例如
                    <code>/qr-redirect?sn=设备编号</code>
                </li>
                <li>
                    用户通过任意方式访问该链接：
                    <ul>
                        <li>
                            <b>微信内部扫码或打开链接：</b>
                            显示一个按钮，点击后拉起指定小程序。
                        </li>
                        <li>
                            <b>手机浏览器扫码或打开链接：</b>
                            自动跳转到H5页面
                            <code>/h5?sn=设备编号</code>。
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <!-- 测试区域 -->
        <div class="test-section">
            <h3>测试功能</h3>

            <div class="input-group">
                <label>设备编号 (SN):</label>
                <input
                    v-model="testSn"
                    type="text"
                    placeholder="请输入设备编号"
                    class="sn-input"
                />
            </div>

            <div class="button-group">
                <button @click="openQRRedirectPage" class="btn primary">
                    在本窗口打开中转页
                </button>
                <a
                    :href="qrUrl"
                    target="_blank"
                    class="btn info"
                    rel="noopener noreferrer"
                >
                    在新窗口打开中转页
                </a>
            </div>
        </div>

        <!-- 二维码生成 -->
        <div class="qr-section">
            <h3>二维码/链接</h3>
            <div class="qr-generator">
                <div class="qr-input">
                    <label>将此链接用于二维码:</label>
                    <div class="url-display">
                        {{ qrUrl }}
                    </div>
                    <button @click="copyUrl" class="btn copy">复制链接</button>
                </div>

                <div class="qr-code" v-if="qrUrl">
                    <div class="qr-placeholder">
                        <p>二维码预览</p>
                        <p class="qr-text" style="color: red">
                            (此为静态预览，请使用上方链接生成真实二维码)
                        </p>
                        <small>扫描生成的二维码，即可测试跳转流程</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 环境检测信息 -->
        <div class="env-info">
            <h3>当前环境信息 (本页面)</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="label">是否微信环境:</span>
                    <span class="value" :class="{ active: envInfo.isWechat }">
                        {{ envInfo.isWechat ? '是' : '否' }}
                    </span>
                </div>
                <div class="info-item">
                    <span class="label">是否移动端:</span>
                    <span class="value" :class="{ active: envInfo.isMobile }">
                        {{ envInfo.isMobile ? '是' : '否' }}
                    </span>
                </div>
                <div class="info-item full-width">
                    <span class="label">用户代理:</span>
                    <span class="value user-agent">{{
                        envInfo.userAgent
                    }}</span>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="usage-guide">
            <h3>实现说明</h3>
            <div class="code-example">
                <h4>1. 创建中转页 <code>qrRedirect.vue</code></h4>
                <!-- <pre><code>// qrRedirect.vue
// onMounted 时:
if (isWechat()) {
  // 微信环境: 初始化 JSSDK
  await initWechatSdk(...)
  // SDK 成功后，<wx-open-launch-weapp> 标签会显示并生效
} else {
  // 其他环境: 直接跳转到H5
  redirectToH5(...)
}</code></pre>

                <h4>2. 后端提供JSSDK配置接口</h4>
                <pre><code>// 后端需要提供一个接口 (例如 /wechat/get-jssdk-config)
// 用于根据前端传入的URL，返回有效的 JSSDK 配置
// { appId, timestamp, nonceStr, signature }</code></pre>

                <h4>
                    3. 在 <code>qrRedirect.vue</code> 中配置小程序信息
                </h4>
                <pre><code>// qrRedirect.vue
const miniProgramAppId = 'wxd...' // 小程序AppId
const miniProgramOriginalId = 'gh_...' // 小程序原始ID
const miniProgramPath = `packageDevice/detail?deviceSn=${sn}` // 小程序页面路径
</code></pre> -->
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { isWechat, isMobile } from '@/utils/qrCodeRedirect'

const router = useRouter()

// 响应式数据
const testSn = ref('866597074507136')

// 环境信息
const envInfo = ref({
    isWechat: false,
    isMobile: false,
    userAgent: '',
})

// 生成二维码URL
const qrUrl = computed(() => {
    if (!testSn.value) return ''
    // 注意：需要根据您的路由模式（hash或history）调整URL格式
    return `${window.location.origin}/#/qr-redirect?sn=${testSn.value}`
})

// 检测环境信息
const detectEnvironment = () => {
    envInfo.value = {
        isWechat: isWechat(),
        isMobile: isMobile(),
        userAgent:
            typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown',
    }
}

// 打开跳转页面
const openQRRedirectPage = () => {
    if (!testSn.value) {
        alert('请输入设备编号')
        return
    }
    router.push({
        path: '/qr-redirect',
        query: { sn: testSn.value },
    })
}

// 复制链接
const copyUrl = async () => {
    if (!qrUrl.value) return
    try {
        await navigator.clipboard.writeText(qrUrl.value)
        alert('链接已复制到剪贴板')
    } catch (error) {
        console.error('复制失败:', error)
        alert('复制失败，请手动复制')
    }
}

onMounted(() => {
    detectEnvironment()
})
</script>

<style lang="less" scoped>
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;

    &.primary {
        background: #3edacd;
        color: white;
        &:hover {
            background: #35c5b8;
        }
    }

    &.info {
        background: #17a2b8;
        color: white;
        &:hover {
            background: #138496;
        }
    }

    &.copy {
        background: #28a745;
        color: white;
        &:hover {
            background: #218838;
        }
    }
}

a.btn {
    line-height: normal;
}

.qr-example-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;

    h2,
    h3 {
        color: #333;
        margin-bottom: 20px;
    }

    h2 {
        text-align: center;
        font-size: 28px;
        margin-bottom: 30px;
    }

    h3 {
        font-size: 20px;
        border-bottom: 2px solid #3edacd;
        padding-bottom: 8px;
    }
}

.description {
    background: #f5f5f5;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;

    ul {
        margin: 10px 0;
        padding-left: 20px;

        li {
            margin-bottom: 8px;
            line-height: 1.5;

            code {
                background: #e9ecef;
                padding: 2px 6px;
                border-radius: 3px;
            }
        }
    }
}

.test-section {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;

    .input-group {
        margin-bottom: 20px;

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .sn-input {
            width: 100%;
            max-width: 400px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;

            &:focus {
                outline: none;
                border-color: #3edacd;
            }
        }
    }

    .button-group {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
    }
}

.qr-section {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;

    .qr-generator {
        display: grid;
        grid-template-columns: 1fr 300px;
        gap: 20px;

        .qr-input {
            label {
                display: block;
                margin-bottom: 8px;
                font-weight: 500;
            }

            .url-display {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                margin-bottom: 10px;
                word-break: break-all;

                font-size: 14px;
            }
        }

        .qr-code {
            .qr-placeholder {
                border: 2px dashed #ddd;
                border-radius: 8px;
                padding: 20px;
                text-align: center;
                background: #f9f9f9;

                .qr-text {
                    font-size: 12px;
                    word-break: break-all;
                    margin: 10px 0;
                    color: #666;
                }

                small {
                    color: #999;
                }
            }
        }
    }
}

.env-info {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;

    .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;

        .info-item {
            display: flex;
            // justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;

            &.full-width {
                grid-column: 1 / -1;
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .label {
                font-weight: 500;
                color: #555;
            }

            .value {
                color: #666;

                &.active {
                    color: #28a745;
                    font-weight: 600;
                }

                &.user-agent {
                    font-size: 12px;
                    word-break: break-all;
                }
            }
        }
    }
}

.usage-guide {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;

    .code-example {
        h4 {
            color: #495057;
            margin: 20px 0 10px;
            font-size: 16px;
        }

        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            margin-bottom: 20px;

            code {
                font-size: 14px;
                line-height: 1.5;
            }
        }
    }
}

@media (max-width: 768px) {
    .qr-generator {
        grid-template-columns: 1fr !important;
    }

    .info-grid {
        grid-template-columns: 1fr !important;
    }

    .button-group {
        flex-direction: column;
    }
}
</style>
