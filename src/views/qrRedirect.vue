<template>
    <div class="qr-redirect-container">
        <div class="loading-card">
            <div class="spinner" v-if="isLoading"></div>
            <div class="icon success" v-else-if="redirectStatus === 'success'">
                ✓
            </div>
            <div class="icon error" v-else-if="redirectStatus === 'error'">
                ✗
            </div>

            <h2>{{ title }}</h2>
            <p class="message">{{ message }}</p>

            <div class="environment-info" v-if="showDebugInfo">
                <h4>环境检测信息 (启动时)</h4>
                <ul>
                    <li>是否微信环境: {{ envInfo.isWechat ? '是' : '否' }}</li>
                    <li>是否移动端: {{ envInfo.isMobile ? '是' : '否' }}</li>
                    <li>用户代理: {{ envInfo.userAgent }}</li>
                </ul>
            </div>

            <div class="actions" v-if="showActions">
                <button @click="startRedirect" class="btn primary">重试</button>
                <button @click="manualRedirect" class="btn secondary">
                    手动跳转到H5
                </button>
                <button @click="toggleDebugInfo" class="btn text">
                    {{ showDebugInfo ? '隐藏' : '显示' }}调试信息
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { smartRedirect, isWechat, isMobile } from '@/utils/qrCodeRedirect'
import { ElMessage } from 'element-plus'

const route = useRoute()

// 响应式数据
const isLoading = ref(true)
const redirectStatus = ref('') // 'success', 'error'
const title = ref('正在准备跳转...')
const message = ref('请稍候，我们将根据您的环境自动跳转。')
const showActions = ref(false)
const showDebugInfo = ref(false)

// 环境信息
const envInfo = ref({
    isWechat: isWechat(),
    isMobile: isMobile(),
    userAgent:
        typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown',
})

// 配置选项
const redirectOptions = {
    miniProgramAppId: 'wxd1b8a814502a9a16', // 实际的小程序AppId
    h5BaseUrl: window.location.origin, // 当前域名
}

// 从URL获取设备SN
const sn = ref(route.query.sn)

// 执行跳转
const startRedirect = async () => {
    if (!sn.value) {
        isLoading.value = false
        redirectStatus.value = 'error'
        title.value = '参数错误'
        message.value = 'URL中缺少设备编号 (sn) 参数。'
        showActions.value = true
        return
    }

    isLoading.value = true
    redirectStatus.value = ''
    showActions.value = false
    title.value = '智能跳转中...'
    message.value = '正在分析环境，即将跳转...'

    try {
        const target = await smartRedirect(sn.value, redirectOptions)

        isLoading.value = false
        redirectStatus.value = 'success'
        if (target === 'miniProgram') {
            title.value = '已发起跳转'
            message.value =
                '正在打开微信小程序... 如果长时间没有响应，请尝试手动操作。'
        } else {
            title.value = '跳转完成'
            message.value = '已成功跳转到H5页面。'
        }
        // smartRedirect 内部会完成跳转，这里不需要再做额外操作
    } catch (error) {
        console.error('智能跳转失败:', error)
        isLoading.value = false
        redirectStatus.value = 'error'
        title.value = '跳转失败'
        message.value =
            error.message || '无法自动跳转，请尝试手动操作或刷新页面。'
        showActions.value = true
    }
}

// 手动跳转到H5
const manualRedirect = () => {
    const h5Url = `${redirectOptions.h5BaseUrl}/#/h5?sn=${sn.value}`
    window.location.href = h5Url
}

// 切换调试信息显示
const toggleDebugInfo = () => {
    showDebugInfo.value = !showDebugInfo.value
}

// 页面挂载时执行
onMounted(() => {
    // 延迟一小段时间再开始，给用户一个缓冲
    setTimeout(startRedirect, 500)
})
</script>

<style lang="less" scoped>
.qr-redirect-container {
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.loading-card {
    background: white;
    border-radius: 16px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    max-width: 400px;
    width: 100%;

    h2 {
        margin: 20px 0 10px;
        color: #333;
        font-size: 24px;
        font-weight: 600;
    }

    .message {
        color: #666;
        font-size: 16px;
        line-height: 1.5;
        margin-bottom: 30px;
    }
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #e3e3e3;
    border-top: 4px solid #3edacd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 24px;
    font-weight: bold;
    color: white;

    &.success {
        background: #4caf50;
    }

    &.error {
        background: #f44336;
    }
}

.environment-info {
    background: #f5f5f5;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    text-align: left;

    h4 {
        margin: 0 0 15px;
        color: #333;
        font-size: 16px;
    }

    ul {
        margin: 0;
        padding: 0;
        list-style: none;

        li {
            padding: 5px 0;
            font-size: 14px;
            color: #666;
            border-bottom: 1px solid #eee;

            &:last-child {
                border-bottom: none;
            }
        }
    }
}

.actions {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s;

        &.primary {
            background: #3edacd;
            color: white;

            &:hover {
                background: #35c5b8;
            }
        }

        &.secondary {
            background: #f5f5f5;
            color: #333;

            &:hover {
                background: #e0e0e0;
            }
        }

        &.text {
            background: transparent;
            color: #666;
            font-size: 14px;

            &:hover {
                color: #3edacd;
            }
        }
    }
}

// 响应式设计
@media (max-width: 480px) {
    .qr-redirect-container {
        padding: 10px;
    }

    .loading-card {
        padding: 30px 20px;

        h2 {
            font-size: 20px;
        }

        .message {
            font-size: 14px;
        }
    }
}
</style>
