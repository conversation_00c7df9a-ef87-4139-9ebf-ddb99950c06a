import * as echarts from 'echarts'
import { nationMapData, provienceMapData } from './maps/mapData'
import { isObject, cloneDeep } from 'lodash'
import { genMapChartOption } from './const'

export const genExpectedData = (
    value,
    upperLimit,
    fixedLength = 2,
    canReduce,
    sliceLength=-10
) => {
    const random = Math.ceil(9 - Math.random() * 10)
    const isArray = Array.isArray(value)
    const baseValue = isArray ? value[value.length - 1] : value
    let increased = 0
    function getIncreased(base) {
        if (random > 0 || canReduce) {
            const expected = +base + (upperLimit * random) / 10
            if (expected) {
                return +expected.toFixed(fixedLength)
            } else return expected
        } else {
            return base
        }
    }
    if(!upperLimit) return isArray ? [...value, 0].slice(sliceLength) : value
    increased = getIncreased(baseValue)
    return isArray ? [...value, increased].slice(sliceLength) : increased
    // return isArray ? [...value, increased] : increased
}

export const countNum = function(num=0,each=0){
    if(!num) return 0 + each
    const newNum = num + each
    return newNum
}

export const pushInLengthLimitedList = (list = [], values, limited = 30) => {
    if (!Array.isArray(values)) {
        values = [values]
    }
    return list.concat(values).slice(-limited)
}

export const decorateRankRatioFromSelfList = (
    list = [],
    key,
    decoratedKey = 'ratio'
) => {
    let max = 0
    list.forEach(item => {
        if (+max < +item[key]) max = +item[key]
    })

    if (max) {
        list.forEach(item => {
            item[decoratedKey] = +(item[key] / max).toFixed(2)
        })
    }
    list.sort((a,b)=>{
       return b[key] - a[key] 
    })
    return list
}

const findElem = (arrayToSearch, attr, val) => {
    if (arrayToSearch != null) {
        for (var i = 0; i < arrayToSearch.length; i++) {
            // eslint-disable-next-line eqeqeq
            if (arrayToSearch[i][attr] == val) {
                return i
            }
        }
    } else {
        return -1
    }
    return -1
}

export const initMap = name => {
    if (name && !provienceMapData[name]) {
        return
    }
    const mapData = Object.assign(
        {},
        name ? provienceMapData[name] : nationMapData
    )
    echarts.registerMap(name || 'china', mapData)
    return genMapChartOption([], name || 'china')
}


function fullClose(n,m) {
    var result = Math.random()*(m+1-n)+n;
    while(result>m) {
        result = Math.random()*(m+1-n)+n;
    }
    return result;
} 

export const randomFunction = (num,biliary)=>{
    if(!num) num = 0
    if(!biliary) biliary = 0
    return num + biliary
}
