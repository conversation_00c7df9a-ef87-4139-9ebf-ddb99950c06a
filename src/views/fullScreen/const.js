// import mapBg from '@/assets/fullScreen/device/bgCountry.png'

export const chargeChartOption = {
    xAxis: {
        type: 'category',
        data: [],
        show: true,
        axisLabel: {
            show: true,
        },
        axisTick: {
            show: false,
        },
        axisLine: {
            show: false,
            lineStyle: {
                color: 'rgba(255,255,255,0.2)',
                type: 'solid',
            },
        },
    },

    legend: [
        {
            data: ['充电'],
            top: `4%`,
            right: `21%`,
            icon: `path://M127.648 128.113H894.54v766.891H127.65V128.113z`,
            itemStyle: { color: `#3ECDDA` },
            textStyle: {
                color: `#FFF`,
                lineHeight: 2,
            },
        },
        {
            data: ['放电'],
            top: `4%`,
            right: `4%`,
            icon: `path://M127.648 128.113H894.54v766.891H127.65V128.113z`,
            itemStyle: { color: `#73ADFF` },
            textStyle: {
                color: `#FFF`,
                lineHeight: 2,
            },
        },
    ],
    grid: {
        top: `25%`,
        left: `13%`,
        height: `65%`,
        width: `80%`,
        borderWidth: 1,
    },
    yAxis: {
        type: 'value',
        splitLine: {
            lineStyle: { type: 'dashed', color: `rgba(255,255,255,0.2)` },
        },
        name: '单位(kWh)',
        nameTextStyle: {
            padding: [0, 25, 0, 0],
        },
    },
    series: [
        {
            name: '充电',
            data: [],
            type: 'line',
            smooth: true,
            symbol: 'none',
            lineStyle: { color: `#3ECDDA` },
        },
        {
            name: '放电',
            data: [],
            type: 'line',
            smooth: true,
            symbol: 'none',
            lineStyle: { color: `#73ADFF` },
        },
    ],
}
export const earningsChartOption = {
    xAxis: {
        type: 'category',
        data: [],
        show: true,
        axisLabel: {
            show: true,
        },
        axisTick: {
            show: false,
        },
        axisLine: {
            show: false,
            lineStyle: {
                color: 'rgba(255,255,255,0.2)',
                type: 'solid',
            },
        },
    },

    legend: {
        // data: ['收益'],
        top: `4%`,
        right: `4%`,
        icon: `path://M127.648 128.113H894.54v766.891H127.65V128.113z`,
        itemStyle: { color: `#3ECDDA` },
        textStyle: {
            color: `#FFF`,
            lineHeight: 2,
        },
    },
    grid: {
        top: `25%`,
        left: `13%`,
        height: `65%`,
        width: `80%`,
        borderWidth: 1,
    },
    yAxis: {
        type: 'value',
        splitLine: {
            lineStyle: { type: 'dashed', color: `rgba(255,255,255,0.2)` },
        },
        name: '单位(元)',
        nameTextStyle: {
            padding: [0, 25, 0, 0],
        },
    },
    series: [
        {
            // name: '收益',
            smooth: true,
            data: [],
            type: 'line',
            symbol: 'none',
            lineStyle: { color: `#3ECDDA` },
        },
    ],
}

const convertData = (data) => {
    var res = []
    for (var i = 0; i < data.length; i++) {
        const mapData = data[i].hoverObj
        const center = data[i].mapProperty.center

        if (center && mapData) {
            res.push({
                name: mapData.name,
                value: center.concat(mapData.num),
            })
        }
    }
    return res
}

const img = require('@/assets/device/111.png')
// export const genMapChartOption = (data = [], name) => {
//     // const scale = name == 'china' ? 0.8 : 0.6
//     // const layoutSize = name == 'china' ? '125%' : '90%'
//     return {
//         tooltip: {
//             show: false,
//             formatter: function (params) {
//                 return `<span>${params.name}</span>`
//             },
//         },
//         geo: [
//             {
//                 tooltip: {
//                     show: false,
//                 },
//                 show:true,
//                 map: name,
//                 layoutCenter: ['50%', '50%'],
//                 zoom: 1.21,
//                 // layoutSize: layoutSize,
//                 label: {
//                     show: false,
//                 },
//                 aspectScale:0.85,
//                 itemStyle: {
//                     normal: {
//                         borderColor: '#B4FBFF',
//                         borderWidth: 1,
//                         shadowColor: '#B4FBFF',
//                         showBlur:5,
//                         shadowOffsetX: 2,
//                         shadowOffsetY:-1,

//                     },
//                     emphasis: {
//                         // areaColor: '#0f5d9d',
//                     },
//                 },
//                 zlevel: -1,
//                 regions: [
//                     {
//                         name: '南海诸岛',
//                         itemStyle: {

//                             borderColor: '#B4FBFF',
//                             normal: {
//                                 opacity: 0,
//                                 label: {
//                                     show: false,
//                                     color: '#009cc9',
//                                 },
//                             },
//                         },
//                         label: {
//                             show: false,
//                             color: '#FFFFFF',
//                             fontSize: 12,
//                         },
//                     },
//                 ],
//                 silent:true
//             },
//         ],
//         series: [
//             {
//                 type: 'map',
//                 mapType: name,
//                 aspectScale:0.85,
//                 layoutCenter: ['50%', '50%'], //地图位置
//                 // layoutSize: layoutSize,
//                 zlevel: 0,
//                 zoom: 1.2, //当前视角的缩放比例
//                 // roam: true, //是否开启平游或缩放
//                 scaleLimit: {
//                     //滚轮缩放的极限控制
//                     min: 1,
//                     max: 2,
//                 },
//                 selectedMode: 'false',
//                 label: {
//                     show: false,
//                     color: '#FFFFFF',
//                     fontSize: 10,
//                 },
//                 itemStyle: {
//                     normal: {
//                         // areaColor: 'rgba(16,55,111,0.9)',
//                         areaColor:'rgb(40,55,91)',
//                         // borderColor: '#3672A4',
//                         borderColor:'rgb(63,111,205)',
//                         borderWidth: 1,
//                     },
//                     emphasis: {
//                         areaColor: '#56b1da',
//                         label: {
//                             show: true,
//                             color: '#fff',
//                         },
//                     },
//                 },
//                 data: [],
//             },
//             {
//                 tooltip: {
//                     show: false,
//                 },
//                 mapType: name,
//                 name: 'Top 5',
//                 type: 'scatter',
//                 coordinateSystem: 'geo',
//                 symbol: 'image://' + img,
//                 symbolSize: [15, 15],
//                 selected: false,
//                 label: {
//                     normal: {
//                         show: false,
//                         formatter(value) {
//                             return value.data.value[2]
//                         },
//                     },
//                 },
//                 itemStyle: {
//                     show: false,
//                     normal: {
//                         //   color: '#D8BC37', //标志颜色
//                     },
//                 },
//                 data: [],
//                 showEffectOn: 'render',
//                 rippleEffect: {
//                     brushType: 'stroke',
//                 },
//                 hoverAnimation: false,
//                 zlevel: 2,
//             },
//         ],
//     }
// }

export const genMapChartOption = (data = [], name) => {
    return {
        tooltip: {
            trigger: 'item',
            show: false,
            enterable: true,
            textStyle: {
                fontSize: 20,
                color: '#fff',
            },
            // backgroundColor: 'rgba(0,2,89,0.8)',
            formatter: '{b}<br />{c}',
        },
        geo: [
            {
                left: 90,
                top: 60,
                right: 90,
                bottom: 60,
                map: name,
                // aspectScale: 0.85,
                roam: false, // 是否允许缩放
                zoom: 1.2, // 默认显示级别
                // layoutSize: '105%',
                // layoutCenter: ['50%', '50%'],
                itemStyle: {
                    normal: {
                        areaColor: {
                            type: 'linear-gradient',
                            x: 0,
                            y: 400,
                            x2: 0,
                            y2: 0,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: 'rgba(67,85,128,0.3)', // 0% 处的颜色
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(33,49,81,0.3)', // 50% 处的颜色
                                },
                            ],
                            global: false, // 缺省为 false
                        },
                        borderColor: 'rgb(63,111,205,0.8)',
                        borderWidth: 1,
                    },
                    emphasis: {
                        areaColor: 'rgb(124,196,239)',
                        label: {
                            show: true,
                        },
                    },
                },
                emphasis: {
                    itemStyle: {
                        areaColor: '#0160AD',
                    },
                    label: {
                        color: '#fff',
                    },
                },
                select: {
                    //这个就是鼠标点击后，地图想要展示的配置
                    disabled: false, //可以被选中
                    itemStyle: {
                        //相关配置项很多，可以参考echarts官网
                        areaColor: '#77e8e4', //选中
                    },
                },
                zlevel: 3,
                regions: [
                    {
                        name: '南海诸岛',
                        itemStyle: {
                            borderColor: '#B4FBFF',
                            normal: {
                                opacity: 0,
                                label: {
                                    show: false,
                                    color: '#fff',
                                },
                            },
                        },
                        label: {
                            show: false,
                            color: '#FFFFFF',
                            fontSize: 12,
                        },
                    },
                ],
            },
            {
                left: 90,
                top: 60,
                right: 90,
                bottom: 60,
                map: name,
                // aspectScale: 0.85,
                roam: false, // 是否允许缩放
                zoom: 1.2, // 默认显示级别
                // layoutSize: '105%',
                // layoutCenter: ['50%', '50%'],
                itemStyle: {
                    normal: {
                        borderColor: 'rgb(39,57,96,0.8)',
                        borderWidth: 1,
                        // shadowColor: '#2C99F6',
                        shadowOffsetY: 0,
                        // shadowBlur: 60,
                        areaColor: 'rgba(29,85,139,.2)',
                    },
                },
                zlevel: 2,
                silent: true,

            },
            // {
            //     left:90,
            //     top:65,
            //     right:90,
            //     bottom:52,
            //     map: name,
            //     // aspectScale: 0.82,
            //     roam: false, // 是否允许缩放
            //     zoom: 1.2, // 默认显示级别
            //     // layoutSize: '105%',
            //     // layoutCenter: ['50%', '51.5%'],
            //     itemStyle: {
            //         // areaColor: '#005DDC',
            //         areaColor: 'rgb(39,57,96,0.4)',
            //         borderColor: 'rgb(39,57,96)',
            //         borderWidth: 1,
            //     },
            //     zlevel: 1,
            //     silent: true,
            // },
        ],
        series: [
            // map
            {
                selectedMode: 'false',
                geoIndex: 0,
                // coordinateSystem: 'geo',
                showLegendSymbol: true,
                type: 'map',
                roam: true,
                label: {
                    normal: {
                        show: false,
                        textStyle: {
                            color: '#fff',
                        },
                    },
                    emphasis: {
                        show: false,
                        textStyle: {
                            color: '#fff',
                        },
                    },
                },
                itemStyle: {
                    normal: {
                        borderColor: '#2ab8ff',
                        borderWidth: 1.5,
                        areaColor: '#12235c',
                    },
                    emphasis: {
                        areaColor: '#2AB8FF',
                        borderWidth: 0,
                        color: 'red',
                    },
                },
                map: name, // 使用
                data: [],
                select: {
                    //这个就是鼠标点击后，地图想要展示的配置
                    disabled: true, //可以被选中
                    itemStyle: {
                        //相关配置项很多，可以参考echarts官网
                        areaColor: '#77e8e4', //选中
                    },
                },
                // data: this.difficultData //热力图数据   不同区域 不同的底色
            },
            {
                geoIndex: 0,
                tooltip: {
                    show: false,
                },
                mapType: name,
                type: 'scatter',
                coordinateSystem: 'geo',
                symbol: 'image://' + img,
                symbolSize: [15, 15],
                selected: false,
                label: {
                    normal: {
                        show: false,
                        formatter(value) {
                            return value.data.value[2]
                        },
                    },
                },
                itemStyle: {
                    show: false,
                    normal: {
                        //   color: '#D8BC37', //标志颜色
                    },
                },
                data: [],
                showEffectOn: 'render',
                rippleEffect: {
                    brushType: 'stroke',
                },
                hoverAnimation: false,
                zlevel: 3,
            },
        ],
    }
}

export const someMax = (data, num) => {
    const isBoolean = data.some((item) => item >= num)
    return isBoolean
}
