<template>
     <!-- :style="`width: calc(${fullWidth} * ${ratio});${style};`" -->
    <em
        :class="`barProgress ${$props.class}`"
        :style="{width,...style}"
    ></em>
</template>
<script>
import { reactive, toRefs, computed } from 'vue'
export const BarProgress = {
    name: `BarProgress`,
    props: {
        style: {type: String, default: ''},
        class: {type: String, default: ''},
        fullWidth: {
            type: [String, Number],
            default: 100,
        },
        ratio: {
            type: [String, Number],
            default: 1,
            validator: v => v <= 1 && v >= 0,
        }, // 0-1
    },
    setup(props) {
        const width = computed(()=>{
            return `${props.fullWidth * props.ratio}%`
        })
        return {
            width
        }
    },
}

export default BarProgress
</script>
<style lang="less" scoped>
.barProgress {
    height: 28px;
    display: inline-block;
    background: linear-gradient(270deg, #005a9b 0%, #00da85 100%);
    border-radius: 4px;
    border: 1px solid;
    transform: width .3s;
    border-image: linear-gradient(
            180deg,
            rgba(0, 220, 235, 1),
            rgba(0, 182, 211, 1)
        )
        1 1;
}
</style>
