<!--
    图表
    @params: width 宽度
    @params: height 高度
    @params: autoResize 是否自动调整大小
    @params: chartOption 图表的配置
-->
<template>
    <div class="chart">
        <div ref="chart" :style="{ height: height, width: width }" />
    </div>
</template>
<script>
// 引入 echarts 核心模块，核心模块提供了 echarts 使用必须要的接口。
import * as echarts from 'echarts/core'
// 引入柱状图图表，图表后缀都为 Chart
import { BarChart } from 'echarts/charts'
// 引入提示框，标题，直角坐标系组件，组件后缀都为 Component
import {
    TitleComponent,
    TooltipComponent,
    GridComponent,
} from 'echarts/components'
// 引入 Canvas 渲染器，注意引入 CanvasRenderer 或者 SVGRenderer 是必须的一步
import { CanvasRenderer } from 'echarts/renderers'
import { initMap } from '../utils'

// 注册必须的组件
echarts.use([
    TitleComponent,
    TooltipComponent,
    GridComponent,
    Bar<PERSON><PERSON>,
    CanvasRenderer,
])

export const ChartViewer = {
    name: 'ChartViewer',
    props: {
        width: {
            type: String,
            default: '100%',
        },
        height: {
            type: String,
            default: '350px',
        },
        autoResize: {
            type: Boolean,
            default: true,
        },
        chartOption: {
            type: Object,
            required: true,
        },
        type: {
            type: String,
            default: 'canvas',
        },
        playHighlight: {
            type: Boolean,
            default: false,
        },
        reset: {
            default: false,
            type: Boolean,
        },
    },
    data() {
        return {
            chart: null,
            // 动画定时器
            actionTimer: '',
            currentIndex: -1,
            timeFn: undefined,
        }
    },
    watch: {
        chartOption: {
            deep: true,
            handler(newVal) {
                this.setOptions(newVal)
            },
        },
        reset: (newVal) => {
            if (newVal) {
                this.$emit('optionChange', initMap())
                this.$emit('update:reset', false)
            }
        },
    },
    mounted() {
        this.initChart()
        if (this.autoResize) {
            window.addEventListener('resize', this.resizeHandler)
        }
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        if (this.autoResize) {
            window.removeEventListener('resize', this.resizeHandler)
        }
        this.chart.dispose()
        this.chart = null

        clearInterval(this.actionTimer)
        this.actionTimer = null
    },
    methods: {
        resizeHandler() {
            this.chart.resize()
        },
        initChart() {
            const that = this
            this.chart = echarts.init(this.$refs.chart, '', {
                renderer: this.type,
            })
            this.chart.setOption(this.chartOption)
            // this.chart.on('mouseover',function(params){
            //     if(params.componentType == "series"){
            //         this.chart.dispatchAction({

            //         })
            //     }

            // })
            this.chart.on('click', this.handleClick)
            this.chart.on('dblclick', (params) => {
                //当双击事件发生时，清除单击事件，仅响应双击事件
                clearTimeout(this.timeFn)
                //返回全国地图
                this.$emit('optionChange', initMap())
            })
            this.chart.resize()
        },
        handleClick(params) {
            // this.chart.on('click', params => {
            //     clearTimeout(this.timeFn)
            //     //由于单击事件和双击事件冲突，故单击的响应事件延迟250毫秒执行
            //     this.timeFn = setTimeout(() => {
            //         const opt = initMap(params.name)
            //         if (opt && params.name) {
            //             this.$emit('optionChange', opt)
            //         }
            //     }, 250)
            // })
            // this.$emit('click', params)

            clearTimeout(this.timeFn)
            //由于单击事件和双击事件冲突，故单击的响应事件延迟250毫秒执行
            this.timeFn = setTimeout(() => {
                const opt = initMap(params.name)
                if (opt && params.name) {
                    this.$emit('optionChange', opt)
                }
            }, 250)
        },
        setOptions(option) {
            this.clearChart()
            this.resizeHandler()
            if (this.chart) {
                this.chart.setOption(option)
            }
        },
        refresh() {
            this.setOptions(this.chartOption)
        },
        clearChart() {
            this.chart && this.chart.clear()
        },
    },
}

export default ChartViewer
</script>
<style scoped lang="scss"></style>
