/**
 * 二维码扫码跳转工具
 * 根据扫码环境自动跳转到对应页面
 */

import wx from 'weixin-js-sdk'
import request from '@/common/request'
import api from '@/api/h5'

let isSdkReady = false
let sdkError = null

/**
 * 初始化微信JS-SDK
 * @param {string} appId - 公众号的唯一标识
 * @param {array} jsApiList - 需要使用的JS接口列表
 * @returns {Promise<void>}
 */
function initWechatSdk(appId, jsApiList) {
    return new Promise((resolve, reject) => {
        if (isSdkReady) {
            resolve()
            return
        }
        if (sdkError) {
            reject(sdkError)
            return
        }

        const url = window.location.href.split('#')[0]
        request
            .post(api.getWechatConfig, { url })
            .then((res) => {
                if (res.code !== 200) {
                    throw new Error(
                        res.msg || '获取微信JSSDK配置失败'
                    )
                }

                wx.config({
                    debug: process.env.NODE_ENV === 'development', // 开启调试模式
                    appId: 'wxd1b8a814502a9a16',
                    timestamp: Date.now(),
                    nonceStr: ' 1',
                    signature: ' ',
                    jsApiList,
                    openTagList: ['wx-open-launch-weapp'], // 补充开放标签列表
                })

                wx.ready(() => {
                    isSdkReady = true
                    resolve()
                })

                wx.error((err) => {
                    sdkError = err
                    alert(`微信JSSDK配置失败:${err}` )
                    reject(new Error('微信JSSDK配置失败'))
                })
            })
            .catch((err) => {
                sdkError = err
                console.error(`请求微信JSSDK配置失败:${err}` )
                reject(err)
            })
    })
}

/**
 * 检测是否为微信环境
 * @returns {boolean}
 */
export function isWechat() {
    if (typeof navigator === 'undefined') return false
    const ua = navigator.userAgent.toLowerCase()
    return /micromessenger/.test(ua)
}

/**
 * 检测是否为移动端
 * @returns {boolean}
 */
export function isMobile() {
    if (typeof navigator === 'undefined') return false
    const ua = navigator.userAgent.toLowerCase()
    return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/.test(
        ua
    )
}

/**
 * 检测是否为微信小程序环境
 * @returns {Promise<boolean>}
 */
export function isMiniProgram() {
    return new Promise((resolve) => {
        if (!isWechat() || !isSdkReady) {
            resolve(false)
            return
        }
        wx.miniProgram.getEnv((res) => {
            resolve(res.miniprogram)
        })
    })
}

/**
 * 跳转到微信小程序
 * @param {string} sn - 设备编号
 * @param {object} options - 配置项
 * @param {string} options.miniProgramAppId - 小程序AppId
 */
export function redirectToMiniProgram(sn, options) {
    const { miniProgramAppId } = options
    if (!miniProgramAppId) {
        alert('缺少小程序AppId (miniProgramAppId)')
    }

    // 根据实际小程序页面路径进行修改
    const path = `/packageDevice/detail?deviceSn=${sn}`

    wx.miniProgram.navigateTo({
        url: path,
        appId: miniProgramAppId,
        envVersion: process.env.NODE_ENV === 'development' ? 'develop' : 'release',
        fail: (err) => {
            alert(`跳转失败：${JSON.stringify(err)}`)
            // 可以选择在这里添加降级逻辑，例如弹窗提示用户手动搜索小程序
            throw new Error(`跳转小程序失败: ${JSON.stringify(err)}`)
        }
    })
}

/**
 * 跳转到H5页面
 * @param {string} sn - 设备编号
 * @param {object} options - 配置项
 * @param {string} options.h5BaseUrl - H5页面的根地址
 */
export function redirectToH5(sn, options) {
    const { h5BaseUrl } = options
    if (!h5BaseUrl) {
        throw new Error('缺少H5根地址 (h5BaseUrl)')
    }
    // 假设H5页面路由为 /h5
    const url = `${h5BaseUrl}/#/h5?sn=${sn}`
    window.location.replace(url)
}

/**
 * 智能跳转函数
 * @param {string} sn - 设备编号
 * @param {object} options - 配置项
 * @param {string} options.miniProgramAppId - 小程序AppId
 * @param {string} options.h5BaseUrl - H5页面的根地址
 * @returns {Promise<string>} 返回跳转的目标 'miniProgram' 或 'h5'
 */
export async function smartRedirect(sn, options) {
    if (!sn) {
        throw new Error('缺少设备编号 (sn) 参数')
    }

    if (isWechat()) {
        try {
            // 在微信环境中，初始化JS-SDK
            await initWechatSdk(options.miniProgramAppId, [
                'miniProgram',
                'getEnv',
            ])
            
            // 检查是否在小程序内
            const inMiniProgram = await isMiniProgram()
            if (inMiniProgram) {
                alert('123')
                // 如果已在小程序中，可能不需要跳转，或执行其他逻辑
                // 这里我们依然尝试跳转，小程序会处理内部页面跳转
                redirectToMiniProgram(sn, options)
                return 'miniProgram'
            } else {
                // 在微信浏览器中，跳转到小程序
                redirectToMiniProgram(sn, options)
                return 'miniProgram'
            }
        } catch (error) {
            alert(`微信环境跳转失败，降级到H5:${error}`)
            // 如果JSSDK初始化或跳转失败，降级到H5
            redirectToH5(sn, options)
            return 'h5'
        }
    } else {
        // 其他环境（如普通浏览器），直接跳转到H5页面
        redirectToH5(sn, options)
        return 'h5'
    }
}

// 默认导出
export default {
    isWechat,
    isMobile,
    isMiniProgram,
    redirectToMiniProgram,
    redirectToH5,
    smartRedirect,
    initWechatSdk,
}
