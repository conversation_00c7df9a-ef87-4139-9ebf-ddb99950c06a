export const email = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

export const mobile = /^1[1-9][0-9]{9}$/;

export const num = /^[0-9]*$/;

export const int = /^-?[1-9]\d*$/;

export const date = /^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)$/;

export const money = /^(-)?(([1-9]{1}\d*)|([0]{1}))(\.(\d){1,2})?$/;

export const tel = /^\d{3}-\d{7,8}|\d{4}-\d{7,8}$/;

export const password = /^[0-9a-zA-Z_]{1,}$/; //字母、数字、下划线

// 新的密码规则：6-20位，支持大小写字母、数字、特殊字符!@#$%
export const newPassword = /^[A-Za-z0-9!@#$%]{6,20}$/;
