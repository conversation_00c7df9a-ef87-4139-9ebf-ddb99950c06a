import { echartsColorVars, getThemeColor } from '@/common/util'

function getMaxPrice(t) {
  if (t.length > 0) {
    var e,
      n = t[0].price,
      r = (function (t, e) {
        var n =
          ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
          t["@@iterator"];
        if (!n) {
          if (
            Array.isArray(t) ||
            (n = (function (t, e) {
              if (t) {

                var n = Object.prototype.toString.call(t).slice(8, -1);
                return (
                  "Object" === n && t.constructor && (n = t.constructor.name),
                  "Map" === n || "Set" === n
                    ? Array.from(t)
                    : "Arguments" === n ||
                      /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
                      ? void 0
                      : void 0
                );
              }
            })(t)) ||
            (e && t && "number" == typeof t.length)
          ) {
            n && (t = n);
            var r = 0,
              i = function () { };
            return {
              s: i,
              n: function () {
                return r >= t.length
                  ? {
                    done: !0,
                  }
                  : {
                    done: !1,
                    value: t[r++],
                  };
              },
              e: function (t) {
                throw t;
              },
              f: i,
            };
          }
          throw new TypeError(
            "Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."
          );
        }
        var o,
          a = !0,
          c = !1;
        return {
          s: function () {
            n = n.call(t);
          },
          n: function () {
            var t = n.next();
            return (a = t.done), t;
          },
          e: function (t) {
            (c = !0), (o = t);
          },
          f: function () {
            try {
              a || null == n.return || n.return();
            } finally {
              // if (c) throw o;
            }
          },
        };
      })(t);
    try {
      for (r.s(); !(e = r.n()).done;) {
        var i = e.value;
        i.price > n && (n = i.price);
      }
    } catch (t) {
      r.e(t);
    } finally {
      r.f();
    }
    return n;
  }
  return 1;
}
function formatData(t, e) {
  var n = ["尖", "峰", "平", "谷", "深谷"],
    r = {
      充电: 2,
      放电: 1,
      静置: 0,
    },
    i = [
      "sharpPrice",
      "peakPrice",
      "normalPrice",
      "valleyPrice",
      "lowestValleyPrice",
    ],
    prices = [],
    areas = [];
  t.workProcessList.map(function (t) {
    var c = i[t.segmentType - 1];
    prices.push({
      startTime: t.startHour,
      endTime: t.endHour,
      priceAmount: t.value,
      type: n[t.segmentType - 1],
      price: e[c],
      segmentType: t.segmentType,
    });
    areas.push({
      type: t.batteryWorkStatus,
      typeDescription: r[t.batteryWorkStatus],
      startTime: t.startHour,
      endTime: t.endHour,
    });
  });
  return {
    prices,
    areas,
  };
}
function drawDashLine(t, x1, y1, x2, y2) {
  var dashLength =
    arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : 3,
    dx = x2 - x1,
    dy = y2 - y1,
    //虚线数量
    numDashes = Math.floor(Math.sqrt(dx * dx + dy * dy) / dashLength),
    u = dx / numDashes,
    f = dy / numDashes;
  t.beginPath();
  for (var i = 0; i < numDashes; i++) {
    i % 2 == 0 ? t.moveTo(x1, y1) : t.lineTo(x1, y1);
    x1 += u;
    y1 += f;
  }
  t.lineTo(x2, y2);
}
import { getState, getSegmentTypeColor } from '@/common/util.js'
class DrawCanvas {
  constructor(context) {
    let { devicePixelRatio } = { devicePixelRatio: window.devicePixelRatio, }
    this.dpr = devicePixelRatio;
    this.context = context;
    this.timeSegment = 24;
    this.padding = 20;
    this.prices = [];
    this.areas = [];
    this.maxPrice = getMaxPrice(this.prices);
    // this.setup();
    this.width = 440;
    this.height = 240;
  }

  get contentBox() {
    return {
      x: this.padding,
      y: this.padding,
      width: (this.width),
      height: (this.height),
    };
  }

  paint() {
    this.drawXAxis();
    this.drawYAxis();
    this.drawArea();
    this.drawPriceLine();
    this.drawPriceArea();
    this.context.stroke();
  }
  drawXAxis() {
    var t = this.context,
      //x轴间隔
      gap = (this.width - 2 * this.padding) / this.timeSegment;
    t.save();
    t.strokeStyle = getThemeColor(echartsColorVars.title);
    //移动坐标系原点到左下角
    t.translate(0, this.height - this.padding - 15);
    // t.setLineDash([10,3]);
    t.beginPath();
    //将绘图游标移动到指定的坐标位置，而不进行绘制。
    t.moveTo((this.maxPrice >= 10 ? 6 : 5) * 5.5, 15);
    //线的终点位置（这里的5，因为原点已经移到左下角了）
    t.lineTo((this.width - ((this.maxPrice >= 10 ? 6 : 5) * 5.5) + 20).toFixed(), 15);
    t.stroke();
    //以上是从左到右画X轴
    for (var n = 0; n <= this.timeSegment; n++) {
      //开始画x轴上的刻度小竖线
      var startX = gap * n + (this.maxPrice >= 10 ? 6 : 5) * 5.5, //起始x轴位置
        startY = 0, //起始y轴位置
        endY = startY + 10; //结束y轴位置，刻度高度为5
      //如果X轴刻度到12或者24时，刻度高度稍微高一点（y轴往上就是负数）
      0 !== n && n % 12 == 0 && (startY -= 2);
      t.beginPath();
      t.moveTo(startX, startY + 15);
      t.lineTo(startX, endY);
      t.stroke();
      //开始画刻度下方的数字
      t.beginPath();
      t.font = "12px Microsoft YaHei";
      t.textAlign = "center";
      // t.setFillStyle("rgba(33, 33, 33, 0.4)");
      t.fillStyle = getThemeColor(echartsColorVars.title);
      t.fillText(n, startX, endY + 18); //+20表示在y轴的下方
      t.closePath();
    }
    t.restore();
  }
  drawYAxis() {
    var t = this.context,
      gap = (this.height - 2 * this.padding) / 10, //把Y轴刻度分成10等分
      priceGap = this.maxPrice / 10; //将最大价格10等分
    t.save();
    t.strokeStyle = getThemeColor(echartsColorVars.title);
    t.beginPath();
    //绘图游标移动到左下角起始位置
    t.moveTo((this.maxPrice >= 10 ? 6 : 5) * 5.5, this.padding);
    //从下往上绘制Y轴竖线
    t.lineTo((this.maxPrice >= 10 ? 6 : 5) * 5.5, this.height - this.padding);
    t.stroke();
    for (var i = 0; i < 10; i++) {
      var startX = (this.maxPrice >= 10 ? 6 : 5) * 5.5,
        startY = i * gap + this.padding,
        endX = startX + 4, //刻度宽度为5
        //计算Y轴的刻度值（将最大价格10等分）
        text = (this.maxPrice - priceGap * i).toFixed(2);
      //每5个刻度，刻度长度大一点
      i % 5 == 0 && (endX += 2);
      t.beginPath();
      t.moveTo(startX, startY);
      t.lineTo(endX, startY);
      t.stroke();
      t.font = "12px Microsoft YaHei";
      t.textAlign = "left";
      // t.setFillStyle("rgba(33, 33, 33, 0.4)");
      t.textBaseline = "middle";
      t.fillStyle = getThemeColor(echartsColorVars.title);
      t.fillText(text, 0, startY);
    }
    t.restore();
  }
  drawPriceLine() {
    var t = this.context;
    for (var i = 0; i < this.prices.length; i++) {
      // t.strokeStyle = '#00AEEF';
      var item = this.prices[i],
        text = item.price.toFixed(2) + " (" + item.type + ")",
        startTimePx = this.timeToPixel(item.startTime),
        endTimePx = this.timeToPixel(item.endTime),
        textX = startTimePx + (endTimePx - startTimePx) / 2,
        pricePx = this.priceToPixel(item.price),
        textY = pricePx - 4;
      if (this._last_x && this._last_y) {
        //绘制和前面的价格相连的虚线
        // t.setLineDash([10,3]);
        // t.setStrokeStyle("#999");
        // drawDashLine(t, this._last_x, this._last_y, startTimePx, pricePx);
        t.stroke();
      }
      // t.setStrokeStyle("#00AEEF");
      // t.setLineDash([10,3]);
      t.beginPath();
      t.strokeStyle = getSegmentTypeColor(item.segmentType).color;
      t.moveTo(startTimePx, pricePx);
      t.lineTo(endTimePx, pricePx);

      t.stroke();
      t.font = "12px Microsoft YaHei";
      t.textAlign = "center";
      t.textBaseline = "middle";
      //处理特殊情况：如果跟前面的价格一样的话只显示一条即可
      t.fillStyle = getThemeColor(echartsColorVars.title);
      // t.fillStyle = getSegmentTypeColor(item.segmentType).color;
      let last = i - 1 >= 0 ? this.prices[i - 1] : null;
      if (!(last && last.type == item.type && last.price == item.price)) {
        t.fillText(text, textX, textY - 4);
      }
      this._last_x = endTimePx;
      this._last_y = pricePx;
      t.save();
    }
    t.restore();
  }

  drawPriceArea() {
    var t = this.context;
    t.translate(0, this.height - this.padding);
    t.save();
    for (var i = 0; i < this.prices.length; i++) {
      var item = this.prices[i],
        text = item.price.toFixed(2) + " (" + item.type + ")",
        startTimePx = this.timeToPixel(item.startTime),
        endTimePx = this.timeToPixel(item.endTime),
        textX = startTimePx + (endTimePx - startTimePx) / 2,
        pricePx = this.priceToPixel(item.price),
        textY = pricePx - 4;
      const grd = t.createLinearGradient(0, 0, 0, textY - (this.height - this.padding - 4));
      getSegmentTypeColor
      grd.addColorStop(0, getSegmentTypeColor(item.segmentType).endColor);
      grd.addColorStop(1, getSegmentTypeColor(item.segmentType).startColor);
      // 填充渐变：
      t.fillStyle = grd;
      t.fillRect(startTimePx, 0, endTimePx - startTimePx, textY - (this.height - this.padding - 4));
      t.stroke();
    }
    t.restore();
  }
  drawArea() {
    var t = this.context,
      colors = [
        "rgba(255,0,0,0)",
        "rgba(111, 190, 206, 0.5)",
        "rgba(253, 117, 11, 0.2)",
      ];

    t.save();
    this.areas.forEach((item) => {
      var r = this.areaBox(item);
      // t.setFillStyle(colors[item.type]);
      t.fillStyle = (colors[item.type]);
      t.fillRect(r.x, r.y, r.width, r.height);
    });
    t.restore();
  }

  areaBox(t) {
    var x = this.timeToPixel(t.startTime);
    return {
      x,
      y: this.padding,
      width: this.timeToPixel(t.endTime) - x,
      height: this.height - 2 * this.padding,
    };
  }

  priceToPixel(t) {
    var e = (this.height - 2 * this.padding) / this.maxPrice;
    return this.height - this.padding - t * e;
  }
  timeToPixel(hour) {
    // 原本值是"08:00"
    // var e = t.split(/[:：]/),
    //   n = (60 * e[0] + 1 * e[1]) / 1440;
    //现在值是8
    var n = (60 * hour) / 1440; //1440=60*24一天的分钟数
    return (this.maxPrice >= 10 ? 6 : 5) * 5.5 + (this.width - 2 * this.padding) * n;
  }
  clear() {
    this._last_x = null;
    this._last_y = null;
    this.context.clearRect(0, 0, this.width, this.height);
  }
  update(t) {
    this.prices = t.prices || [];
    this.areas = t.areas || [];
    this.maxPrice = getMaxPrice(t.prices);
    // console.log('[ this.maxPrice ] >', this.maxPrice)
  }
}
export { DrawCanvas, formatData };
