import { useStore } from "vuex";
import { useRouter } from "vue-router";
import { getCurrentInstance, reactive, computed } from "vue";
import api from "@/api";

export default function uploadLogic({
  sence,
  fileLimit,
  fileSize,
  fileTypes,
  emit,
  needWaterRemark=false,
}) {
  const store = useStore();
  const router = useRouter();
  const { proxy } = getCurrentInstance();
  const state = reactive({
    uploadLoading: false,
    fileList: [],
    files: [],
    previewImage: "",
    previewVisible: false,
  });
  const handleUploadChange = (info) => {
    if (info.file.status === "uploading") {
      state.uploadLoading = true;
      return;
    }
    if (info.file.status === "done") {
      if (fileLimit.value == 1) {
        //只能传一个时替换第一个文件
        state.files[0] = info.file.response.data;
      } else {
        state.files.push(info.file.response.data);
      }
      emit("add", state.files);
      emitValue();
      state.uploadLoading = false;
    }
    if (info.file.status === "error") {
      proxy.$message.error("对不起，您的登录已失效...请重新登录!");
      if (info.file.response.code == 401) {
        store.commit("user/setLogout");
        router.push("/login");
      } else {
        proxy.$message.error("上传失败");
        state.uploadLoading = false;
      }
    }
  };
  const beforeUpload = (file) => {
    if (file.name.length > 50) {
      proxy.$message.error("上传文件名不能超过50个字");
      return false;
    }
    const isValidFileType =
      fileTypes.value.indexOf(getFileType(file.name)) >= 0;
    if (!isValidFileType) {
      proxy.$message.error("只能上传" + fileTypes.value.join() + "格式的文件");
      return false;
    }
    const isLt1M = file.size / 1024 / 1024 < fileSize.value;
    if (!isLt1M) {
      proxy.$message.error(`上传文件必须小于${fileSize.value}M!`);
      return false;
    }
    //  如果需要水印
    if(needWaterRemark.value){
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
          const img = new Image();
          img.src = reader.result;
          img.onload = () => {
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext('2d');
            // draw the image onto the canvas
            ctx.drawImage(img, 0, 0);
            // add the watermark text
            ctx.font = 'bold 20px Arial';
            ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
            ctx.textAlign = 'center';
            // 保存当前状态
            ctx.save();
            // 移动原点到文本中心点
            ctx.translate(-canvas.width, -canvas.height);
            // ctx.translate(0,0);
            // 旋转canvas上下文
            // ctx.rotate(Math.PI/-6);
            ctx.transform(1, -0.5, 0.5, 1, 50, 50); // 水平方向倾斜45度

            // 绘制文本
            // 定义水印间距和列数
            var watermarkSpacingX = 450;
            var watermarkSpacingY = 80;
            var watermarkColumnsX = Math.ceil(canvas.width / watermarkSpacingX * 2);   // x轴数量翻倍
            var watermarkColumnsY = Math.ceil(canvas.height / watermarkSpacingY * 4);  // y轴数量翻倍
            // 循环绘制水印
            for (var i = 0; i < watermarkColumnsX; i++) {
              for (var j = 0; j < watermarkColumnsY; j++) {
                // 计算水印位置
                var x = (i ) * watermarkSpacingX;
                var y = (j ) * watermarkSpacingY;
                // 绘制水印
                ctx.fillText("此件复印无效，仅为本网站公示使用", x, y);
              }
            } 
            // 恢复之前的状态
            ctx.restore();
            // ctx.fillText('MingWork', canvas.width / 2, canvas.height / 2);
            // convert the canvas to a blob
            canvas.toBlob((blob) => {
              resolve(new File([blob], file.name, { type: file.type }));
            }, 'image/jpeg');
          };
        };
      });
    }
  };
  const fileUploadUrl = computed(() => {
    return api.fileUpload.replace("{sence}", sence.value);
  });
  const removeFile = (index) => {
    emit("remove", state.files[index]);
    state.files.splice(index, 1);
    emitValue();
  };
  const getFileType = (name) => {
    if (name) {
      const index = name.lastIndexOf(".");
      const fileType = (
        name.substring(index + 1, name.length + 1) || ""
      ).toLowerCase();
      return fileType;
    } else {
      return "";
    }
  };
  const preview = ({ fileName, thumbnailFileVisitUrl, fileVisitUrl }) => {
    if (fileName.includes(".pdf")) {
      window.open(fileVisitUrl, "_blank");
    } else {
      state.previewImage = thumbnailFileVisitUrl;
      state.previewVisible = true;
    }
  };
  const download = async (url, fileName) => {
    fetch(url)
      .then((res) => res.blob())
      .then((blob) => {
        var a = document.createElement("a");
        var url = window.URL.createObjectURL(blob);
        a.href = url;
        a.download = fileName;
        a.click();
        window.URL.revokeObjectURL(url);
      });
  };
  const up = (index) => {
    [state.files[index], state.files[index - 1]] = [
      state.files[index - 1],
      state.files[index],
    ];
    emitValue();
  };
  const down = (index) => {
    [state.files[index], state.files[index + 1]] = [
      state.files[index + 1],
      state.files[index],
    ];
    emitValue();
  };
  const emitValue = () => {
    emit("update:value", state.files);
    emit("done", state.files);
  };
  return {
    state,
    handleUploadChange,
    beforeUpload,
    fileUploadUrl,
    removeFile,
    preview,
    download,
    up,
    down,
    emitValue,
  };
}
