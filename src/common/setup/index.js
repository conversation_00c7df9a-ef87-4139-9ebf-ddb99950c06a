import {
  ref,
  onBeforeUnmount,
  onMounted,
  reactive,
  getCurrentInstance,
  computed,
} from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import Clipboard from "clipboard";

//发送验证码按钮禁用
export function useDisableTime() {
  const disableTime = ref(0);
  let disableTimeInterval = null;
  const setDisableTime = () => {
    if (disableTimeInterval) {
      clearInterval(disableTimeInterval);
    }
    disableTime.value = 60;
    disableTimeInterval = setInterval(() => {
      disableTime.value--;
      if (disableTime.value == 0) {
        clearInterval(disableTimeInterval);
      }
    }, 1000);
  };
  onBeforeUnmount(() => {
    if (disableTimeInterval) {
      clearInterval(disableTimeInterval);
    }
    disableTime.value = 0;
  });
  return {
    disableTime,
    setDisableTime,
  };
}
//分页下拉加载
export function useLoadMore(pageSize, getList) {
  const page = reactive({
    current: 1, //当前
    size: pageSize, //每页条数
    totalPages: 0, //总页数
  });
  const setTotalPage = (total) => {
    page.totalPages = total;
  };
  const btnMoreLoading = ref(false);
  const handleMore = async () => {
    if (!btnMoreLoading.value && page.current < page.totalPages) {
      btnMoreLoading.value = true;
      page.current++;
      await getList();
      btnMoreLoading.value = false;
    }
  };
  onMounted(async () => {
    window.addEventListener("scroll", function () {
      //真实内容的高度
      var pageHeight = Math.max(
        document.body.scrollHeight,
        document.body.offsetHeight
      );
      //视窗的高度
      var viewportHeight =
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight ||
        0;
      //隐藏的高度
      var scrollHeight =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop ||
        0;
      //判断是否到底部
      if (pageHeight - viewportHeight - scrollHeight <= 392) {
        //事件
        handleMore && handleMore();
      }
    });
  });
  return {
    btnMoreLoading,
    setTotalPage,
    page,
  };
}
import { sortType, sortOrder } from "@/common/constant.js";
export function usePagenation(fnGetList) {
  const paginationProps = ref({
    pageSize: 10,
    current: 1,
    total: 0,
    // sortType: "create_time",
    // sortOrder: "desc",
    showSizeChanger: true
  });
  const pageParam = computed(() => {
    let { current, pageSize } = paginationProps.value;
    return { current, size: pageSize };
  });
  const changePage = async (current, pageSize) => {
    paginationProps.value.current = current;
    paginationProps.value.pageSize = pageSize;
    await fnGetList();
  };
  const onTableChange = async (pagination, filters, sorter) => {
    if (sorter?.order) {
      paginationProps.value.sortType = sortType[sorter.columnKey];
      paginationProps.value.sortOrder = sortOrder[sorter.order];
    } else {
      paginationProps.value.sortType = undefined;
      paginationProps.value.sortOrder = undefined;
    }

    await fnGetList();
  };
  const refresh = async () => {
    paginationProps.value.current = 1;
    await fnGetList();
  };
  return {
    paginationProps,
    changePage,
    onTableChange,
    refresh,
    pageParam,
  };
}
export function useCopy(selector) {
  const { proxy } = getCurrentInstance();
  const copy = () => {
    var clipboard = new Clipboard(selector);
    clipboard.on("success", (e) => {
      proxy.$message.success("复制成功");
      // 释放内存
      clipboard.destroy();
    });
    clipboard.on("error", (e) => {
      // 不支持复制
      // 释放内存
      clipboard.destroy();
    });
  };
  return {
    copy,
  };
}
