import { ref, onMounted, reactive } from "vue";
import BScroll from "@better-scroll/core";
import Pullup from "@better-scroll/pull-up";
import MouseWheel from "@better-scroll/mouse-wheel";

BScroll.use(Pullup);
BScroll.use(MouseWheel);
//分页上拉加载
export function usePullUpLoad(selector, pageSize, getList) {
  let scroll;
  const page = reactive({
    current: 1, //当前
    size: pageSize, //每页条数
    totalPages: 0, //总页数
  });
  const setTotalPage = (total) => {
    page.totalPages = total;
  };
  const btnMoreLoading = ref(false);
  const handleMore = async () => {
    if (!btnMoreLoading.value && page.current < page.totalPages) {
      btnMoreLoading.value = true;
      page.current++;
      await getList();
      btnMoreLoading.value = false;
    }
  };
  const initBscroll = () => {
    // 初始化滚动
    scroll = new BScroll(selector, {
      probeType: 3,
      scrollY: true,
      // 上拉距离超过50px时触发 pullingUp 事件
      pullUpLoad: {
        threshold: -10,
      },
      mouseWheel: {
        speed: 20,
        invert: false,
        easeTime: 300,
        discreteTime: 400,
        throttleTime: 0,
        dampingFactor: 0.1,
      },
    });

    scroll.on("pullingUp", async () => {
      await handleMore();
      //   scroll.finishPullUp();

      //   this.isPullUpLoad = true

      //   await this.requestData()

      scroll.finishPullUp();
      scroll.refresh();
      //   this.isPullUpLoad = false
    });
  };
  return {
    btnMoreLoading,
    setTotalPage,
    page,
    initBscroll,
  };
}
