import { mobile, email, password, money } from "@/common/reg";
let checkPhone = async (rule, value) => {
  if (!value) {
    return false;
  }
  if (!mobile.test(value)) {
    return Promise.reject("请填写正确的手机号码");
  }
};
let checkEmail = async (rule, value) => {
  if (!value) {
    return false;
  }
  if (!email.test(value)) {
    return Promise.reject("请填写正确的邮箱");
  }
};
let checkPhoneOrEmail = async (rule, value) => {
  if (!value) {
    return false;
  }
  if (!mobile.test(value) && !email.test(value)) {
    return Promise.reject("请填写正确的手机号码或邮箱");
  }
};
let checkPassword = async (rule, value) => {
  if (!value) {
    return Promise.reject("请填写密码");
  } else if (value.length < 8) {
    return Promise.reject("密码至少为8位");
  } else if (!password.test(value)) {
    return Promise.reject("密码只能为数字、字母、下划线");
  }
};
let checkMoney = async (rule, value) => {
  if (!value) {
    return Promise.reject("请填写预算金额");
  } else if (!money.test(value)) {
    return Promise.reject("请填写正确的金额");
  }
};

export { checkPhone, checkEmail, checkPhoneOrEmail, checkPassword, checkMoney };
