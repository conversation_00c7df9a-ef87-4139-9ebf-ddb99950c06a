import { $http } from "./base.js";

const urlReplace = function (url, pathParam) {
  let newUrl = url;
  let urls = url.match(/\{[^\}]+\}/g);
  if (pathParam) {
    for (var i in pathParam) {
      for (var j = 0; j < urls.length; j++) {
        if (urls[j].slice(1, -1) === i) {
          newUrl =
            newUrl.split(urls[j])[0] + pathParam[i] + newUrl.split(urls[j])[1];
        }
      }
    }
  }
  return newUrl;
};

function fetch({ method, url, pathParam, urlParam, bodyParam, config = null }) {
  let fullUrl = urlReplace(url, pathParam);
  let p = new Promise((resolve, reject) => {
    $http({
      method: method,
      url: fullUrl,
      params: urlParam,
      data: bodyParam,
      ...config,
    })
      .then((result) => {
        resolve(result);
      })
      .catch((error) => {
        reject(error);
      });
  });
  return p;
}

function Get({ url, pathParam = null, urlParam = null, config = null }) {
  return fetch({
    method: "get",
    url: url,
    pathParam: pathParam,
    urlParam: urlParam,
    config: config,
  });
}

function Post({
  url,
  pathParam = null,
  urlParam = null,
  bodyParam = null,
  config = null,
}) {
  return fetch({
    method: "post",
    url,
    pathParam,
    urlParam,
    bodyParam,
    config,
  });
}

function Patch({
  url,
  pathParam = null,
  urlParam = null,
  bodyParam = null,
  config = null,
}) {
  return fetch({
    method: "patch",
    url: url,
    pathParam: pathParam,
    urlParam: urlParam,
    bodyParam: bodyParam,
    config: config,
  });
}

function Put({
  url,
  pathParam = null,
  urlParam = null,
  bodyParam = null,
  config = null,
}) {
  return fetch({
    method: "put",
    url: url,
    pathParam: pathParam,
    urlParam: urlParam,
    bodyParam: bodyParam,
    config: config,
  });
}

function Delete({ url, pathParam = null, urlParam = null, config = null }) {
  return fetch({
    method: "delete",
    url: url,
    pathParam: pathParam,
    urlParam: urlParam,
    config: config,
  });
}

export { Post, Delete, Put, Patch, Get };
