import { createVNode } from "vue";
import { $http, RequestUse, ResponseUse } from "./base";
import { responseIntercept, createResponseInterceptError } from "./errorHandle";
import { Post, Delete, Put, Patch, Get } from "./methods";
import { message } from "ant-design-vue";
import { errorMsg, newErrorMsg, langs, getLang } from "@/common/constant";
import store from "@/store";
import router from "@/router";
import { enterpriseServiceDomain } from "@/config/env";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
// import { cosh } from "core-js/core/number";
import i18n from '@/lang.js'

const { t, locale } = i18n.global

let errorHandler = async (status, errorMessage) => {
  if (status == 403) {
    message.error(errorMessage || '鉴权失败');
  } else if (status == 401) {
    await store.commit("user/loginOut");
    router.push("/login");
  } else {
    if (newErrorMsg[status] || errorMsg[status]) {
      errorMessage = newErrorMsg[status] ? t(newErrorMsg[status]) : errorMsg[status] ? t(errorMsg[status]) : t('code_error');
      message.error(errorMessage);
    } else {
      message.error(errorMessage);
    }
  }
};
RequestUse((config) => {
  if (config.needToken != false) {
    // const token = store.getters["user/getToken"];
    const token = store.getters["user/getNewToken"];
    const lang = store.getters["lang/currentLanguage"];
    const langValue = getLang(lang)
    config.headers['accept-language'] = langValue
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
  }
  return config;
});
ResponseUse(
  responseIntercept(errorHandler),
  createResponseInterceptError(errorHandler)
);

export { Post, Delete, Put, Patch, Get, $http, RequestUse, ResponseUse };
