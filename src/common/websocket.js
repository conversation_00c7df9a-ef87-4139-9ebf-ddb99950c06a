import api from "@/api";
import { uuidv4 } from "@/common/util";
import store from "@/store";

let websock,
  lockReconnect = false,
  timeoutnum = null,
  timeoutObj = null,
  serverTimeoutObj = null,
  timeout = 15000;
const initWebSocket = () => {
  let { staffId } = store.state.user.userInfo;
  if (!staffId) return;
  //初始化weosocket
  const wsuri = `${api.websocket}/${staffId}/${uuidv4()}`;
  websock = new WebSocket(wsuri);
  websock.onopen = websocketonopen;
  websock.onmessage = websocketonmessage;
  websock.onerror = websocketonerror;
  websock.onclose = websocketclose;
};
const reconnect = () => {
  //重新连接
  if (lockReconnect) {
    // 是否真正建立连接
    return;
  }
  lockReconnect = true;
  //没连接上会一直重连，设置延迟避免请求过多
  timeoutnum && clearTimeout(timeoutnum);
  // 如果到了这里断开重连的倒计时还有值的话就清除掉
  timeoutnum = setTimeout(function () {
    //然后新连接
    initWebSocket();
    lockReconnect = false;
  }, 5000);
};
const reset = () => {
  //重置心跳
  //清除时间（清除内外两个心跳计时）
  clearTimeout(timeoutObj);
  clearTimeout(serverTimeoutObj);
  //重启心跳
  start();
};
const start = () => {
  //开启心跳
  timeoutObj && clearTimeout(timeoutObj);
  // 如果外层心跳倒计时存在的话，清除掉
  serverTimeoutObj && clearTimeout(serverTimeoutObj);
  // 如果内层心跳检测倒计时存在的话，清除掉
  timeoutObj = setTimeout(function () {
    // 重新赋值重新发送 进行心跳检测
    //这里发送一个心跳，后端收到后，返回一个心跳消息，
    if (websock.readyState == 1) {
      //如果连接正常
      websocketsend("heartbeat");
    } else {
      //否则重连
      reconnect();
    }
    serverTimeoutObj = setTimeout(function () {
      // 在三秒一次的心跳检测中如果某个值3秒没响应就关掉这次连接
      //超时关闭
      websock.close();
    }, 5000);
  }, timeout);
  // 30s一次
};

const websocketonopen = (e) => {
  //连接建立之后执行send方法发送数据
  // let actions = { test: "12345" };
  // this.websocketsend(JSON.stringify(actions));
};
const websocketonerror = () => {
  //连接建立失败重连
  initWebSocket();
};
const websocketonmessage = (e) => {
  //数据接收
  try {
    const data = JSON.parse(e.data);
    if (data.data) {
      store.commit("user/setMenuUnreadCount", data.data);
      store.commit("user/setNoticeCount", data.data.ES_NOTICE);
      store.commit("user/setReminderCount", data.data.UNREAD_DETAIL_COUNT);
    }
  } catch (error) {
    // console.log(error);
  }
  reset();
};
const websocketsend = (Data) => {
  //数据发送
  websock.send(Data);
};
const websocketclose = (e) => {
  //关闭
  console.log("断开连接", e);
};
export default initWebSocket;
