
// import i18n from '@/lang.js'
import i18n from '@/lang.js'

const { t, locale } = i18n.global
const errorMsg = {
  "10040": 'code_10040',
  "10013": "code_10013",
  // "10005": "图形验证码错误",
  // "10042": "账号或密码错误，请校验后重新输入",
  // "10107": "手机号已经被注册",
  // "10108": "邮箱已经被注册",
  // "10110": "用户不存在",
  // "10044": "邮箱验证码错误",
  // "100206": "企业名称已存在",
  // "10700": "清单中包含解除协助",
  // "10118": "当前企业已切换，请刷新后重试",
};

//新的状态码
const newErrorMsg = {
  '10011': 'code_10011',
  '10012': 'code_10012',
  '10041': 'code_10041'
}

const sortOrder = {
  ascend: "asc",
  descend: "desc",
};
const sortType = {
  createTime: "create_time",
  publishTime: "publish_time",
  terminateTime: "terminate_time",
  licenseeTime: "licensee_time",
  expireTime: "expire_time",
  appointmentTime: "appointment_time",
  inchargeTotal: "incharge_total",
  inchargeToday: "incharge_today",
  dischargeTotal: "discharge_total",
  dischargeToday: "discharge_today",
  moneyTotal: "money_total",
  moneyToday: "money_today",
  malfunctionTotal: "malfunction_total",
  malfunctionToday: "malfunction_today",
};


export const word = [
  "A",
  "B",
  "C",
  "D",
  "E",
  "F",
  "G",
  "H",
  "I",
  "J",
  "K",
  "L",
  "M",
  "N",
  "O",
  "P",
  "Q",
  "R",
  "S",
  "T",
  "U",
  "V",
  "W",
  "X",
  "Y",
  "Z",
];

export function showPrice(t) {
  return "number" == typeof t && t ? t.toFixed(2) : "--";
}
export function getPricePercentage(t, prices) {
  let v = 100,
    y = Math.max.apply(Math, (Object.values(prices)));
  return t ? (t / y) * v + "px" : 0;
}
export function formatData(t, e) {
  var n = ["尖", "峰", "平", "谷", "深谷"],
    r = {
      充电: 1,
      放电: 2,
      静置: 0,
    },
    i = [
      "highestPeakPrice",
      "peakPrice",
      "normalPrice",
      "valleyPrice",
      "lowestValleyPrice",
    ],
    prices = [],
    areas = [];
  t.workProcessList.map(function (t) {
    var c = i[t.segmentType - 1];
    prices.push({
      startTime: t.startHour,
      endTime: t.endHour,
      priceAmount: t.value,
      type: n[t.segmentType - 1],
      price: e[c],
      segmentType: t.segmentType,
    });
    areas.push({
      type: t.batteryWorkStatus,
      typeDescription: r[t.batteryWorkStatus],
      startTime: t.startHour,
      endTime: t.endHour,
    });
  });




  return {
    prices,
    areas,
  };
}

function getMaxPrice(t) {
  if (t.length > 0) {
    var e,
      n = t[0].price,
      r = (function (t, e) {
        var n =
          ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
          t["@@iterator"];
        if (!n) {
          if (
            Array.isArray(t) ||
            (n = (function (t, e) {
              if (t) {
                if ("string" == typeof t) return (t, e);
                var n = Object.prototype.toString.call(t).slice(8, -1);
                return (
                  "Object" === n && t.constructor && (n = t.constructor.name),
                  "Map" === n || "Set" === n
                    ? Array.from(t)
                    : "Arguments" === n ||
                      /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
                      ? (t, e)
                      : void 0
                );
              }
            })(t)) ||
            (e && t && "number" == typeof t.length)
          ) {
            n && (t = n);
            var r = 0,
              i = function () { };
            return {
              s: i,
              n: function () {
                return r >= t.length
                  ? {
                    done: !0,
                  }
                  : {
                    done: !1,
                    value: t[r++],
                  };
              },
              e: function (t) {
                throw t;
              },
              f: i,
            };
          }
          throw new TypeError(
            "Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."
          );
        }
        var o,
          a = !0,
          c = !1;
        return {
          s: function () {
            n = n.call(t);
          },
          n: function () {
            var t = n.next();
            return (a = t.done), t;
          },
          e: function (t) {
            (c = !0), (o = t);
          },
          f: function () {
            try {
              a || null == n.return || n.return();
            } finally {
              // if (c) throw o;
            }
          },
        };
      })(t);
    try {
      for (r.s(); !(e = r.n()).done;) {
        var i = e.value;
        i.price > n && (n = i.price);
      }
    } catch (t) {
      r.e(t);
    } finally {
      r.f();
    }
    return n;
  }
  return 1;
}
function drawDashLine(t, x1, y1, x2, y2) {
  var dashLength =
    arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : 3,
    dx = x2 - x1,
    dy = y2 - y1,
    //虚线数量
    numDashes = Math.floor(Math.sqrt(dx * dx + dy * dy) / dashLength),
    u = dx / numDashes,
    f = dy / numDashes;
  t.beginPath();
  for (var i = 0; i < numDashes; i++) {
    i % 2 == 0 ? t.moveTo(x1, y1) : t.lineTo(x1, y1);
    x1 += u;
    y1 += f;
  }
  t.lineTo(x2, y2);
}
export class DrawCanvas {
  constructor(context) {
    // var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : [];
    // debugger;
    // s(this, t);
    let { devicePixelRatio, windowWidth, screenWidth } = { devicePixelRatio: window.devicePixelRatio, windowWidth: 390, screenWidth: 190 }
    const screenRatio = screenWidth / 375;
    this.dpr = devicePixelRatio;
    // this.canvas = e;
    this.context = context;
    this.timeSegment = 24;
    this.padding = 12 * this.dpr;
    this.tickStyle = "#000";
    this.axisStyle = "#999";
    this.prices = [];
    this.areas = [];
    this.maxPrice = getMaxPrice(this.prices);
    this.showAxisPointer = !0;
    this.mouseX = 0;
    this.mouseY = 0;
    // this.setup();
    // this.width = windowWidth;
    this.width = windowWidth - 36 / screenRatio;
    this.height = 150;

  }
  // get width() {
  //   return this.canvas.width;
  // }
  // set width(value) {
  //   this.canvas.width = newValue;
  // }
  // get height() {
  //   return this.canvas.height;
  // }
  // set height(newValue) {
  //   this.canvas.height = newValue;
  // }
  get contentBox() {
    return {
      x: this.padding,
      y: this.padding,
      width: this.width - 2 * this.padding,
      height: this.height - 2 * this.padding,
    };
  }
  // setup() {
  //   var t = this;
  //   this.canvas.addEventListener("mousemove", function (e) {
  //     var n = e.offsetX,
  //       r = e.offsetY;
  //     t.mouseX = n;
  //     t.mouseY = r;
  //     t.clear();
  //     t.paint();
  //     t.showAxisPointer && P(t.contentBox, n, r) && t.drawAxisPointer(n, r);
  //   });
  // }
  paint() {
    this.drawXAxis();
    this.drawYAxis();
    this.drawArea();
    this.drawPriceLine();
    // this.context.draw();
  }
  drawXAxis() {
    var t = this.context,
      //x轴间隔
      gap = (this.width - 2 * this.padding) / this.timeSegment;
    t.save();
    //移动坐标系原点到左下角
    t.translate(0, this.height - this.padding - 5);
    // t.setLineWidth(1);
    t.setLineWidth = 1
    t.beginPath();
    t.strokeStyle = this.axisStyle
    // t.setStrokeStyle(this.axisStyle);
    //将绘图游标移动到指定的坐标位置，而不进行绘制。
    t.moveTo(this.padding, 5);
    //线的终点位置（这里的5，因为原点已经移到左下角了）
    t.lineTo(this.width - this.padding, 5);
    t.stroke();
    t.fillStyle = this.tickStyle
    // t.setFillStyle(this.tickStyle);
    //以上是从左到右画X轴
    for (var n = 0; n <= this.timeSegment; n++) {
      //开始画x轴上的刻度小竖线
      var startX = gap * n + this.padding, //起始x轴位置
        startY = 0, //起始y轴位置
        endY = startY + 5; //结束y轴位置，刻度高度为5
      //如果X轴刻度到12或者24时，刻度高度稍微高一点（y轴往上就是负数）
      0 !== n && n % 12 == 0 && (startY -= 2 * this.dpr);
      t.beginPath();
      t.moveTo(startX, startY);
      t.lineTo(startX, endY);
      t.stroke();
      //开始画刻度下方的数字
      t.beginPath();
      // t.font = "10rpx Microsoft YaHei";
      t.font = "10rpx"
      // t.setTextAlign("center");
      t.textAlign = 'center'
      t.fillStyle = 'rgba(33, 33, 33, 0.4)'
      // t.setFillStyle("rgba(33, 33, 33, 0.4)");
      t.fillText(n, startX, endY + 20); //+20表示在y轴的下方
      t.closePath();
    }
    t.restore();
  }
  drawYAxis() {
    var t = this.context,
      gap = (this.height - 2 * this.padding) / 10, //把Y轴刻度分成10等分
      priceGap = this.maxPrice / 10; //将最大价格10等分
    t.save();
    t.beginPath();
    t.strokeStyle = this.axisStyle
    // t.setStrokeStyle(this.axisStyle);
    //绘图游标移动到左下角起始位置
    t.moveTo(this.padding, this.padding);
    //从下往上绘制Y轴竖线
    t.lineTo(this.padding, this.height - this.padding);
    t.stroke();
    t.fillStyle = this.tickStyle
    // t.setFillStyle(this.tickStyle);
    for (var i = 0; i < 10; i++) {
      var startX = this.padding,
        startY = i * gap + this.padding,
        endX = startX + 5, //刻度宽度为5
        //计算Y轴的刻度值（将最大价格10等分）
        text = (this.maxPrice - priceGap * i).toFixed(2);
      //每5个刻度，刻度长度大一点
      i % 5 == 0 && (endX += 2);
      t.beginPath();
      t.moveTo(startX, startY);
      t.lineTo(endX, startY);
      t.stroke();
      t.font = "10rpx Microsoft YaHei";
      t.font = "10rpx";
      // t.setTextAlign("center");
      t.textAlign = 'center'
      t.fillStyle = 'rgba(33, 33, 33, 0.4)'
      // t.setFillStyle("rgba(33, 33, 33, 0.4)");
      t.textBaseline = "middle";
      t.fillText(text, startX - 14, startY);
    }
    t.restore();
  }
  drawPriceLine() {
    var t = this.context;
    t.save();
    t.fillStyle = this.tickStyle;
    t.strokeStyle = "#222";
    // t.setFillStyle(this.tickStyle);
    // t.setStrokeStyle("#222");
    // debugger;
    for (var i = 0; i < this.prices.length; i++) {
      var item = this.prices[i],
        text = item.price.toFixed(2) + " (" + item.type + ")",
        startTimePx = this.timeToPixel(item.startTime),
        endTimePx = this.timeToPixel(item.endTime),
        textX = startTimePx + (endTimePx - startTimePx) / 2,
        pricePx = this.priceToPixel(item.price),
        textY = pricePx - 4 * this.dpr;
      if (this._last_x && this._last_y) {
        //绘制和前面的价格相连的虚线
        // t.setLineWidth(1);
        t.lineWidth = 1
        // t.setStrokeStyle("#999");
        t.strokeStyle = '#999'
        drawDashLine(t, this._last_x, this._last_y, startTimePx, pricePx);
        t.stroke();
      }
      t.strokeStyle = "#00AEEF"
      t.lineWidth = 1
      // t.setStrokeStyle("#00AEEF");
      // t.setLineWidth(1);
      t.beginPath();
      t.moveTo(startTimePx, pricePx);
      t.lineTo(endTimePx, pricePx);
      t.stroke();
      t.fillStyle = '#00AEEF'
      // t.setFillStyle("#00AEEF");
      // t.font = "12rpx Microsoft YaHei";
      t.font = "12rpx";
      t.textAlign = "center";
      // t.setTextAlign("center");
      t.textBaseline = "middle";
      //处理特殊情况：如果跟前面的价格一样的话只显示一条即可
      let last = i - 1 >= 0 ? this.prices[i - 1] : null;
      if (!(last && last.type == item.type && last.price == item.price)) {
        t.fillText(text, textX, textY);
      }
      this._last_x = endTimePx;
      this._last_y = pricePx;
    }
    t.restore();
  }
  drawArea() {
    var t = this.context,
      colors = [
        "rgba(0,0,0,0)",
        "rgba(111, 190, 206, 0.5)",
        "rgba(253, 117, 11, 0.2)",
      ];
    t.save();
    this.areas.forEach((item) => {
      var r = this.areaBox(item);
      t.fillStyle = colors[item.type]
      // t.setFillStyle(colors[item.type]);
      t.fillRect(r.x, r.y, r.width, r.height);
    });
    t.restore();
  }
  drawAxisPointer(t, e) {
    var n =
      arguments.length > 2 && void 0 !== arguments[2]
        ? arguments[2]
        : "#FF00F0",
      r = this.context;
    r.save();
    r.lineWidth = 1
    // r.setLineWidth(1);
    r.lineWidth = 1
    r.strokeStyle = n;
    // r.setStrokeStyle(n);
    // r.setFillStyle(n);
    r.fillStyle = n;
    r.beginPath();
    r.arc(t, e, 3, 0, 2 * Math.PI, !1);
    r.fill();
    var i = this.padding,
      o = this.width - this.padding;
    drawDashLine(r, i, e, o, e, 5);
    r.stroke();
    var a = this.padding,
      c = this.height - this.padding;
    drawDashLine(r, t, a, t, c, 5);
    r.stroke();
    r.setFillStyle("#00AEEF");
    r.fillRect(t - 140, e - 50, 280, 40);
    r.beginPath();
    r.moveTo(t, e - 3);
    r.lineTo(t - 8, e - 10);
    r.lineTo(t + 8, e - 10);
    r.fill();
    var s = this.priceFromPixel(e),
      l = this.timeFromPixel(t),
      u = this.chargeStatusFromPixel(t, e),
      f = "时间:".concat(l, " 电价:").concat(s, " ").concat(u);
    r.font = "14rpx Microsoft YaHei";
    // r.setTextAlign("center");
    // r.setFillStyle("#fff");
    r.textAlign = "center";
    r.fillStyle = "#fff";
    r.fillText(f, t + 5, e - 25, 260);
    r.restore();
  }
  getPercentageX(t) {
    return (t - this.padding) / this.contentBox.width;
  }
  areaBox(t) {
    var x = this.timeToPixel(t.startTime);
    return {
      x,
      y: this.padding,
      width: this.timeToPixel(t.endTime) - x,
      height: this.height - 2 * this.padding,
    };
  }
  priceFromPixel(t) {
    var e = (this.height - t - this.padding) / this.contentBox.height;
    return (this.maxPrice * e).toFixed(2);
  }
  timeFromPixel(t) {
    var e = 1440 * this.getPercentageX(t),
      n = Math.floor(e / 60),
      r = Math.floor(e % 60),
      i = function (t) {
        return String(t).padStart(2, "0");
      };
    return i(n) + ":" + i(r);
  }
  chargeStatusFromPixel(t, e) {
    var n,
      r = ["充电↓", "放电↑", "静置"],
      i = (function (t, e) {
        var n =
          ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
          t["@@iterator"];
        if (!n) {
          if (
            Array.isArray(t) ||
            (n = (function (t, e) {
              if (t) {
                if ("string" == typeof t) return (t, e);
                var n = Object.prototype.toString.call(t).slice(8, -1);
                return (
                  "Object" === n && t.constructor && (n = t.constructor.name),
                  "Map" === n || "Set" === n
                    ? Array.from(t)
                    : "Arguments" === n ||
                      /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
                      ? (t, e)
                      : void 0
                );
              }
            })(t)) ||
            (e && t && "number" == typeof t.length)
          ) {
            n && (t = n);
            var r = 0,
              i = function () { };
            return {
              s: i,
              n: function () {
                return r >= t.length
                  ? {
                    done: !0,
                  }
                  : {
                    done: !1,
                    value: t[r++],
                  };
              },
              e: function (t) {
                throw t;
              },
              f: i,
            };
          }
          throw new TypeError(
            "Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."
          );
        }
        var o,
          a = !0,
          c = !1;
        return {
          s: function () {
            n = n.call(t);
          },
          n: function () {
            var t = n.next();
            return (a = t.done), t;
          },
          e: function (t) {
            (c = !0), (o = t);
          },
          f: function () {
            try {
              a || null == n.return || n.return();
            } finally {
              // if (c) throw o;
            }
          },
        };
      })(this.areas);
    try {
      for (i.s(); !(n = i.n()).done;) {
        var o = n.value;
        if ((this.areaBox(o), t, e)) return r[o.type] || "";
      }
    } catch (t) {
      i.e(t);
    } finally {
      i.f();
    }
    return "";
  }
  priceToPixel(t) {
    var e = (this.height - 2 * this.padding) / this.maxPrice;
    return this.height - this.padding - t * e;
  }
  timeToPixel(hour) {
    // 原本值是"08:00"
    // var e = t.split(/[:：]/),
    //   n = (60 * e[0] + 1 * e[1]) / 1440;
    //现在值是8
    var n = (60 * hour) / 1440; //1440=60*24一天的分钟数
    return this.padding + (this.width - 2 * this.padding) * n;
  }
  clear() {
    this._last_x = null;
    this._last_y = null;
    this.context.clearRect(0, 0, this.width, this.height);
  }
  update(t) {
    this.prices = t.prices || [];
    this.areas = t.areas || [];
    this.maxPrice = getMaxPrice(t.prices);
  }
}
const langs = [
  {
    label: '中文',
    lang: 'zh',
    value: 'zh-CN'
  },
  {
    label: 'English',
    lang: 'en',
    value: 'en-US'
  }
]
const getLang = (lang) => {
  const res = langs.filter(item => item.lang == lang)
  if (res.length) {
    return res[0].value
  }
  return 'zh-CN'
}
export {
  errorMsg,
  sortOrder,
  sortType,
  newErrorMsg,
  langs,
  getLang,
};
