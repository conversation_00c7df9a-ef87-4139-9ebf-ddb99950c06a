export default {
  /**登录 */
  merchantLoginAndRegister: "skipAuth/merchantLoginAndRegister", //商家登录注册（钉钉用）
  dingTalkUserAuthAcquirePhone: "skipAuth/dingTalkUserAuthAcquirePhone", //钉钉授权获取手机号并登录
  DDFreeAuthenLogin: "skipAuth/DDFreeAuthenLogin", //钉钉免登通过code码获取第三方账户信息
  enterMerchantCenter: "sysSupplier/enterMerchantCenter", //进入商家
  platformLoginAndRegister: "skipAuth/platformLoginAndRegister", //平台登录
  thirdUserBindPlatformUser: "skipAuth/thirdUserBindPlatformUser", //第三方账户绑定平台账户
  queryTycOrgBasicInfo: "sysSupplier/searchTycOrg?keyword={keyword}", // 根据输入内容获取公司名称列表
  sysSupplierCreate: "sysSupplier/create", //用户创建企业
  applyJoinSupplier: "sysSupplier/applyJoinSupplier", //用户申请企业
  queryCurrentOrgStaffList: "supplierStaff/queryCurrentOrgStaffListWithRole", //获取当前用户供应商员工列表
  queryCurrentOrgManager: "supplierStaff/queryCurrentOrgManager", //获取当前用户供应商管理员信息
  queryEmployeeList: "supplierStaff/queryCurrentOrgManagerAndStaffList", // 获取当前用户供应商所有员工列表包括管理员
  getCenterInformation: "sysSupplier/queryOrgConf", // 获取厂商配置信息
  centerInformationUpdate: "sysSupplier/modifyOrgConf", // 厂商配置中心编辑
  searchUserByPhoneForInvite:
    "supplierStaff/searchUserByPhoneForInvite?phone={phone}", //根据手机号搜索用户邀请加入供应商企业
  addStaffAndRole: "supplierStaff/addStaffAndRole", //供应商修改成员权限
  authAndModifyOrgManager: "sysSupplier/authAndModifyOrgManager", //供应商变更管理员
  modifyStafRole: "supplierStaff/modifyStafRoleOrStaffLeave", //供应商添加成员
  getSmsCode: "skipAuth/sendSmsVerificationCode", //获取短信验证码
  supplyerCreate: "sysSupplier/create", //供应商入驻
  getUserInfo: "supplierStaff/queryCurrentStaffInfoWithRole", //获取当前登录员工信息及角色
  queryUserSupplier: "sysSupplier/queryUserSupplier", //获取当前用户所在供应商信息
  supplierModify: "sysSupplier/modify", //编辑供应商企业信息
  staffModify: "supplierStaff/modifyStaffInfo", //编辑员工个人信息
  fileUpload: "file/upload?sence={sence}", //文件上传
  verifySmsCode: "skipAuth/verifySmsCode",
  listRoleWithPermission: "role/listRoleWithPermission", //获取企业下的所有角色及对应权限（厂商侧）
  queryOrgStaffListByRoleCode: "supplierStaff/queryOrgStaffListByRoleCode", //根据角色获取厂商成员列表（厂商侧）
  queryUserSupplierList: "sysSupplier/queryUserSupplierList", //查询用户所在商家列表
  queryStaffSupplierList: "sysSupplier/queryStaffSupplierList", //查询商家成员所在商家列表
  searchAddressAssociate: "skipAuth/searchAddressAssociate", //根据关键词搜索地址联想
  getAddressDivisionCode: "skipAuth/getAddressDivisionCode", //根据地址获取行政区识别码
  changeTenant: "user/changeTenant", //商户成员切换企业

  addSupplierOperator: "supplierStaff/addSupplierOperator", //添加操作人
  getSupplierOperatorList: "supplierStaff/getSupplierAdmin", //获取操作人列表
  leaveStaff: "supplierStaff/leaveStaff", //员工离职
  /*upms标签*/
  upmsLabelRemove: "label/remove/{labelId}",
  upmsLabelList: "label/queryOrgLabelList",
  upmsLabelAdd: "label/createOrgLabel",
  // 客户
  checkByCustomerName: "customer/checkByCustomerName", //检测客户是否存在
  addCustomerAndContacts: "customer/addCustomerAndContacts", //新增客户及联系人
  // customerList: "customer/customerPage",
  getSysUserRelationList: "sysUserRelation/getSysUserRelationList", // 注册客户分页
  customerCreate: "customer/addCustomer",
  queryStaffList: "customer/queryCustomerAllotStaff",
  customerDetail: "customer/customerDetail?customerId={customerId}",
  // contactsList: "customer/contactsPage",  // 旧的联系人分页
  contactCreate: "customer/addContacts",
  receivingNews: "customer/contacts/address/list", //  发货选择收货地址
  searchTycOrg: "sysSupplier/searchTycOrg?keyword={keyword}", // 天眼查数据
  searchTycOrgDetail: "sysSupplier/searchTycOrgDetail?keyword={keyword}", // 天眼查详细数据
  modifyCustomer: "customer/modifyCustomer", // 编辑客户信息
  queryContactsListBySupplier: "customer/queryContactsListBySupplier", //根据客户Id查找联系人列表
  deleteCustomer: "customer/deleteCustomer",
  modifyContacts: "customer/modifyContacts",
  contactsDetail: "customer/contactsDetail",
  deleteContacts: "customer/deleteContacts",
  queryCustomerNameListBySupplier: "customer/queryCustomerNameListBySupplier", //根据条件查找客户名称列表
  queryCustomerAndContactsByUserId: "customer/queryCustomerAndContactsByUserId", //基于用户userId查询对应的厂商crm客户信息
  orderRelationConsumerAndAddContacts:
    "customer/orderRelationConsumerAndAddContacts", //订单关联已有客户

  queryOrgBankAccount: "sysSupplier/queryOrgBankAccount?orgId={orgId}", // 获取企业对公账户信息
  addOrgBankAccount: "sysSupplier/addOrgBankAccount", // 新增企业对公账户信息
  modifyOrgBankAccount: "sysSupplier/modifyOrgBankAccount", // 修改企业对公账户信息

  addOrgServiceCate: "sysSupplier/addOrgServiceCate", //新增微官网推荐商品分类
  getOrgServiceCate: "sysSupplier/getOrgServiceCate", //获取微官网推荐商品分类
  delOrgServiceCate: "sysSupplier/delOrgServiceCate", //删除微官网推荐商品分类
  sortOrgServiceCate: "sysSupplier/sortOrgServiceCate", //微官网推荐商品分类排序
  updateOrgServiceCate: "sysSupplier/updateOrgServiceCate", //更新微官网推荐商品分类
  modifyMicroMall: "sysSupplier/modifyMicroMall", //微官网主题色修改
  /**客户跟进 */
  followLogPage: "customer/followLogPage", //分页拉取客户跟进日志
  addFollowLog: "customer/addFollowLog", //新增客户跟进日志
  addShareFollowLog: "customer/addShareFollowLog", //新增客户分享跟进日志
  modifyFollowLog: "customer/modifyFollowLog", //更新客户跟进日志
  deleteFollowLog: "customer/deleteFollowLog", //删除客户跟进日志
  addedServiceFind: "org/addedService/find",
  getAddedServiceDetail:"org/addedService/getAddedServiceDetail",
  /** 在线商店 */
  onlineShopInfo: "online/shop/info", //获取在线商店设置信息
  onlineShopSet: "online/shop/set", //在线商店设置
  checkSubDomainName: "online/shop/checkSubDomainName", //子域名检查
  /** 分销权限相关*/
  createAndUpdateOrg: "sysSupplier/createAndUpdateOrg",
  selectSupplierPage: "sysSupplier/selectSupplierPage",
  selectFranchiserList: "sysSupplier/selectFranchiserList",
   /**站点树结构信息 */
  //  getCurrentOrgInfoTree:'sysSupplier/getCurrentOrgInfoTree'
};
