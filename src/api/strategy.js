export default {
  getRunMode: 'ems/runMode/get',
  changeRunMode: 'ems/runMode/change',
  changeStationRunMode:'ems/station/runMode/change',
  getStrategyTemplateList: 'strategy/template/list',
  getStrategyTemplatePage:'strategy/template/page',
  getOrgAndSubOrgStationNameList:'station/getOrgAndSubOrgStationNameList',
  getStrategyTemplateDetail: 'strategy/template/detail',
  addStrategyTemplate: 'strategy/template/add',
  updateStrategyTemplate: "strategy/template/update",
  deleteStrategyTemplate: 'strategy/template/delete',
  getStrategyStationList: "strategy/station/list",
  previewStation: "strategy/station/preview",
  addStrategyStation: "strategy/station/add",
  authorizationStation: "strategy/station/authorization",
  updateStation: "strategy/station/update",
  deleteStation: "strategy/station/delete",
  getAiRecommendStrategy: "strategy/getAiRecommendStrategy",
  getAiForecastPower: "strategy/getAiForecastPower",

  getElecPriceTemplateList: "elecPrice/template/list",
  getElecPriceTemplateDetail: "elecPrice/template/detail",
  addElecPriceTemplate: "elecPrice/template/add",
  updateElecPriceTemplate: "elecPrice/template/update",
  deleteElecPriceTemplate: "elecPrice/template/delete",
  getElecPriceStationList: "elecPrice/station/list",
  settingStationElectricPrice: "elecPrice/settingStationElectricPrice",
  uploadVPPElectricPrice: "elecPrice/uploadVPPElectricPrice",

  authorizationStationElectricPrice:'elecPrice/authorizationStationElectricPrice',
  // ems
  getEmsDynamicEnvironmentSystem:'ems/setting/getDynamicEnvironmentSystem',
  setDynamicEnvironmentSystem:'ems/setting/setDynamicEnvironmentSystem'
}