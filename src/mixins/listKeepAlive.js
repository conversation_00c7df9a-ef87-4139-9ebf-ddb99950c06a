import store from "@/store";
export default function(fromNames = [], getListKey, queryKey = "query") {
  return {
    data() {
      return {
        hasOnBeforeMount: false,
      };
    },
    beforeRouteEnter(to, from) {
      // mixin进不了这个钩子！！！！
      if (
        fromNames.includes(from.name) &&
        (from[queryKey].id || from[queryKey].bizId)
      ) {
        //只有从编辑页过来的数据要缓存
        // to.meta.keepAlive = true;
        // store.commit("user/setKeepAliveList", to.name);
      } else if (from.name) {
        // to.meta.keepAlive = false;
        store.commit("user/removeKeepAliveList", to.name);
      }
    },
    beforeRouteLeave() {
      this.hasOnBeforeMount = false;
    },
    async activated() {
      if (!this.hasOnBeforeMount) {
        this[getListKey] && (await this[getListKey]());
      }
    },
    beforeMount() {
      this.hasOnBeforeMount = true;
    },
  };
}
