// src/utils/db.js
import { openDB } from 'idb';

const DB_NAME = 'i18nCache';
const STORE_NAME = 'messages';
const CACHE_DURATION = 30 * 60 * 1000; // 30分钟

// 初始化数据库
async function initDB() {
  return openDB(DB_NAME, 1, {
    upgrade(db) {
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        db.createObjectStore(STORE_NAME, { keyPath: 'locale' });
      }
    },
  });
}

// 获取缓存的语言包
export async function getCachedMessages(locale) {
  console.log('[ locale ] >', locale)
  const db = await initDB();
  const cached = await db.get(STORE_NAME, locale);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.messages; // 返回未过期的缓存
  }
  return null; // 缓存过期或不存在
}

// 缓存语言包
export async function cacheMessages(locale, messages) {
  const db = await initDB();
  await db.put(STORE_NAME, {
    locale,
    messages,
    timestamp: Date.now(),
  });
}

// 清除所有缓存
export async function clearCache() {
  const db = await initDB();
  await db.clear(STORE_NAME);
}
