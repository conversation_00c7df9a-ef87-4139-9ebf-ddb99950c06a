let alioss = "https://ming-enterprise.oss-cn-hangzhou.aliyuncs.com/static";
let setting = {
  mingwork: {
    logoHeader: "ssnj.svg",
    proApiBaseUrl: "https://outgateway.mingwork.com",
    proEnterpriseServiceDomain: "https://www.mingwork.com",
    storeDomain: ".store.mingwork.com",
    miniShop: "miniShop/",
    weChatAppId: "wx41316c49103ed6ac",
    copyRight: "©2022 上海明我相忆网络科技有限公司",
    beian: "沪ICP备13029193号-7",
    businessLicenseNo: "沪B2-20211453",
    recordcode: "31011002005440",
    market: {
      name: "可乐",
      position: "商务经理",
      phone: "***********",
      email: "<EMAIL>",
      qrcode: "home/QRcode-xieqinan.png",
    },
    versionIds: {
      standard: "1610923211488202753",
      professional: "1610926870393491457",
      ultimate: "1610934216672112642",
    },
    merchantAgreementName: "服务条款",
    manageAgreementName: "隐私政策",
    login: {
      background: alioss + "/login/background-mingwork.jpg",
      title1: "精准定位服务",
      title2: "助您快速打造个性化TOB商店",
      p1: "「在线商店」零代码打造独立品牌商店",
      p2: "「仓库系统」云仓库进销存流水一目了然",
      p3: "「私域营销」全方位监控私域营销数据",
      p4: "「订单交付」B2B订单交付流程全面完善",
    },
  },
  ssnj: {
    logoHeader: "ssnj.svg",
    proApiBaseUrl: "https://frsoutgateway.mingwork.com",
    proEnterpriseServiceDomain: "http://frspc.mingwork.com",
    storeDomain: ".store.mingwork.com",
    miniShop: "",
    weChatAppId: "wx8e9fa377f3a90428",
    copyRight: "©2023 上海上善能及能源科技有限公司",
    beian: "沪ICP备13029193号-7",
    businessLicenseNo: "沪B2-20211453",
    recordcode: "31011002005440",
    market: {
      name: "可乐",
      position: "商务经理",
      phone: "***********",
      email: "<EMAIL>",
      qrcode: "home/QRcode-xieqinan.png",
    },
    versionIds: {
      standard: "1610923211488202753",
      professional: "1610926870393491457",
      ultimate: "1610934216672112642",
    },
    appendMenus: {
      ES_OPERATION_MANAGER: {
        label: "运维",
        key: "maintence",
        sort: 0,
        icon: "icon-daohang-4beifen",
        roleCode: "ES_OPERATION_MANAGER",
      },
    },
    merchantAgreementName: "入驻协议",
    manageAgreementName: "管理规范",
    login: {
      background: alioss + "/login/background-ssnj.jpg",
      textColor: "text-ff",
      title1: "上善能及",
      // ScienEnergy
      title2: "量身定制完整的储能解决方案",
      p1: "【削峰填谷】利用峰谷电差价差，大幅降低成本",
      p2: "【动态扩容】储能代替变压器增容，满足高峰时生产经营需求",
      p3: "【备用电源】医院、机房等重要场景下保障电力供应",
      p4: "【光储融合】结合光伏实现新能源方案落地，获取碳积分",
    },
  },
};

export default setting[process.env.VUE_APP_COMPANY || "mingwork"];
