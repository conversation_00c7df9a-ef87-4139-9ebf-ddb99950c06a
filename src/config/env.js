import appConstant from "@/config/appConstant.js";

let enterpriseServiceDomain = "",
  cookieDomain = "",
  pre = "",
  domain = "",
  storeDomain = "";
if (process.env.NODE_ENV == "development") {
  pre = "http://192.168.88.247/";
  enterpriseServiceDomain = pre + "enterprise-service-pc";
  domain = pre + "enterprise-service-supplier";
  storeDomain = ".storesit.mingwork.com";
} else if (process.env.NODE_ENV == "testing") {
  enterpriseServiceDomain = "https://pcsit.mingwork.com/";
  domain = "http://suppliersit.mingwork.com/";
  storeDomain = ".storesit.mingwork.com";
} else if (process.env.NODE_ENV == "production") {
  enterpriseServiceDomain = appConstant.proEnterpriseServiceDomain;
  domain = "https://" + window.location.host;
  storeDomain = appConstant.storeDomain;
  // domain = "https://pro-supplier.mingwork.com";
  // cookieDomain = "https://supplier.mingwork.com";
  // cookieDomain = location.origin;
}
let shopIframeUrl = `${enterpriseServiceDomain}/#/${appConstant.miniShop}`;
export {
  enterpriseServiceDomain,
  cookieDomain,
  domain,
  storeDomain,
  shopIframeUrl,
};
