// i18nService.js
import service from '@/apiService';
import { getCachedMessages, cacheMessages } from '../indexedDBService';

export async function fetchMessages(locale) {
  // 先从缓存中获取
  // const cachedMessages = await getCachedMessages(locale);
  // if (cachedMessages && Object.keys(cachedMessages).length) {
  //   return cachedMessages;
  // }

  // 缓存中没有或已过期，从接口获取
  try {
    // const response = await service.getMessages(locale);
    // const messagesArray = response.data.data;
    // const messages = messagesArray.reduce((acc, item) => {
    //   acc[item.messageCode] = item.messageText || ' ';
    //   return acc;
    // }, {});
    // // 缓存语言包
    // await cacheMessages(locale, messages);
    // return messages;
  } catch (error) {
    console.error('Failed to fetch messages:', error);
    return {};
  }
}
