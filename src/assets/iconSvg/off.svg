<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="主页面" stroke="none" stroke-width="1">
        <g id="首页-车站列表-站点详情" transform="translate(-1433, -661)">
            <g id="编组-54" transform="translate(241, 636)">
                <g id="编组-64" transform="translate(16, 16)">
                    <g id="按钮-/-1备份-6" transform="translate(1104, 3)">
                        <g id="编组" transform="translate(10, 5)">
                            <g id="图标-/-1" transform="translate(64.1875, 3.1875)">
                                <path d="M15.625,7.8125 C15.625,12.1272246 12.1272246,15.625 7.8125,15.625 C3.49777539,15.625 0,12.1272246 0,7.8125 C0,4.22861177 2.41320752,1.20835835 5.70333404,0.288028257 L5.70307568,1.76165414 C3.20133638,2.63371557 1.40625,5.01347534 1.40625,7.8125 C1.40625,11.3505742 4.27442582,14.21875 7.8125,14.21875 C11.3505742,14.21875 14.21875,11.3505742 14.21875,7.8125 C14.21875,5.01347534 12.4236636,2.63371557 9.92192432,1.76165414 L9.92166596,0.288028257 C13.2117925,1.20835835 15.625,4.22861177 15.625,7.8125 Z" id="形状结合" fill-rule="nonzero"></path>
                                <rect id="矩形" x="7.109375" y="0" width="1.40625" height="7.91015625"></rect>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>