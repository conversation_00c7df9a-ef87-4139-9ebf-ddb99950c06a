<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="主页面" stroke="none" stroke-width="1" fill-rule="evenodd">
        <g id="首页-车站列表-站点详情" transform="translate(-1633, -661)" fill-rule="nonzero">
            <g id="编组-54" transform="translate(241, 636)">
                <g id="编组-64" transform="translate(16, 16)">
                    <g id="按钮-/-1备份-3" transform="translate(1332, 3)">
                        <g id="编组" transform="translate(10, 5)">
                            <g id="图标-/-1" transform="translate(36.1875, 3.1875)">
                                <path d="M1.29013761,7.8125 C1.29013761,11.4146911 4.21030887,14.3348624 7.8125,14.3348624 C9.50892852,14.3348624 11.1014602,13.6852458 12.3055029,12.540554 L12.3418699,12.505715 L13.2378957,13.4339352 C11.7914899,14.8301738 9.86373396,15.625 7.8125,15.625 C5.08677188,15.625 2.68706449,14.2291006 1.2892507,12.1131746 L1.29013761,14.1835499 L0,14.1835499 L0,7.8125 L1.29013761,7.8125 Z M7.8125,0 C10.5382281,0 12.9379355,1.39589943 14.3357493,3.5118254 L14.3348624,1.44145005 L15.625,1.44145005 L15.624,7.748 L15.625,7.8125 L14.3348624,7.8125 C14.3348624,4.21030887 11.4146911,1.29013761 7.8125,1.29013761 C6.03604343,1.29013761 4.37467248,2.00294911 3.15607132,3.24532576 L3.11927988,3.28313544 L2.19106989,2.38709901 C3.65359443,0.8720603 5.66439795,0 7.8125,0 Z" id="形状结合"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>