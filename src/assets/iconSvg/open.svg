<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="主页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="站点信息备份-5" transform="translate(-1008.000000, -385.000000)" fill="#FFFFFF">
            <g id="编组-24备份-2" transform="translate(544.000000, 0.000000)">
                <g id="编组-27" transform="translate(16.000000, 91.000000)">
                    <g id="编组-20" transform="translate(24.000000, 280.000000)">
                        <g id="图标-/-页面-/-qt-新增" transform="translate(424.000000, 14.000000)">
                            <path d="M12.5625,2.3125 C13.1838203,2.3125 13.6875,2.81617966 13.6875,3.4375 L13.6875,12.5625 C13.6875,13.1838203 13.1838203,13.6875 12.5625,13.6875 L3.4375,13.6875 C2.81617966,13.6875 2.3125,13.1838203 2.3125,12.5625 L2.3125,3.4375 C2.3125,2.81617966 2.81617966,2.3125 3.4375,2.3125 L12.5625,2.3125 Z M8.5625,4.40625 L7.4375,4.40625 L7.43725,7.43725 L4.40625,7.4375 L4.40625,8.5625 L7.43725,8.56225 L7.4375,11.59375 L8.5625,11.59375 L8.56225,8.56225 L11.59375,8.5625 L11.59375,7.4375 L8.56225,7.43725 L8.5625,4.40625 Z" id="形状结合"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>