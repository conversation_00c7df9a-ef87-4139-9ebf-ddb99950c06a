// 创建一个新的 store 实例
import apiService from '@/apiService/device'

export default {
  namespaced: true,
  state: {
    dictionaries: {}  // 用于存储不同类型的字典数据
  },
  mutations: {
    SET_DICTIONARY(state, { type, data }) {
      state.dictionaries[type] = data
    }
  },
  actions: {
    async getDictionary({ state, commit }, type) {
      // 如果已经存在该类型的字典数据，直接返回
      if (state.dictionaries[type]) {
        return state.dictionaries[type]
      }
      // 不存在则调用接口获取
      try {
        const res = await apiService.getDictByType({ type })
        commit('SET_DICTIONARY', { type, data: res.data.data })
        return res.data.data
      } catch (error) {
        console.error('获取字典数据失败:', error)
        return []
      }
    }
  },
  getters: {
    getDictionaryByType: (state) => (type) => {
      return state.dictionaries[type] || []
    }
  }
}