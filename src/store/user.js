import service from '@/apiService'
import deviceService from '@/apiService/device'
import homeService from '@/apiService/home'
// import { toRaw } from "vue";
import Cookies from 'js-cookie'
import { cookieDomain } from '@/config/env'
import router, { resetRouter } from '@/router'
import { getCurrentInstance } from 'vue'
import dayjs from 'dayjs'
import initWebSocket from '@/common/websocket'
import appConstant from '@/config/appConstant.js'
import hooks from '@/views/role/components/hooks'
import { setThemeColor, setBusinessType } from '@/common/util.js'
import { ElMessageBox } from 'element-plus'
import i18n from '@/lang'
let menus = {

    ES_ROLE_MANAGER: {
        label: '角色',
        key: 'rolePage',
        sort: 99,
        icon: 'icon-daohang-9beifen',
        roleCode: 'ES_SETTING_MANAGER',
    },

    ES_DEVICE: {
        label: '站点',
        icon: 'icon-daohang-8beifen',
        key: 'device',
        sort: 7,
        roleCode: 'ES_SERVICE_MANAGER',

    },

}

// 创建一个新的 store 实例
const state = () => {
    return {
        userInfo: {
        },
        companyInfo: {},
        menu: [menus.home],
        supplierList: [],
        merchantEditionType: '',
        keepAliveList: [],
        staffRoleCodes: [],
        addServiceInfo: '',
        companyInfoDrawerVisible: false,
        noticeCount: 0,
        reminderCount: 0,
        isLogin: false,
        menuKeys: [],

        //新数据定义
        userInfoData: null,
        configData: null,
        loading401: false,
        hidetip: false
    }
}
const getters = {
    isManager(state) {
        let isManager =
            state.staffRoleCodes.indexOf('ES_SUPPLIER_OPERATOR') >= 0 ||
            state.staffRoleCodes.indexOf('ES_SUPPLIER_MANAGER') >= 0
        return isManager
    },
    isOwner(state) {
        return (
            state.companyInfo.sellType == 'owner' ||
            state.companyInfo.sellType == 'franchiser'
        )
    },
    getCompanyInfo() {
        return JSON.parse(Cookies.get('companyInfo'))
    },
    isLogin(state) {
        return state.isLogin
    },
    loginMerchantId() {
        return localStorage.getItem('loginMerchantId')
    },
    getToken() {
        const token = Cookies.get(
            'accessToken',
            cookieDomain ? { domain: cookieDomain } : {}
        )
        if (token) {
            const { access_token } = JSON.parse(token)
            return access_token
        } else {
            return ''
        }
    },
    getPlatformToken() {
        const token = Cookies.get(
            'platformAccessToken',
            cookieDomain ? { domain: cookieDomain } : {}
        )
        if (token) {
            const { access_token } = JSON.parse(token)
            return access_token
        } else {
            return ''
        }
    },
    //获取可切换企业列表
    showCreateAccount(state) {
        //当前登录为企业版时，如果没有个人版
        //当前登录为个人版时，如果没有企业版
        //可以创建账号
        state.merchantEditionType
        let index = state.supplierList.findIndex(
            (item) => item.merchantEditionType != state.merchantEditionType
        )
        return index == -1
    },
    getAddServiceInfo(state) {
        // 多抛出一个时间戳
        let delayDays = 0,
            isDelay = false,
            isTrialEdition = false,
            delayTimes = 0,
            info = {}
        const addServiceInfo = state.addServiceInfo
        if (addServiceInfo) {
            info = JSON.parse(addServiceInfo)
            if (info.createTime) {
                let serviceStopTime = dayjs(info.serviceStopTime)
                delayDays = serviceStopTime.diff(dayjs(), 'days')
                delayTimes = serviceStopTime.diff(dayjs())
                isDelay = delayTimes <= 0
                isTrialEdition =
                    info.serviceVersion == 'trial_edition' ||
                    info.serviceVersion == 'trial_edition2'
            }
        }
        return {
            delayDays,
            isDelay,
            isTrialEdition,
            ...info,
            delayTimes,
        }
    },
    getCompanyInfoDrawerVisible(state) {
        return state.companyInfoDrawerVisible
    },

    //新getter
    //获取token
    getNewToken() {
        const token = Cookies.get('token')
        return token ? token : ''
    },
    //获取登录信息
    getUserInfoData(state) {
        return state.userInfoData
    },
    getConfigData(state) {
        return state?.configData || localStorage.getItem('configData')
    },
    getLoadingStatus(state) {
        return state.loading401
    },
}
const mutations = {
    checkLogin(state) {
        let result =
            !!Cookies.get(
                'accessToken',
                cookieDomain ? { domain: cookieDomain } : {}
            ) && localStorage.getItem('loginMerchantId')
        state.isLogin = result
    },
    setIsLogin(state, isLogin) {
        state.isLogin = isLogin
    },
    setKeepAliveList(state, routerName) {
        if (routerName && !state.keepAliveList.includes(routerName)) {
            state.keepAliveList.push(routerName)
        }
    },
    removeKeepAliveList(state, routerName) {
        let index = state.keepAliveList.findIndex((e) => e == routerName)
        if (routerName && index >= 0) {
            state.keepAliveList.splice(index, 1)
        }
    },
    setLogout(state) {
        Cookies.remove(
            'accessToken',
            cookieDomain ? { domain: cookieDomain } : {}
        )
        Cookies.remove(
            'accessToken',
            cookieDomain ? { domain: cookieDomain } : {}
        )
        localStorage.removeItem('addServiceInfo')
        state.isLogin = false
        state.companyInfo = {}
        Cookies.set('companyInfo', JSON.stringify({}))
        state.addServiceInfo = ''
    },
    setUserInfo(state, userInfo) {
        // Object.keys(state.userInfo).forEach((key) => {
        //   state.userInfo[key] = userInfo[key];
        // });
        state.userInfo = userInfo
    },
    setMerchantEditionType(state, merchantEditionType) {
        state.merchantEditionType = merchantEditionType
    },
    setCompanyInfo(state, info) {
        state.companyInfo = info
        Cookies.set('companyInfo', JSON.stringify(info))
    },
    setStaffRoleCodes(state, info) {
        state.staffRoleCodes = info
        if (state.staffRoleCodes.length == 0) {
            //没有权限跳转到无权限页
            router.replace('/noright')
        }
    },
    setSupplierList(state, list) {
        state.supplierList = list
    },
    setMenu(state) {

        state.menu = []
        const meun = ['ES_DEVICE', 'ES_ROLE_MANAGER']
        meun.forEach((element) => {
            if (menus[element]) {
                state.menu.push(menus[element])
            }
        })
    },
    setMenuUnreadCount(state, messageCount) {
        for (const key in messageCount) {
            let menu = state.menu.find((m) => m.roleCode == key)
            if (menu) {
                if (menu.children) {
                    menu.children.forEach((c) => {
                        c.unread = messageCount[key][c.key]
                    })
                } else {
                    menu.unread = messageCount[key]
                }
            }
        }
    },
    setAddServiceInfo(state, info) {
        state.addServiceInfo = info
    },
    setCompanyInfoDrawerVisible(state, visible) {
        state.companyInfoDrawerVisible = visible
    },
    // 设置未读通知数量
    setNoticeCount(state, data) {
        state.noticeCount = data
    },
    setReminderCount(state, data) {
        state.reminderCount = data
    },
    clearAllMessageCount(state) {
        state.noticeCount = 0
        state.reminderCount = 0
        state.setMenuUnreadCount = 0
    },

    //新数据定义方法
    setUserInfoData(state, data) {
        state.userInfoData = data ? { ...data } : void 0
    },
    loginOut(state) {
        state.userInfoData = void 0
        // Cookies.remove('token')
        Cookies.remove('token', { path: '/', domain: '.ssnj.com' })
        Cookies.remove('token', { path: '/' })
        Cookies.remove('modifyPasswordFlag', { path: '/', domain: '.ssnj.com' })
        Cookies.remove('hideOnce', { path: '/', domain: '.ssnj.com', })
        localStorage.removeItem('businessType')
    },
    setConfigData(state, data) {
        state.configData = data
        localStorage.setItem('configData', JSON.stringify(data))
    },
    setLoading(state, booean) {
        state.loading401 = booean
    },
    setHidetip(state, data) {
        state.hidetip = data
    }
}
const actions = {
    //平台登录
    async platformLogin({ commit, dispatch, rootState }, param) {
        let result = await service.platformLoginAndRegister(param)
        let { accessToken } = result.data.data
        let accessTokenStr = JSON.stringify(accessToken)
        var expires = new Date(
            new Date().getTime() + accessToken.expires_in * 1000
        )
        Cookies.set('platformAccessToken', accessTokenStr, {
            expires,
            ...(cookieDomain ? { domain: cookieDomain } : {}),
        })
        router.replace('/chooseSupplier')
    },
    async login({ commit, dispatch, rootState }, tenantId) {
        let result = await service.enterMerchantCenter(tenantId)
        await dispatch('setLoginin', result.data.data)
        router.replace('/device')
    },
    async getBasicInfo({ dispatch }) {
        await dispatch('getStaffInfo')
        await dispatch('getCompanyInfo')
        await dispatch('getAddServiceInfo')
    },
    async changeTenant({ commit, dispatch, rootState }, tenantId) {
        let result = await service.changeTenant(tenantId)
        await dispatch('setLoginin', result.data.data)
        await dispatch('getBasicInfo')
        initWebSocket()
        await commit('clearAllMessageCount')
        let routeName = router.currentRoute.value.name
        if (routeName == 'home') {
            router.replace('refresh')
        } else {
            router.replace('/')
        }
        // setTimeout(() => {
        // location.reload();
        // }, 200);
    },
    async merchantLogin(
        { commit, dispatch, rootState },
        { thirdUserIdentify, corpId, proxy }
    ) {
        const loginResult = await service.merchantLoginAndRegister({
            thirdUserIdentify,
            thirdPlatform: 'dingTalk',
            sourcePlatform: 'ES_SUPPLIER_DING_TALK_APP',
            thirdOrgId: corpId,
        })
        const { bizCode } = loginResult.data.data
        if (bizCode == '10047') {
            ElMessageBox.confirm('您不是企业管理员，请联系企业管理员开通', '提示', {
                cancelButtonText: '否',
                confirmButtonText: '是',
            })
            // proxy.$Modal.confirm({
            //     title: '提示',
            //     content: '',
            // })
            router.replace({ name: 'login', query: { showLoginBox: false } })
        } else if (bizCode == '10045') {
            ElMessageBox.confirm('您暂未开通企服助手，请联系管理员在企服助手（pc版），【设置】-【成员管理】模块中添加成员', '提示', {
                cancelButtonText: '否',
                confirmButtonText: '是',
            })
            // proxy.$Modal.confirm({
            //     title: '提示',
            //     content:
            //         '',
            // })
            router.replace({ name: 'login', query: { showLoginBox: false } })
        } else if (bizCode == 0) {
            await dispatch('setLoginin', loginResult.data.data)
            router.replace('/')
        }
    },
    async setLoginin(
        { commit, dispatch, rootState },
        { accessToken, tenantUserInfo, isSave = true, loginMerchantId }
    ) {
        if (accessToken) {
            let accessTokenStr = JSON.stringify(accessToken)
            if (isSave) {
                // expires_in单位为s
                var expires = new Date(
                    new Date().getTime() + accessToken.expires_in * 1000
                )
                Cookies.set('accessToken', accessTokenStr, {
                    expires,
                    ...(cookieDomain ? { domain: cookieDomain } : {}),
                })
            } else {
                Cookies.set(
                    'accessToken',
                    accessTokenStr,
                    cookieDomain ? { domain: cookieDomain } : {}
                )
            }
            commit('setIsLogin', true)
        }
        if (loginMerchantId) {
            //记录当前登录的企业
            localStorage.setItem('loginMerchantId', loginMerchantId)
        }
        let { staffInfo, staffRoleCodes, merchantEditionType } = tenantUserInfo
        //保存用户信息
        commit('setUserInfo', staffInfo)
        commit('setMerchantEditionType', merchantEditionType)
        //先不做动态路由了 刷新有问题
        commit('setMenu', tenantUserInfo)
        commit('setStaffRoleCodes', staffRoleCodes)

        // return staffRoleCodes;
    },
    //获取员工信息
    async getStaffInfo({ commit, dispatch }) {
        let result = await service.getUserInfo()
        //保存用户信息
        await dispatch('setLoginin', { tenantUserInfo: result.data.data })
    },
    async getCompanyInfo({ commit }) {
        let result = await service.queryUserSupplier()
        //保存用户信息
        commit('setCompanyInfo', result.data.data)
        return result.data.data.merchantType
    },

    async getSupplierList({ commit }) {
        let result = await service.queryStaffSupplierList()
        let { data } = result.data
        //保存用户信息
        commit('setSupplierList', data)
    },
    async getAddServiceInfo({ commit }) {
        let result = await homeService.addedServiceFind()
        let { data } = result.data
        let dataStr = JSON.stringify(data || '')
        commit('setAddServiceInfo', dataStr)
        if (dataStr) {
            let info = JSON.parse(dataStr)
            if (info.createTime) {
                let serviceStopTime = dayjs(info.serviceStopTime)
                let delayTimes = serviceStopTime.diff(dayjs())
                if (delayTimes < 0) {
                    //没有权限跳转到续费页
                    router.replace('/buyVersion')
                }
            }
        }
    },

    //新登录
    async newLoginInt({ dispatch }, params) {
        const {
            data: { data, code },
        } = await deviceService.login(params)
        if (code === 0) {
            await dispatch('setLogInfo', data)
            dispatch('getUserColorDetail')
            Cookies.set('modifyPasswordFlag', data.modifyPasswordFlag, {
                expires: 7,
                path: '/',
                domain: 'ssnj.com',
            })
        }
    },
    async setLogInfo({ commit }, { token }) {
        Cookies.set('token', token, {
            expires: 365,
            path: '/',
            domain: 'ssnj.com',
        })
    },
    async setUserInfoData({ commit }) {
        const {
            data: { data, code },
        } = await deviceService.getCurrentUserDetail()
        commit('setUserInfoData', data)
    },
    async getUserInfo({ commit }) {
        return new Promise((resolve, reject) => {
            deviceService.getCurrentUserDetail().then(response => {
                const { data } = response
                commit('setUserInfoData', data.data)
                if (data) {
                    resolve(data.data)
                } else {
                    reject(new Error('获取用户信息失败'))
                }
            }).catch(error => {
                reject(error)
            })
        })
    },
    Logout({ commit, state }) {
        // let that = this
        commit('loginOut',)
        setTimeout(() => {
            router.push('/login')
            // location.reload();
        }, 900);
        // return false
    },
    //获取token下设置的主题并设置
    
    async getUserColorDetail({ commit }) {
        const hostname = window.location.hostname
        const isShangshan =  hostname == 'ems.ssnj.com' ||hostname == 'ems-beta.ssnj.com' ||hostname == 'localhost' ||hostname == 'faw.ssnj.com'

        try {
            const {
                data: { data, code },
            } = await deviceService.getCurrentOrgData()
            if (code === 0) {
                if (data) {
                    // 设置当前角色权限。并前往对应系统
                    setBusinessType(data.businessType)
                    let homeRoute = localStorage.getItem('homeRoute')
                    router.push({ path: homeRoute })
                    const params = {
                        orgLogo: data.orgLogo,
                        loginBanner: data.loginBanner,
                        themeColor: data.themeColor,
                        webPageIcon: data.webPageIcon,
                        webPageTitle:isShangshan?i18n.global.t('上善能及能源云'): data.webPageTitle,
                    }
                    const obj = hooks.setColor(params)
                    document.getElementById('title').innerHTML =
                    isShangshan? i18n.global.t('上善能及能源云'):  obj.webPageTitle
                    document.getElementById('icon-svg').href = obj.webPageIcon
                    document
                        .getElementsByTagName('body')[0]
                        .style.setProperty('--themeColor', obj.themeColor)
                    setTimeout(() => {
                        commit('setConfigData', obj)
                        setThemeColor(obj.themeColor)
                    }, 500)

                } else {
                    // commit('setConfigData', hooks.config)
                }
            }
        } catch (error) {
            // commit('setConfigData', hooks.config)
        }
    },
}
export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
}
