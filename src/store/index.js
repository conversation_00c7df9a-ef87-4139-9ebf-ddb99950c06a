import { createStore } from "vuex";
import user from "./user";
import order from "./order";
import device from "./device";
import breadcrumb from "./breadcrumb";
import strategy from "./strategy";
import dictionary from "./dictionary";
import lang from "./lang";
import theme from "./theme";
import car from "./car";
// import cart from "./cart";
// 创建一个新的 store 实例
const store = createStore({
  modules: {
    user,
    breadcrumb,
    order,
    device,
    strategy,
    dictionary,
    lang,
    theme,
    car
    // cart,
  },
});

export default store;
