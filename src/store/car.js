// 创建一个新的 store 实例
const state = () => {
  return {
    selectSupplierInfoCar: {},
  }
}
const mutations = {
  setSelectSupplierInfoCar(state, data) {
    localStorage.setItem('selectSupplierInfoCar', JSON.stringify(data))
    if (data) {
      state.selectSupplierInfoCar = JSON.parse(localStorage.getItem('selectSupplierInfoCar'))
    }
  }
}
export default {
  namespaced: true,
  state,
  mutations,
};
