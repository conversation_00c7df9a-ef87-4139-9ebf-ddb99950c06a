// store/modules/theme.js
const state = {
  isDark: localStorage.getItem('cn-sys-theme') === 'dark'
}

const mutations = {
  SET_THEME(state, isDark) {
    state.isDark = isDark
    localStorage.setItem('cn-sys-theme', isDark ? 'dark' : 'light')
  }
}

const actions = {
  toggleTheme({ commit, state }) {
    commit('SET_THEME', !state.isDark)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}