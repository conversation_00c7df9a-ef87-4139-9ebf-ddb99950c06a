// store/lang.js
import service from '@/apiService';
// import { saveMessages, getMessage, getAllMessages } from '@/config/indexedDBService';
import { getCachedMessages, cacheMessages } from '@/config/indexedDBService';
const state = () => {
  return {
    currentLanguage: localStorage.getItem('language') || 'zh',
    messages: {},
  }
}
const mutations = {
  setLanguage(state, language) {
    state.currentLanguage = language;
  },
  setMessages(state, messages) {
    state.messages = messages;
  },
}
const actions = {
  async changeLanguage({ commit, dispatch }, language) {
    console.log('store',language);
    localStorage.setItem('language', language);
    commit('setLanguage', language);
    await dispatch('fetchMessages', language);
  },
  async fetchMessages() {
  },
}
const getters = {
  currentLanguage(state) {
    return state.currentLanguage
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
}
