// vue.config.js for less-loader6.0.0
const path = require("path");
const oss = require("./oss.config");
const WebpackAliyunOss = require("webpack-aliyun-oss");
const Timestamp = new Date().getTime();
const isProduction = process.env.NODE_ENV === "production";

function resolve(dir) {
  return path.join(__dirname, dir);
}
const plugins = (module.exports = {
  publicPath: isProduction
    ? "./"
    : "./",
  productionSourceMap: false,
  configureWebpack: (config) => {
    const webpackAliyunOss = [
      new WebpackAliyunOss({
        region: oss.region,
        accessKeyId: oss.accessKeyId,
        accessKeySecret: oss.accessKeySecret,
        bucket: oss.bucket,
        from: ["./dist/**", "!**.map"],
        dist: oss.dist,
        setOssPath: (filePath) => {
          const index = filePath.lastIndexOf("dist");
          const Path = filePath.substring(index + 4, filePath.length);
          return Path.replace(/\\/g, "/");
        },
        // 并发数量
        parallel: 100,
        // 是否覆盖
        overwrite: true,
      }),
    ];
    return {
      resolve: {
        alias: {
          "@": resolve("src"),
        },
      },
      output: {
        // 输出重构  打包编译后的 文件名称  【模块名称.版本号.时间戳】
        filename: `js/[name].${Timestamp}.js`,
        chunkFilename: `js/[name].${Timestamp}.js`,
      },
      plugins: isProduction ? [] : []
      // plugins: isProduction ? webpackAliyunOss : [],
    };
  },
  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          javascriptEnabled: true,
        },
      },
    },
  },
  // 配置转发代理
  devServer: {
    allowedHosts: [".ssnj.com"],
    port: 8080
    // proxy: {
    //   '/': {
    //     target: 'https://gateway-beta.mingwork.com',
    //     ws: true,
    //     pathRewrite: {
    //       '^/': '/'
    //     }
    //   }
    // }
  },
  chainWebpack: (config) => {
    const svgRule = config.module.rule('svg');
    // 清空默认svg规则
    svgRule.uses.clear();
    //针对svg文件添加svg-sprite-loader规则
    svgRule.exclude.add([resolve('node_modules')]);
    svgRule
      .test(/\.svg$/)
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      });
    // config.module.rules.delete('svg'); //重点:删除默认配置中处理svg,
    // config.module.rule('svg').exclude.add(/node_modules/).end()
    // config.module.rule('svg-sprite-loader').test(/\.svg$/)
    // .include.add(resolve('src/assets/iconSvg')) // 处理svg目录
    // .end().use('svg-sprite-loader').loader('svg-sprite-loader')
    // .options({//被加载svg 元素的名字变为svg-[name]
    //   symbolId: 'icon-[name]'
    // })

    config.plugin('define').tap((definitions) => {
      Object.assign(definitions[0], {
        __VUE_OPTIONS_API__: 'true',
        __VUE_PROD_DEVTOOLS__: 'false',
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false'
      })
      return definitions
    })
  },
});

// 'link-color': '#1890ff', // 链接色
// 'success-color': '#52c41a', // 成功色
// 'warning-color': '#faad14', // 警告色
// 'error-color': '#f5222d', // 错误色
// 'font-size-base': '14px', // 主字号
// 'heading-color': 'rgba(0, 0, 0, 0.85)', // 标题色
// 'text-color': 'rgba(0, 0, 0, 0.65)', // 主文本色
// 'text-color-secondary': 'rgba(0, 0, 0, 0.45)', // 次文本色
// 'disabled-color': 'rgba(0, 0, 0, 0.25)', // 失效色
// 'border-radius-base': '4px', // 组件/浮层圆角
// 'border-color-base': '#d9d9d9', // 边框色
// 'box-shadow-base': '0 2px 8px rgba(0, 0, 0, 0.15') // 浮层阴影
